{"train": [{"input": [[0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 3, 0, 0, 3, 4, 4, 4, 4, 4], [2, 2, 2, 3, 0, 0, 3, 4, 4, 4, 4, 4], [2, 2, 2, 3, 0, 0, 3, 4, 4, 4, 4, 4], [2, 2, 2, 3, 0, 0, 3, 4, 4, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 3, 8, 8, 8, 8, 8]]}, {"input": [[0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0]], "output": [[2, 2, 2, 3, 0, 0, 0, 3, 4, 4, 4, 4], [2, 2, 2, 3, 0, 0, 0, 3, 4, 4, 4, 4], [2, 2, 2, 3, 0, 0, 0, 3, 4, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 7, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 1, 3, 0, 0, 0, 3, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 0, 3, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 0, 3, 8, 8, 8, 8], [1, 1, 1, 3, 0, 0, 0, 3, 8, 8, 8, 8]]}, {"input": [[0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0]], "output": [[2, 2, 2, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 4], [2, 2, 2, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [0, 0, 0, 3, 7, 7, 7, 3, 7, 7, 7, 3, 7, 7, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 1, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 8], [1, 1, 1, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 8], [1, 1, 1, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 8], [1, 1, 1, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 8]]}], "test": [{"input": [[0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0]], "output": [[2, 2, 3, 0, 0, 0, 3, 0, 0, 3, 4, 4, 4, 4, 4], [2, 2, 3, 0, 0, 0, 3, 0, 0, 3, 4, 4, 4, 4, 4], [2, 2, 3, 0, 0, 0, 3, 0, 0, 3, 4, 4, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 3, 7, 7, 7, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 3, 7, 7, 7, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 3, 7, 7, 7, 3, 7, 7, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 3, 7, 7, 7, 3, 7, 7, 3, 0, 0, 0, 0, 0], [0, 0, 3, 7, 7, 7, 3, 7, 7, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 3, 0, 0, 0, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 3, 0, 0, 0, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 3, 0, 0, 0, 3, 0, 0, 3, 8, 8, 8, 8, 8], [1, 1, 3, 0, 0, 0, 3, 0, 0, 3, 8, 8, 8, 8, 8]]}]}