import React, { useState, useEffect, Suspense } from 'react';
import { <PERSON>, Typography, Box, Tabs, Tab, Stack, Button, CircularProgress, Alert, Toolt<PERSON>, Chip } from '@mui/material';
import { DataObject as DataObjectIcon, Analytics as AnalyticsIcon, Compare as CompareIcon, Psychology as PsychologyIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import { useDispatch } from 'react-redux';

// Lazy loading des composants de niveau
const Level0Panel = React.lazy(() => import('./components/Level0Panel').then(module => ({ default: module.Level0Panel })));
const Level1Panel = React.lazy(() => import('./components/Level1Panel').then(module => ({ default: module.Level1Panel })));
const Level2Panel = React.lazy(() => import('./components/Level2Panel').then(module => ({ default: module.Level2Panel })));
const Level3Panel = React.lazy(() => import('./components/Level3Panel').then(module => ({ default: module.Level3Panel })));

// Composants partagés
import { TabPanel } from './components/shared/TabPanel';

// Hooks
import { useAnalysisState } from './hooks/useAnalysisState';
import { useAnalyzePuzzleMutation } from '../../store';
import { setAnalysis } from '../../store/slices/puzzleSlice';
import { dataStorage } from '../../services/dataStorage';

const AnalysisPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { currentPuzzle, analysis, showValues, lastAnalysisTime, setLastAnalysisTime, getLevelStatus } = useAnalysisState();
  const [analyzePuzzle, { isLoading }] = useAnalyzePuzzleMutation();
  const [currentTab, setCurrentTab] = useState(0);

  const handleAnalyze = async () => {
    if (!currentPuzzle) return;

    try {
      const result = await analyzePuzzle(currentPuzzle).unwrap();
      if (result) {
        dispatch(setAnalysis(result));
        setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
    }
  };

  // Auto-analyse lors du changement de puzzle
  useEffect(() => {
    const autoAnalyze = async () => {
      if (!currentPuzzle) return;

      const cachedAnalysis = dataStorage.getAnalysis(currentPuzzle.id);

      if (cachedAnalysis) {
        dispatch(setAnalysis(cachedAnalysis.analysis));
        setLastAnalysisTime(new Date(cachedAnalysis.timestamp).toLocaleString('fr-FR'));
      } else {
        try {
          const result = await analyzePuzzle(currentPuzzle).unwrap();
          if (result) {
            dispatch(setAnalysis(result));
            setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
          }
        } catch (error) {
          console.error('Erreur lors de l\'auto-analyse:', error);
        }
      }
    };

    autoAnalyze();
  }, [currentPuzzle?.id, analyzePuzzle, dispatch, setLastAnalysisTime]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  if (!currentPuzzle) {
    return (
      <Paper sx={{ p: 2, height: '100%' }}>
        <Typography variant="h6" gutterBottom>
          Analyse ARC AGI
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Sélectionnez un puzzle pour voir l'analyse en 4 niveaux
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header avec contrôles */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Analyse ARC AGI</Typography>
            <Stack direction="column" alignItems="flex-end" spacing={0.5}>
              <Button
                size="small"
                startIcon={isLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                onClick={handleAnalyze}
                disabled={isLoading}
                variant="contained"
              >
                {isLoading ? 'Analyse...' : 'Analyser'}
              </Button>
              {lastAnalysisTime && (
                <Typography variant="caption" color="text.secondary">
                  Dernière analyse: {lastAnalysisTime}
                </Typography>
              )}
            </Stack>
          </Stack>

          <Alert severity="info" sx={{ py: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              Architecture Pure en 4 Niveaux : Données Brutes → Analyses Dérivées → Comparaisons → Synthèse
            </Typography>
          </Alert>

          {/* Indicateurs de progression par niveau */}
          {analysis && (
            <Stack direction="row" spacing={1} justifyContent="center">
              {[
                { key: 'level0', label: 'Niveau 0', icon: <DataObjectIcon /> },
                { key: 'level1', label: 'Niveau 1', icon: <AnalyticsIcon /> },
                { key: 'level2', label: 'Niveau 2', icon: <CompareIcon /> },
                { key: 'level3', label: 'Niveau 3', icon: <PsychologyIcon /> }
              ].map((level, index) => (
                <Tooltip key={level.key} title={level.label}>
                  <Chip
                    icon={level.icon}
                    label={`N${index}`}
                    size="small"
                    color={getLevelStatus(level.key) === 'complete' ? 'success' : 'default'}
                    variant={getLevelStatus(level.key) === 'complete' ? 'filled' : 'outlined'}
                  />
                </Tooltip>
              ))}
            </Stack>
          )}
        </Stack>
      </Box>

      {/* Onglets pour les différents niveaux */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ minHeight: 48 }}
        >
          <Tab icon={<DataObjectIcon />} label="Niveau 0" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<AnalyticsIcon />} label="Niveau 1" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<CompareIcon />} label="Niveau 2" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<PsychologyIcon />} label="Niveau 3" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
        </Tabs>
      </Box>

      {/* Contenu des onglets avec lazy loading */}
      <Box sx={{ 
        flex: '1 1 0', 
        overflowY: 'auto', 
        overflowX: 'hidden', 
        minHeight: 0,
        '&::-webkit-scrollbar': {
          width: '12px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#f1f1f1',
          borderRadius: '6px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#888',
          borderRadius: '6px',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          background: '#555',
        },
      }}>
        {!analysis ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom color="text.secondary">
              Analyse ARC AGI en 4 Niveaux
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Cliquez sur "Analyser" pour obtenir une analyse complète selon l'architecture pure en niveaux
            </Typography>
            <Stack spacing={1} sx={{ maxWidth: 400, mx: 'auto' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <DataObjectIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 0</strong> : Données brutes (grilles, dimensions, fréquences)
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AnalyticsIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 1</strong> : Analyses dérivées (objets, patterns, séparations)
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CompareIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 2</strong> : Comparaisons input/output
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PsychologyIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 3</strong> : Synthèse par entraînement
                </Typography>
              </Box>
            </Stack>
          </Box>
        ) : (
          <>
            <TabPanel value={currentTab} index={0}>
              <Suspense fallback={<CircularProgress />}>
                <Level0Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={1}>
              <Suspense fallback={<CircularProgress />}>
                <Level1Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={2}>
              <Suspense fallback={<CircularProgress />}>
                <Level2Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={3}>
              <Suspense fallback={<CircularProgress />}>
                <Level3Panel analysis={analysis} />
              </Suspense>
            </TabPanel>
          </>
        )}
      </Box>
    </Paper>
  );
};

export default AnalysisPanel;