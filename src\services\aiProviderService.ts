import { AI_PROVIDERS, AIProvider } from '../types';

export interface AIRequest {
  provider: string;
  model: string;
  prompt: string;
  apiKey?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface AIResponse {
  content: string;
  tokens: number;
  cost: number;
  responseTime: number;
  provider: string;
  model: string;
}

export interface AIError {
  code: string;
  message: string;
  retryable: boolean;
  retryAfter?: number;
}

class AIProviderService {
  private static instance: AIProviderService;
  private apiKeys = new Map<string, string>();
  private rateLimits = new Map<string, { requests: number; resetTime: number }>();

  private constructor() {
    this.loadApiKeysFromStorage();
  }

  static getInstance(): AIProviderService {
    if (!AIProviderService.instance) {
      AIProviderService.instance = new AIProviderService();
    }
    return AIProviderService.instance;
  }

  /**
   * Configure une clé API pour un fournisseur
   */
  setApiKey(providerId: string, apiKey: string): void {
    this.apiKeys.set(providerId, apiKey);
    this.saveApiKeysToStorage();
  }

  /**
   * Récupère une clé API pour un fournisseur
   */
  getApiKey(providerId: string): string | undefined {
    return this.apiKeys.get(providerId);
  }

  /**
   * Vérifie si un fournisseur a une clé API configurée
   */
  hasApiKey(providerId: string): boolean {
    const provider = AI_PROVIDERS[providerId];
    if (!provider) return false;
    if (!provider.requiresKey) return true;
    return this.apiKeys.has(providerId);
  }

  /**
   * Sauvegarde les clés API dans le localStorage (chiffrées)
   */
  private saveApiKeysToStorage(): void {
    try {
      const keysObject = Object.fromEntries(this.apiKeys);
      // En production, il faudrait chiffrer ces clés
      localStorage.setItem('arc_api_keys', JSON.stringify(keysObject));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des clés API:', error);
    }
  }

  /**
   * Charge les clés API depuis le localStorage
   */
  private loadApiKeysFromStorage(): void {
    try {
      const stored = localStorage.getItem('arc_api_keys');
      if (stored) {
        const keysObject = JSON.parse(stored);
        this.apiKeys = new Map(Object.entries(keysObject));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clés API:', error);
    }
  }

  /**
   * Vérifie les limites de taux pour un fournisseur
   */
  private checkRateLimit(providerId: string): boolean {
    const limit = this.rateLimits.get(providerId);
    if (!limit) return true;

    const now = Date.now();
    if (now > limit.resetTime) {
      this.rateLimits.delete(providerId);
      return true;
    }

    return limit.requests > 0;
  }

  /**
   * Met à jour les limites de taux après une requête
   */
  private updateRateLimit(providerId: string, remaining: number, resetTime: number): void {
    this.rateLimits.set(providerId, {
      requests: remaining,
      resetTime: resetTime * 1000, // Convertir en millisecondes
    });
  }

  /**
   * Envoie une requête à un fournisseur IA
   */
  async sendRequest(request: AIRequest): Promise<AIResponse> {
    const provider = AI_PROVIDERS[request.provider];
    if (!provider) {
      throw new Error(`Fournisseur inconnu: ${request.provider}`);
    }

    // Vérifier la clé API
    if (provider.requiresKey && !request.apiKey && !this.hasApiKey(request.provider)) {
      throw new Error(`Clé API requise pour ${provider.name}`);
    }

    // Vérifier les limites de taux
    if (!this.checkRateLimit(request.provider)) {
      const limit = this.rateLimits.get(request.provider);
      throw new Error(`Limite de taux atteinte. Réessayez dans ${Math.ceil((limit!.resetTime - Date.now()) / 1000)} secondes`);
    }

    const startTime = Date.now();
    const apiKey = request.apiKey || this.getApiKey(request.provider);

    try {
      let response: Response;

      switch (request.provider) {
        case 'openai':
          response = await this.sendOpenAIRequest(provider, request, apiKey!);
          break;
        case 'groq':
          response = await this.sendGroqRequest(provider, request, apiKey!);
          break;
        case 'openrouter':
          response = await this.sendOpenRouterRequest(provider, request, apiKey!);
          break;
        case 'perplexity':
          response = await this.sendPerplexityRequest(provider, request, apiKey!);
          break;
        case 'deepseek':
          response = await this.sendDeepSeekRequest(provider, request, apiKey!);
          break;
        case 'ollama':
          response = await this.sendOllamaRequest(provider, request);
          break;
        case 'lmstudio':
          response = await this.sendLMStudioRequest(provider, request);
          break;
        default:
          throw new Error(`Fournisseur non supporté: ${request.provider}`);
      }

      const responseTime = Date.now() - startTime;
      
      // Mettre à jour les limites de taux si disponibles
      const remaining = response.headers.get('x-ratelimit-remaining');
      const resetTime = response.headers.get('x-ratelimit-reset');
      if (remaining && resetTime) {
        this.updateRateLimit(request.provider, parseInt(remaining), parseInt(resetTime));
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Erreur ${response.status}: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      return this.parseResponse(data, request, responseTime);

    } catch (error) {
      // const responseTime = Date.now() - startTime;
      console.error(`Erreur lors de la requête vers ${provider.name}:`, error);
      
      // Analyser le type d'erreur pour déterminer si on peut retry
      const aiError = this.parseError(error as Error);
      throw aiError;
    }
  }

  /**
   * Envoie une requête à OpenAI
   */
  private async sendOpenAIRequest(provider: AIProvider, request: AIRequest, apiKey: string): Promise<Response> {
    return fetch(`${provider.endpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 30000),
    });
  }

  /**
   * Envoie une requête à Groq
   */
  private async sendGroqRequest(provider: AIProvider, request: AIRequest, apiKey: string): Promise<Response> {
    return fetch(`${provider.endpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 30000),
    });
  }

  /**
   * Envoie une requête à OpenRouter
   */
  private async sendOpenRouterRequest(provider: AIProvider, request: AIRequest, apiKey: string): Promise<Response> {
    return fetch(`${provider.endpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'ARC AGI Testing',
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 30000),
    });
  }

  /**
   * Envoie une requête à Perplexity
   */
  private async sendPerplexityRequest(provider: AIProvider, request: AIRequest, apiKey: string): Promise<Response> {
    return fetch(`${provider.endpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 30000),
    });
  }

  /**
   * Envoie une requête à DeepSeek
   */
  private async sendDeepSeekRequest(provider: AIProvider, request: AIRequest, apiKey: string): Promise<Response> {
    return fetch(`${provider.endpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 30000),
    });
  }

  /**
   * Envoie une requête à Ollama (local)
   */
  private async sendOllamaRequest(provider: AIProvider, request: AIRequest): Promise<Response> {
    return fetch(`${provider.endpoint}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: request.model,
        prompt: request.prompt,
        stream: false,
        options: {
          temperature: request.temperature || 0.7,
          num_predict: request.maxTokens || 2000,
        },
      }),
      signal: AbortSignal.timeout(request.timeout || 60000),
    });
  }

  /**
   * Envoie une requête à LM Studio (local)
   */
  private async sendLMStudioRequest(provider: AIProvider, request: AIRequest): Promise<Response> {
    return fetch(`${provider.endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: request.model,
        messages: [{ role: 'user', content: request.prompt }],
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2000,
      }),
      signal: AbortSignal.timeout(request.timeout || 60000),
    });
  }

  /**
   * Parse la réponse d'un fournisseur IA
   */
  private parseResponse(data: any, request: AIRequest, responseTime: number): AIResponse {
    let content = '';
    let tokens = 0;

    if (request.provider === 'ollama') {
      // Format Ollama
      content = data.response || '';
      tokens = data.eval_count || 0;
    } else {
      // Format OpenAI compatible
      content = data.choices?.[0]?.message?.content || '';
      tokens = data.usage?.total_tokens || 0;
    }

    const cost = this.calculateCost(request.provider, request.model, tokens);

    return {
      content: content.trim(),
      tokens,
      cost,
      responseTime,
      provider: request.provider,
      model: request.model,
    };
  }

  /**
   * Calcule le coût d'une requête
   */
  private calculateCost(provider: string, model: string, tokens: number): number {
    // Tarifs approximatifs par 1000 tokens (à mettre à jour selon les tarifs réels)
    const pricing: Record<string, Record<string, number>> = {
      openai: {
        'gpt-4-turbo': 0.01,
        'gpt-3.5-turbo': 0.002,
      },
      groq: {
        'llama2-70b-4096': 0.0008,
        'mixtral-8x7b-32768': 0.0006,
      },
      openrouter: {
        'anthropic/claude-3-sonnet': 0.015,
        'openai/gpt-4-turbo': 0.01,
      },
      perplexity: {
        'llama-3-sonar-large-32k-online': 0.001,
      },
      deepseek: {
        'deepseek-chat': 0.0014,
        'deepseek-coder': 0.0014,
      },
    };

    const providerPricing = pricing[provider];
    if (!providerPricing) return 0;

    const modelPrice = providerPricing[model];
    if (!modelPrice) return 0;

    return (tokens / 1000) * modelPrice;
  }

  /**
   * Parse une erreur pour déterminer si elle est retryable
   */
  private parseError(error: Error): AIError {
    const message = error.message.toLowerCase();

    // Erreurs de rate limit
    if (message.includes('rate limit') || message.includes('429')) {
      return {
        code: 'RATE_LIMIT',
        message: error.message,
        retryable: true,
        retryAfter: 60, // 1 minute par défaut
      };
    }

    // Erreurs de timeout
    if (message.includes('timeout') || message.includes('aborted')) {
      return {
        code: 'TIMEOUT',
        message: 'Timeout de la requête',
        retryable: true,
      };
    }

    // Erreurs de réseau
    if (message.includes('network') || message.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Erreur de réseau',
        retryable: true,
      };
    }

    // Erreurs d'authentification
    if (message.includes('401') || message.includes('unauthorized')) {
      return {
        code: 'AUTH_ERROR',
        message: 'Erreur d\'authentification - vérifiez votre clé API',
        retryable: false,
      };
    }

    // Erreurs de quota
    if (message.includes('quota') || message.includes('insufficient')) {
      return {
        code: 'QUOTA_ERROR',
        message: 'Quota dépassé',
        retryable: false,
      };
    }

    // Erreur générique
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      retryable: false,
    };
  }

  /**
   * Teste la connexion avec un fournisseur
   */
  async testConnection(providerId: string, apiKey?: string): Promise<boolean> {
    try {
      const testRequest: AIRequest = {
        provider: providerId,
        model: AI_PROVIDERS[providerId].models[0],
        prompt: 'Test de connexion',
        apiKey,
        maxTokens: 10,
        timeout: 10000,
      };

      await this.sendRequest(testRequest);
      return true;
    } catch (error) {
      console.error(`Test de connexion échoué pour ${providerId}:`, error);
      return false;
    }
  }

  /**
   * Obtient les statistiques d'utilisation
   */
  getUsageStats(): Record<string, { requests: number; tokens: number; cost: number }> {
    // Cette méthode pourrait être étendue pour tracker les statistiques
    return {};
  }

  /**
   * Nettoie les clés API (pour la déconnexion)
   */
  clearApiKeys(): void {
    this.apiKeys.clear();
    localStorage.removeItem('arc_api_keys');
  }
}

export const aiProviderService = AIProviderService.getInstance();
