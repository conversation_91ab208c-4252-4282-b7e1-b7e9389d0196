{"train": [{"input": [[0, 0, 0, 0, 0, 8, 3, 0, 0, 0, 0, 8, 9, 2, 0, 0, 0, 8, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 8, 4, 0, 0, 2, 0, 8, 6, 0, 3, 0, 0, 8, 9, 0, 0, 0, 5], [9, 6, 0, 0, 0, 8, 0, 0, 1, 0, 0, 8, 0, 0, 8, 0, 0, 8, 2, 0, 0, 4, 0], [7, 7, 0, 0, 5, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 1, 8, 0, 0, 2, 9, 0], [0, 0, 0, 3, 0, 8, 0, 0, 0, 0, 0, 8, 0, 0, 2, 0, 0, 8, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 1, 4, 2, 8, 0, 3, 0, 0, 0, 8, 0, 2, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 2, 0, 9, 0, 8, 0, 3, 0, 4, 0, 8, 0, 0, 0, 0, 2, 8, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 8, 0, 0, 5, 0, 0, 8, 0, 0, 0, 2, 2, 8, 0, 0, 0, 8, 3], [0, 0, 6, 0, 0, 8, 9, 1, 0, 7, 0, 8, 0, 2, 0, 2, 2, 8, 0, 0, 0, 7, 0], [0, 5, 0, 0, 9, 8, 0, 0, 0, 4, 0, 8, 0, 0, 0, 2, 0, 8, 8, 0, 0, 5, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 1, 0, 7, 0, 8, 0, 0, 2, 3, 9, 8, 4, 0, 0, 9, 0, 8, 0, 0, 0, 4, 0], [0, 6, 0, 4, 0, 8, 0, 1, 9, 0, 8, 8, 0, 0, 0, 0, 0, 8, 0, 8, 2, 0, 0], [3, 2, 0, 9, 4, 8, 0, 0, 0, 6, 0, 8, 0, 3, 8, 0, 0, 8, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 8, 0, 0, 1, 0, 0, 8, 0, 0, 0, 9, 0], [0, 0, 2, 0, 0, 8, 3, 4, 0, 0, 0, 8, 9, 0, 0, 0, 0, 8, 8, 0, 0, 0, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 4, 0, 8, 2, 9, 0, 6, 0, 8, 0, 0, 0, 2, 0, 8, 0, 0, 0, 3, 0], [0, 6, 1, 0, 0, 8, 3, 0, 0, 0, 0, 8, 0, 2, 1, 0, 0, 8, 0, 0, 9, 0, 0], [0, 0, 0, 5, 5, 8, 0, 0, 0, 2, 5, 8, 0, 0, 0, 0, 1, 8, 5, 0, 3, 0, 6], [0, 0, 0, 9, 0, 8, 1, 0, 0, 8, 0, 8, 2, 0, 7, 0, 0, 8, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 8, 6, 0, 0, 8, 0, 8, 8, 0, 0, 0, 2, 8, 0, 0, 0, 4, 7]], "output": [[0, 2, 0, 0, 0], [0, 0, 0, 0, 2], [0, 0, 0, 2, 2], [0, 2, 0, 2, 2], [0, 0, 0, 2, 0]]}, {"input": [[0, 7, 0, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 7, 6, 0, 3, 0, 2, 6, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 4, 3, 5, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 3, 0, 6, 0, 9, 3, 2, 0, 0, 0, 0, 3, 0, 2, 0, 0, 0], [3, 6, 0, 8, 0, 3, 4, 0, 0, 0, 0, 3, 0, 0, 0, 0, 1, 3, 0, 0, 6, 0, 0], [0, 0, 0, 0, 0, 3, 4, 0, 0, 0, 0, 3, 0, 0, 0, 4, 0, 3, 0, 0, 7, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 0, 0, 8, 0, 3, 0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 8, 0], [0, 0, 6, 0, 3, 3, 0, 0, 6, 0, 0, 3, 4, 0, 0, 0, 0, 3, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 3, 0, 3, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 4, 3, 0, 0, 3, 0, 0, 9, 0, 2, 3, 0, 0, 3, 0, 3, 3, 0, 2, 0, 0, 0], [8, 0, 1, 5, 0, 3, 0, 5, 0, 0, 2, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 3, 0, 2, 0, 4, 0, 3, 0, 5, 0, 1, 0, 3, 0, 0, 0, 0, 9], [0, 0, 0, 9, 0, 3, 0, 6, 0, 1, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 1], [0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 2, 0, 3, 0, 0, 6, 0, 0], [0, 3, 0, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 4, 3, 0, 0, 0, 0, 3], [6, 0, 0, 0, 0, 3, 0, 0, 0, 0, 9, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 3, 0, 7, 0, 0, 7, 3, 0, 0, 0, 0, 0, 3, 0, 0, 9, 0, 5], [0, 0, 0, 0, 0, 3, 0, 0, 0, 7, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 4, 8, 0, 3, 0, 0, 0, 7, 0, 3, 0, 0, 7, 0, 0, 3, 0, 0, 7, 1, 0], [0, 8, 0, 0, 0, 3, 0, 0, 7, 0, 0, 3, 2, 0, 0, 0, 0, 3, 0, 3, 0, 0, 0], [0, 5, 0, 0, 0, 3, 0, 0, 0, 0, 7, 3, 0, 0, 0, 0, 0, 3, 0, 0, 3, 0, 0]], "output": [[0, 7, 0, 0, 7], [0, 0, 0, 7, 0], [0, 0, 0, 7, 0], [0, 0, 7, 0, 0], [0, 0, 0, 0, 7]]}, {"input": [[0, 0, 0, 5, 5, 0, 0, 5, 0, 1, 0, 5, 0, 0, 4, 5, 0, 0, 0, 5, 6, 0, 0], [1, 9, 0, 5, 0, 6, 4, 5, 0, 0, 4, 5, 4, 4, 0, 5, 0, 0, 1, 5, 0, 5, 5], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 4, 0, 5, 0, 4, 0, 5, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 8, 5, 0, 0, 4, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 4, 0], [0, 5, 0, 5, 0, 3, 9, 5, 0, 1, 0, 5, 0, 1, 0, 5, 0, 1, 0, 5, 0, 1, 0], [1, 0, 0, 5, 0, 0, 7, 5, 0, 0, 0, 5, 0, 0, 3, 5, 0, 6, 0, 5, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 8, 0, 4, 5, 0, 9, 0, 5, 0, 7, 5, 5, 0, 0, 0, 5, 0, 0, 0], [0, 0, 4, 5, 0, 0, 0, 5, 0, 0, 8, 5, 0, 0, 6, 5, 0, 6, 0, 5, 4, 0, 6], [0, 1, 0, 5, 2, 0, 0, 5, 7, 0, 0, 5, 0, 2, 0, 5, 0, 7, 0, 5, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 4, 5, 4, 0, 0, 5, 0, 0, 0], [0, 3, 8, 5, 0, 3, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 4, 0, 8], [0, 0, 0, 5, 8, 0, 0, 5, 1, 0, 3, 5, 0, 7, 0, 5, 0, 8, 0, 5, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 7, 0, 5, 0, 0, 0, 5, 0, 2, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 2], [0, 3, 0, 5, 2, 6, 1, 5, 0, 8, 0, 5, 2, 0, 9, 5, 0, 7, 0, 5, 0, 0, 7], [0, 0, 0, 5, 8, 0, 0, 5, 0, 0, 0, 5, 0, 8, 0, 5, 0, 0, 4, 5, 2, 0, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 7, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 3, 0, 5, 0, 0, 8, 5, 0, 0, 0], [0, 0, 1, 5, 0, 4, 0, 5, 3, 0, 3, 5, 0, 0, 0, 5, 3, 0, 0, 5, 0, 3, 0], [0, 0, 2, 5, 0, 0, 3, 5, 4, 0, 0, 5, 0, 8, 0, 5, 0, 0, 0, 5, 8, 0, 0]], "output": [[0, 0, 4], [4, 4, 0], [0, 4, 0]]}], "test": [{"input": [[4, 3, 0, 0, 0, 2, 0, 0, 0, 8, 3, 2, 1, 0, 0, 0, 0, 2, 0, 8, 0, 0, 0, 2, 8, 0, 0, 0, 0], [6, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 7, 4, 0, 2, 0, 7, 4, 0, 4, 2, 0, 9, 0, 5, 0], [0, 0, 0, 0, 0, 2, 0, 9, 1, 0, 5, 2, 0, 6, 6, 0, 0, 2, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 2, 0, 0, 8, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 0, 3, 0, 2, 4, 0, 1, 0, 0], [0, 0, 0, 0, 0, 2, 4, 0, 0, 9, 0, 2, 0, 9, 0, 2, 5, 2, 0, 0, 3, 7, 0, 2, 0, 0, 0, 4, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [6, 0, 0, 0, 0, 2, 6, 0, 5, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 6, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 0, 7, 0, 0, 2, 0, 0, 3, 0, 0, 2, 9, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 7, 0, 0], [7, 0, 8, 3, 0, 2, 0, 0, 4, 4, 6, 2, 0, 0, 9, 7, 7, 2, 2, 0, 9, 0, 0, 2, 0, 0, 6, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 3, 2, 0, 0, 4, 0, 0, 2, 0, 0, 5, 0, 0], [0, 0, 3, 0, 3, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 5, 5, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 6, 4, 0, 2, 0, 0, 8, 0, 8, 2, 0, 0, 0, 0, 0, 2, 0, 0, 8, 0, 6, 2, 0, 0, 0, 0, 0], [0, 3, 0, 0, 7, 2, 0, 0, 0, 0, 0, 2, 1, 0, 0, 1, 6, 2, 0, 0, 0, 0, 0, 2, 0, 4, 0, 0, 3], [0, 0, 0, 0, 0, 2, 8, 0, 0, 0, 0, 2, 0, 0, 1, 1, 0, 2, 0, 9, 0, 0, 0, 2, 0, 2, 0, 8, 0], [0, 0, 5, 0, 0, 2, 0, 0, 8, 8, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 2, 0, 0, 0, 0, 8, 2, 0, 0, 2, 0, 0, 2, 1, 0, 0, 0, 0, 2, 0, 0, 0, 8, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 6, 2, 0, 0, 0, 7, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0], [8, 0, 0, 0, 0, 2, 0, 3, 2, 0, 0, 2, 0, 0, 0, 0, 3, 2, 7, 0, 0, 0, 7, 2, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 2, 4, 6, 0, 6, 1, 2, 0, 8, 2, 0, 8, 2, 0, 0, 0, 8, 0, 2, 0, 5, 0, 6, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 4, 0, 0, 0, 0, 2, 5, 3, 4, 0, 0, 2, 0, 0, 0, 0, 0], [3, 0, 3, 0, 1, 2, 0, 0, 6, 0, 0, 2, 0, 0, 1, 4, 0, 2, 0, 0, 3, 8, 0, 2, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 4, 0, 0, 2, 6, 0, 0, 0, 0, 2, 3, 0, 0, 0, 0, 2, 4, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 0, 7, 0, 0, 2, 4, 0, 3, 0, 8, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 3, 2, 0, 0, 0, 0, 5], [0, 4, 0, 0, 0, 2, 0, 2, 1, 0, 0, 2, 3, 0, 0, 4, 0, 2, 0, 0, 0, 0, 8, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 9, 0, 2, 0, 0, 0, 6, 5, 2, 0, 5, 0, 0, 0, 2, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 6, 0, 6, 0, 2, 0, 0, 0, 1, 9, 2, 7, 0, 5, 7, 3, 2, 0, 0, 1, 0, 0]], "output": [[0, 0, 8, 0, 8], [0, 0, 0, 0, 0], [8, 0, 0, 0, 0], [0, 0, 8, 8, 0], [0, 0, 0, 0, 8]]}]}