{"train": [{"input": [[5, 5, 5, 0, 5, 0, 0, 0, 5, 5], [5, 0, 0, 5, 5, 0, 5, 0, 5, 5], [0, 5, 5, 0, 5, 5, 0, 5, 0, 0], [2, 0, 5, 5, 2, 0, 5, 0, 2, 5], [5, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 0, 2, 5, 5, 5, 2, 0, 5, 0], [5, 5, 0, 0, 0, 5, 5, 5, 5, 5], [0, 5, 0, 5, 5, 0, 5, 0, 5, 5], [0, 5, 5, 0, 5, 0, 5, 0, 5, 5], [5, 5, 0, 0, 5, 5, 5, 5, 5, 5]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 2, 0, 0, 0, 2, 0], [5, 2, 0, 2, 5, 2, 0, 2, 5, 2], [5, 5, 2, 5, 5, 5, 2, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[1, 0, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 1, 0, 1, 1, 0, 1, 0, 1], [1, 1, 0, 0, 1, 1, 1, 1, 1, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 1, 1, 0, 0, 0, 0, 0, 0], [1, 1, 0, 1, 0, 0, 0, 0, 1, 0], [0, 1, 1, 1, 0, 1, 1, 0, 1, 0], [1, 1, 0, 0, 0, 1, 0, 0, 1, 0], [0, 0, 1, 0, 0, 0, 1, 1, 0, 0], [0, 1, 1, 1, 0, 1, 1, 0, 0, 1]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[0, 8, 8, 0, 8, 0, 8, 8, 0, 0], [2, 0, 8, 8, 0, 8, 0, 0, 0, 8], [2, 2, 8, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 8, 0, 0, 0, 0, 0], [8, 8, 2, 2, 0, 0, 0, 8, 8, 0], [8, 8, 8, 2, 2, 8, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 8, 8, 8, 0], [8, 0, 8, 0, 0, 2, 2, 8, 8, 8], [8, 8, 0, 0, 0, 0, 2, 2, 8, 0], [0, 8, 0, 8, 0, 8, 8, 2, 2, 8]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [8, 2, 2, 0, 0, 0, 0, 0, 0, 0], [8, 8, 2, 2, 0, 0, 0, 0, 0, 0], [8, 8, 8, 2, 2, 0, 0, 0, 0, 0], [8, 8, 8, 8, 2, 2, 0, 0, 0, 0], [8, 8, 8, 8, 8, 2, 2, 0, 0, 0], [8, 8, 8, 8, 8, 8, 2, 2, 0, 0], [8, 8, 8, 8, 8, 8, 8, 2, 2, 0]]}], "test": [{"input": [[9, 9, 9, 0, 0, 0, 0, 0, 0, 0], [9, 9, 9, 0, 9, 0, 0, 9, 0, 0], [9, 0, 0, 0, 9, 0, 9, 0, 0, 0], [0, 0, 9, 9, 9, 0, 9, 0, 0, 0], [0, 2, 2, 2, 0, 2, 2, 2, 9, 2], [2, 2, 0, 2, 2, 2, 9, 2, 2, 2], [9, 0, 0, 9, 9, 9, 0, 9, 9, 0], [0, 0, 0, 0, 9, 0, 9, 0, 0, 9], [0, 9, 9, 0, 0, 0, 0, 9, 9, 0], [9, 0, 9, 0, 0, 9, 0, 9, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 2, 2, 2, 0, 2], [2, 2, 9, 2, 2, 2, 9, 2, 2, 2], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9]]}]}