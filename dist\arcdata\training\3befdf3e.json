{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 4, 6, 4, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 0, 0, 0, 0], [0, 0, 4, 6, 6, 6, 4, 0, 0, 0], [0, 0, 4, 6, 4, 6, 4, 0, 0, 0], [0, 0, 4, 6, 6, 6, 4, 0, 0, 0], [0, 0, 0, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 2, 7, 7, 2, 0, 0, 0], [0, 0, 0, 2, 7, 7, 2, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 2, 2, 7, 7, 7, 7, 2, 2, 0], [0, 2, 2, 7, 2, 2, 7, 2, 2, 0], [0, 2, 2, 7, 2, 2, 7, 2, 2, 0], [0, 2, 2, 7, 7, 7, 7, 2, 2, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 3, 1, 1, 3, 0, 0, 0, 0], [0, 0, 3, 1, 1, 3, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [3, 3, 1, 1, 1, 1, 3, 3, 0, 0], [3, 3, 1, 3, 3, 1, 3, 3, 0, 0], [3, 3, 1, 3, 3, 1, 3, 3, 0, 0], [3, 3, 1, 1, 1, 1, 3, 3, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 3, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 3, 3, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 3, 3, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [8, 3, 3, 3, 8, 0, 0, 0, 0, 0, 0, 0], [8, 3, 8, 3, 8, 0, 0, 0, 0, 0, 0, 0], [8, 3, 3, 3, 8, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 8, 8, 3, 3, 3, 3, 8, 8], [0, 0, 0, 0, 8, 8, 3, 8, 8, 3, 8, 8], [0, 0, 0, 0, 8, 8, 3, 8, 8, 3, 8, 8], [0, 0, 0, 0, 8, 8, 3, 3, 3, 3, 8, 8], [0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0]]}]}