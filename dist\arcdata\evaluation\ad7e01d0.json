{"train": [{"input": [[5, 0, 1], [5, 2, 0], [5, 5, 5]], "output": [[5, 0, 1, 0, 0, 0, 0, 0, 0], [5, 2, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 0, 0, 0, 0, 0, 0], [5, 0, 1, 0, 0, 0, 0, 0, 0], [5, 2, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 0, 0, 0, 0, 0, 0], [5, 0, 1, 5, 0, 1, 5, 0, 1], [5, 2, 0, 5, 2, 0, 5, 2, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[2, 5, 1], [0, 5, 0], [2, 5, 1]], "output": [[0, 0, 0, 2, 5, 1, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 2, 5, 1, 0, 0, 0], [0, 0, 0, 2, 5, 1, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 2, 5, 1, 0, 0, 0], [0, 0, 0, 2, 5, 1, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 2, 5, 1, 0, 0, 0]]}, {"input": [[0, 5, 0, 3], [5, 5, 2, 0], [0, 2, 5, 5], [3, 0, 5, 0]], "output": [[0, 0, 0, 0, 0, 5, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 0, 3, 0, 5, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 2, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 5, 5, 0, 2, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 5, 0, 3, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 3, 0, 5, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 2, 0, 5, 5, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5, 5, 0, 2, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 5, 0, 3, 0, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 5, 0, 0, 0, 0, 0]]}, {"input": [[5, 5, 5, 5], [5, 2, 3, 5], [5, 3, 3, 5], [5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 2, 3, 5, 5, 2, 3, 5, 5, 2, 3, 5, 5, 2, 3, 5], [5, 3, 3, 5, 5, 3, 3, 5, 5, 3, 3, 5, 5, 3, 3, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 2, 3, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 3, 5], [5, 3, 3, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 3, 5], [5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 2, 3, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 3, 5], [5, 3, 3, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 3, 5], [5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 2, 3, 5, 5, 2, 3, 5, 5, 2, 3, 5, 5, 2, 3, 5], [5, 3, 3, 5, 5, 3, 3, 5, 5, 3, 3, 5, 5, 3, 3, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}], "test": [{"input": [[1, 0, 5, 0, 1], [0, 2, 2, 2, 0], [5, 0, 5, 0, 5], [0, 2, 2, 2, 0], [1, 0, 5, 0, 1]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0], [5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0], [1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}