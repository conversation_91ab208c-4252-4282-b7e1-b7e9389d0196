{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 1, 1, 0, 0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 6, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 3, 6, 0, 0, 0, 0, 0, 6, 3, 0, 0, 0], [0, 0, 3, 0, 6, 0, 0, 0, 6, 0, 3, 0, 0, 0], [0, 0, 3, 0, 0, 6, 0, 6, 0, 0, 3, 0, 0, 0], [0, 0, 3, 0, 0, 0, 6, 0, 0, 0, 3, 0, 0, 0], [0, 0, 3, 0, 0, 0, 6, 0, 0, 0, 3, 0, 0, 0], [0, 0, 3, 0, 0, 6, 0, 6, 0, 0, 3, 0, 0, 0], [0, 0, 3, 0, 6, 0, 0, 0, 6, 0, 3, 0, 0, 0], [0, 0, 3, 6, 0, 0, 0, 0, 0, 6, 3, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 2, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 2, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 2, 0, 0, 0, 2, 1, 0, 0, 3, 2, 0, 0, 2, 3, 0], [0, 0, 1, 0, 2, 0, 2, 0, 1, 0, 0, 3, 0, 2, 2, 0, 3, 0], [0, 0, 1, 0, 0, 2, 0, 0, 1, 0, 0, 3, 0, 2, 2, 0, 3, 0], [0, 0, 1, 0, 2, 0, 2, 0, 1, 0, 0, 3, 2, 0, 0, 2, 3, 0], [0, 0, 1, 2, 0, 0, 0, 2, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 4, 3, 0, 0, 0, 0, 0, 0, 0, 3, 4, 0], [0, 0, 0, 0, 0, 0, 4, 0, 3, 0, 0, 0, 0, 0, 3, 0, 4, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 3, 3, 3, 3, 3, 0, 0, 4, 0], [0, 0, 0, 0, 0, 0, 4, 0, 3, 0, 0, 0, 0, 0, 3, 0, 4, 0], [0, 0, 0, 0, 0, 0, 4, 3, 0, 0, 0, 0, 0, 0, 0, 3, 4, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 6, 8, 8, 8, 8, 8, 8, 8, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 3, 1, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 4, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 1, 0, 0, 1, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 3, 0, 1, 1, 0, 3, 0, 0, 0, 0, 0, 0, 0, 8, 6, 0, 0, 0, 0, 0, 6, 8, 0], [0, 3, 0, 1, 1, 0, 3, 0, 0, 0, 0, 0, 0, 0, 8, 0, 6, 0, 0, 0, 6, 0, 8, 0], [0, 3, 1, 0, 0, 1, 3, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 6, 0, 6, 0, 0, 8, 0], [0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 6, 0, 0, 0, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 6, 0, 0, 0, 8, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 8, 0, 0, 0, 6, 0, 0, 0, 8, 0], [0, 0, 0, 2, 4, 0, 0, 0, 0, 0, 0, 4, 2, 0, 8, 0, 0, 0, 6, 0, 0, 0, 8, 0], [0, 0, 0, 2, 0, 4, 0, 0, 0, 0, 4, 0, 2, 0, 8, 0, 0, 0, 6, 0, 0, 0, 8, 0], [0, 0, 0, 2, 0, 0, 4, 0, 0, 4, 0, 0, 2, 0, 8, 0, 0, 6, 0, 6, 0, 0, 8, 0], [0, 0, 0, 2, 0, 0, 0, 4, 4, 0, 0, 0, 2, 0, 8, 0, 6, 0, 0, 0, 6, 0, 8, 0], [0, 0, 0, 2, 0, 0, 0, 4, 4, 0, 0, 0, 2, 0, 8, 6, 0, 0, 0, 0, 0, 6, 8, 0], [0, 0, 0, 2, 0, 0, 4, 0, 0, 4, 0, 0, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 2, 0, 4, 0, 0, 0, 0, 4, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 4, 0, 0, 0, 0, 0, 0, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}