import { ARCGrid, ValidationResult } from '../types';

export class SolutionValidator {
  private static instance: SolutionValidator;

  private constructor() {}

  static getInstance(): SolutionValidator {
    if (!SolutionValidator.instance) {
      SolutionValidator.instance = new SolutionValidator();
    }
    return SolutionValidator.instance;
  }

  /**
   * Valide une solution proposée contre la solution attendue
   */
  validateSolution(proposed: ARCGrid, expected: ARCGrid): ValidationResult {
    // Vérifier les dimensions
    if (proposed.width !== expected.width || proposed.height !== expected.height) {
      return {
        isValid: false,
        accuracy: 0,
        feedback: `Dimensions incorrectes. Attendu: ${expected.width}×${expected.height}, Reçu: ${proposed.width}×${proposed.height}`,
        errorGrid: this.createFullErrorGrid(expected.width, expected.height),
      };
    }

    // Comparer cellule par cellule
    const errorGrid: boolean[][] = [];
    let correctCells = 0;
    const totalCells = expected.width * expected.height;

    for (let y = 0; y < expected.height; y++) {
      errorGrid[y] = [];
      for (let x = 0; x < expected.width; x++) {
        const expectedValue = expected.grid[y][x];
        const proposedValue = proposed.grid[y][x];
        const isCorrect = expectedValue === proposedValue;
        
        errorGrid[y][x] = isCorrect;
        if (isCorrect) {
          correctCells++;
        }
      }
    }

    const accuracy = correctCells / totalCells;
    const isValid = accuracy === 1.0;
    const errorCount = totalCells - correctCells;

    let feedback = '';
    if (isValid) {
      feedback = 'Solution parfaite ! Toutes les cellules sont correctes.';
    } else {
      feedback = `${errorCount} cellule(s) incorrecte(s) sur ${totalCells}. Précision: ${(accuracy * 100).toFixed(1)}%`;
      
      // Ajouter des détails sur les erreurs
      const errorDetails = this.analyzeErrors(proposed, expected, errorGrid);
      if (errorDetails.length > 0) {
        feedback += '\n\nErreurs détectées:\n' + errorDetails.join('\n');
      }
    }

    return {
      isValid,
      accuracy,
      errorGrid,
      feedback,
    };
  }

  /**
   * Crée une grille d'erreur complète (toutes les cellules sont fausses)
   */
  private createFullErrorGrid(width: number, height: number): boolean[][] {
    const errorGrid: boolean[][] = [];
    for (let y = 0; y < height; y++) {
      errorGrid[y] = [];
      for (let x = 0; x < width; x++) {
        errorGrid[y][x] = false;
      }
    }
    return errorGrid;
  }

  /**
   * Analyse les erreurs pour fournir des détails utiles
   */
  private analyzeErrors(proposed: ARCGrid, expected: ARCGrid, errorGrid: boolean[][]): string[] {
    const errors: string[] = [];
    const maxErrors = 10; // Limiter le nombre d'erreurs affichées
    let errorCount = 0;

    for (let y = 0; y < expected.height && errorCount < maxErrors; y++) {
      for (let x = 0; x < expected.width && errorCount < maxErrors; x++) {
        if (!errorGrid[y][x]) {
          const expectedValue = expected.grid[y][x];
          const proposedValue = proposed.grid[y][x];
          errors.push(`Position (${x},${y}): attendu ${expectedValue}, reçu ${proposedValue}`);
          errorCount++;
        }
      }
    }

    if (errorCount === maxErrors) {
      const totalErrors = this.countErrors(errorGrid);
      if (totalErrors > maxErrors) {
        errors.push(`... et ${totalErrors - maxErrors} autres erreurs`);
      }
    }

    return errors;
  }

  /**
   * Compte le nombre total d'erreurs dans une grille d'erreur
   */
  private countErrors(errorGrid: boolean[][]): number {
    let count = 0;
    for (const row of errorGrid) {
      for (const cell of row) {
        if (!cell) count++;
      }
    }
    return count;
  }

  /**
   * Parse une solution depuis une réponse textuelle d'IA
   */
  parseSolutionFromText(text: string): ARCGrid | null {
    try {
      // Chercher du JSON dans le texte
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const gridArray = JSON.parse(jsonMatch[0]);
        if (Array.isArray(gridArray) && gridArray.length > 0 && Array.isArray(gridArray[0])) {
          return {
            grid: gridArray,
            width: gridArray[0].length,
            height: gridArray.length,
          };
        }
      }

      // Chercher des patterns de grille dans le texte
      const lines = text.split('\n');
      const gridLines: number[][] = [];

      for (const line of lines) {
        // Chercher des lignes qui ressemblent à des grilles
        const numbers = line.match(/\d+/g);
        if (numbers && numbers.length > 0) {
          const row = numbers.map(n => parseInt(n, 10));
          // Vérifier que tous les nombres sont des couleurs ARC valides (0-9)
          if (row.every(n => n >= 0 && n <= 9)) {
            gridLines.push(row);
          }
        }
      }

      // Vérifier que toutes les lignes ont la même longueur
      if (gridLines.length > 0) {
        const width = gridLines[0].length;
        if (gridLines.every(row => row.length === width)) {
          return {
            grid: gridLines,
            width,
            height: gridLines.length,
          };
        }
      }

      return null;
    } catch (error) {
      console.error('Erreur lors du parsing de la solution:', error);
      return null;
    }
  }

  /**
   * Valide le format d'une grille
   */
  validateGridFormat(grid: any): { isValid: boolean; error?: string } {
    if (!Array.isArray(grid)) {
      return { isValid: false, error: 'La grille doit être un tableau' };
    }

    if (grid.length === 0) {
      return { isValid: false, error: 'La grille ne peut pas être vide' };
    }

    const width = grid[0].length;
    if (width === 0) {
      return { isValid: false, error: 'Les lignes de la grille ne peuvent pas être vides' };
    }

    for (let y = 0; y < grid.length; y++) {
      const row = grid[y];
      
      if (!Array.isArray(row)) {
        return { isValid: false, error: `La ligne ${y} doit être un tableau` };
      }

      if (row.length !== width) {
        return { isValid: false, error: `Toutes les lignes doivent avoir la même largeur. Ligne ${y}: ${row.length}, attendu: ${width}` };
      }

      for (let x = 0; x < row.length; x++) {
        const cell = row[x];
        if (!Number.isInteger(cell) || cell < 0 || cell > 9) {
          return { isValid: false, error: `Valeur invalide à la position (${x},${y}): ${cell}. Les valeurs doivent être des entiers entre 0 et 9` };
        }
      }
    }

    return { isValid: true };
  }

  /**
   * Génère une grille de différences visuelles pour l'affichage
   */
  generateDiffVisualization(proposed: ARCGrid, expected: ARCGrid): string {
    if (proposed.width !== expected.width || proposed.height !== expected.height) {
      return 'Dimensions incompatibles pour la comparaison';
    }

    const symbols = ['⬛', '🟦', '🟥', '🟩', '🟨', '⬜', '🟪', '🟧', '🟦', '🟫'];
    let visualization = 'Comparaison (Proposé vs Attendu):\n\n';

    for (let y = 0; y < expected.height; y++) {
      let proposedRow = '';
      let expectedRow = '';
      let diffRow = '';

      for (let x = 0; x < expected.width; x++) {
        const proposedValue = proposed.grid[y][x];
        const expectedValue = expected.grid[y][x];
        const isCorrect = proposedValue === expectedValue;

        proposedRow += symbols[proposedValue];
        expectedRow += symbols[expectedValue];
        diffRow += isCorrect ? '✅' : '❌';
      }

      visualization += `Proposé:  ${proposedRow}\n`;
      visualization += `Attendu:  ${expectedRow}\n`;
      visualization += `Résultat: ${diffRow}\n\n`;
    }

    return visualization;
  }

  /**
   * Calcule des métriques de similarité avancées
   */
  calculateSimilarityMetrics(proposed: ARCGrid, expected: ARCGrid): {
    pixelAccuracy: number;
    colorAccuracy: number;
    shapeAccuracy: number;
    positionAccuracy: number;
  } {
    if (proposed.width !== expected.width || proposed.height !== expected.height) {
      return {
        pixelAccuracy: 0,
        colorAccuracy: 0,
        shapeAccuracy: 0,
        positionAccuracy: 0,
      };
    }

    const totalCells = proposed.width * proposed.height;
    let correctPixels = 0;
    const proposedColors = new Set<number>();
    const expectedColors = new Set<number>();

    // Calculer la précision pixel par pixel
    for (let y = 0; y < proposed.height; y++) {
      for (let x = 0; x < proposed.width; x++) {
        const proposedValue = proposed.grid[y][x];
        const expectedValue = expected.grid[y][x];
        
        proposedColors.add(proposedValue);
        expectedColors.add(expectedValue);
        
        if (proposedValue === expectedValue) {
          correctPixels++;
        }
      }
    }

    const pixelAccuracy = correctPixels / totalCells;

    // Calculer la précision des couleurs (couleurs utilisées)
    const commonColors = new Set([...proposedColors].filter(c => expectedColors.has(c)));
    const totalUniqueColors = new Set([...proposedColors, ...expectedColors]).size;
    const colorAccuracy = totalUniqueColors > 0 ? commonColors.size / totalUniqueColors : 1;

    // Pour les métriques de forme et position, on utilise des approximations simples
    const shapeAccuracy = pixelAccuracy; // Simplification
    const positionAccuracy = pixelAccuracy; // Simplification

    return {
      pixelAccuracy,
      colorAccuracy,
      shapeAccuracy,
      positionAccuracy,
    };
  }
}

export const solutionValidator = SolutionValidator.getInstance();
