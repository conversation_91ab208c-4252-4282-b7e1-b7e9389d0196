{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 2, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 2, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 2, 1, 0, 1, 8, 2, 1, 0, 1, 8, 2, 1, 0, 1, 8, 2, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 1, 1, 8, 1, 0, 1, 1, 4, 1, 0, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0], [0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0, 1, 8, 8, 1, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 8, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 8, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 1, 3, 2, 0, 2, 1, 3, 2, 0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 1, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 3, 2, 0, 2, 6, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 3, 8, 2, 0, 2, 3, 8, 2, 0, 2, 3, 8, 2, 0, 2, 3, 8, 2, 0], [0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0, 2, 3, 3, 2, 0], [0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 7, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 4, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 3, 8, 8, 0, 8, 4, 8, 8, 0, 8, 3, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 7, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 7, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 7, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 3, 8, 8, 0, 8, 3, 8, 8, 0, 8, 3, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 4, 4, 8, 0, 8, 7, 4, 8, 0, 8, 4, 4, 8, 0], [0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0, 8, 4, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 2, 5, 0, 5, 5, 4, 5, 0, 5, 5, 2, 5, 0, 5, 5, 4, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 6, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 6, 2, 5, 0, 5, 2, 3, 5, 0], [0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 4, 5, 0, 5, 5, 4, 5, 0, 5, 5, 4, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0, 5, 2, 3, 5, 0, 5, 2, 2, 5, 0, 5, 2, 2, 5, 0], [0, 5, 5, 8, 5, 0, 5, 5, 8, 5, 0, 5, 5, 8, 5, 0, 5, 5, 8, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 6, 2, 5, 0, 5, 6, 2, 5, 0, 5, 6, 3, 5, 0, 5, 6, 3, 5, 0, 5, 2, 3, 5, 0], [0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0, 5, 5, 2, 5, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}