{"train": [{"input": [[0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 3, 1, 1, 3, 2, 0, 0, 0, 0, 0], [2, 2, 1, 0, 0, 1, 2, 2, 0, 0, 0, 0], [2, 2, 1, 0, 0, 1, 2, 2, 0, 0, 0, 0], [0, 2, 3, 1, 1, 3, 2, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 2], [0, 0, 2, 2], [0, 2, 3, 1], [2, 2, 1, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 5, 5, 2, 2, 5, 5, 0, 0, 0], [0, 0, 0, 5, 3, 3, 3, 3, 5, 0, 0, 0], [0, 0, 2, 2, 3, 1, 1, 3, 2, 2, 0, 0], [0, 0, 2, 2, 3, 1, 1, 3, 2, 2, 0, 0], [0, 0, 0, 5, 3, 3, 3, 3, 5, 0, 0, 0], [0, 0, 0, 5, 5, 2, 2, 5, 5, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 2], [0, 5, 5, 2], [0, 5, 3, 3], [2, 2, 3, 1]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 7, 0, 0, 7, 7, 0], [0, 0, 0, 0, 7, 2, 2, 3, 3, 2, 2, 7], [0, 0, 0, 0, 7, 2, 8, 8, 8, 8, 2, 7], [0, 0, 0, 0, 0, 3, 8, 0, 0, 8, 3, 0], [0, 0, 0, 0, 0, 3, 8, 0, 0, 8, 3, 0], [0, 0, 0, 0, 7, 2, 8, 8, 8, 8, 2, 7], [0, 0, 0, 0, 7, 2, 2, 3, 3, 2, 2, 7], [0, 0, 0, 0, 0, 7, 7, 0, 0, 7, 7, 0]], "output": [[0, 7, 7, 0], [7, 2, 2, 3], [7, 2, 8, 8], [0, 3, 8, 0]]}], "test": [{"input": [[0, 0, 1, 0, 0, 5, 5, 0, 0, 1, 0, 0], [0, 0, 0, 5, 3, 8, 8, 3, 5, 0, 0, 0], [0, 0, 0, 3, 2, 8, 8, 2, 3, 0, 0, 0], [0, 0, 5, 8, 8, 6, 6, 8, 8, 5, 0, 0], [0, 0, 5, 8, 8, 6, 6, 8, 8, 5, 0, 0], [0, 0, 0, 3, 2, 8, 8, 2, 3, 0, 0, 0], [0, 0, 0, 5, 3, 8, 8, 3, 5, 0, 0, 0], [0, 0, 1, 0, 0, 5, 5, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[1, 0, 0, 5], [0, 5, 3, 8], [0, 3, 2, 8], [5, 8, 8, 6]]}]}