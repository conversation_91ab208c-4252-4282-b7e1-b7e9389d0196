{"train": [{"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 2, 2, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 2, 2, 8, 8], [1, 1, 2, 2, 1, 1, 1, 8, 8, 2, 2, 8, 8], [1, 1, 2, 2, 1, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 2, 2, 2, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 2, 2, 2, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 2, 2, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 2, 2, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8]], "output": [[1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 2, 2, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 2, 2, 2, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 2, 2, 8, 8, 1, 1, 1, 1, 2, 2, 1, 1], [8, 8, 2, 2, 8, 8, 1, 1, 1, 1, 2, 2, 1, 1], [8, 8, 2, 2, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 2, 2, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 2, 2, 2, 1, 1, 1], [8, 2, 2, 8, 8, 8, 1, 1, 2, 2, 2, 1, 1, 1], [8, 2, 2, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 2, 2, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 2, 2], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 2, 2], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 2, 2, 2], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 2, 2, 2], [2, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 2, 2], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8], [8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2]]}], "test": [{"input": [[1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 8, 2, 8, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [1, 2, 2, 1, 1, 1, 8, 8, 2, 2, 8], [1, 2, 2, 1, 1, 1, 8, 8, 2, 2, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [2, 2, 2, 2, 1, 1, 8, 8, 8, 8, 8], [2, 2, 2, 2, 1, 1, 8, 8, 2, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 2, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8]], "output": [[1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 1, 2, 2, 2, 2, 8, 8, 8], [1, 1, 1, 1, 2, 2, 2, 2, 8, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 2, 2, 2, 2, 8, 8, 8, 8, 8], [1, 1, 2, 2, 2, 2, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 2, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 2, 2, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 2, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 2, 2, 8, 8, 8], [8, 8, 8, 8, 2, 2, 8, 8, 8], [8, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 2, 2], [1, 1, 1, 1, 1, 1, 1, 1, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 8, 8, 8, 8, 8, 8, 8], [2, 2, 8, 8, 8, 8, 8, 8, 8], [2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8]]}]}