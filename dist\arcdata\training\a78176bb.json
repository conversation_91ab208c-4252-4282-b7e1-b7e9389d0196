{"train": [{"input": [[7, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 7, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 7, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7]], "output": [[7, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 7, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 7, 0, 0, 0, 7, 0, 0, 0], [0, 0, 0, 7, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 7, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 7, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7]]}, {"input": [[0, 0, 0, 0, 0, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 9, 0, 0, 0], [0, 0, 0, 0, 0, 5, 5, 9, 0, 0], [0, 0, 0, 0, 0, 5, 5, 5, 9, 0], [0, 0, 0, 0, 0, 5, 5, 5, 5, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 9, 0, 0, 0, 0], [9, 0, 0, 0, 0, 0, 9, 0, 0, 0], [0, 9, 0, 0, 0, 0, 0, 9, 0, 0], [0, 0, 9, 0, 0, 0, 0, 0, 9, 0], [0, 0, 0, 9, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 9, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 9, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 9, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 9, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 2, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 2, 0, 0, 0, 0, 0], [0, 0, 0, 5, 5, 2, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 0]], "output": [[0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 2, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 2, 0], [2, 0, 0, 0, 0, 2, 0, 0, 0, 2], [0, 2, 0, 0, 0, 0, 2, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 2, 0]]}], "test": [{"input": [[0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 1, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 1, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 1, 0, 0, 0], [0, 0, 0, 0, 5, 5, 5, 1, 0, 0], [0, 0, 0, 0, 5, 5, 5, 5, 1, 0], [0, 0, 0, 0, 5, 5, 5, 5, 5, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 1, 0, 0, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 1, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0, 1, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 1], [0, 0, 0, 1, 0, 0, 0, 0, 0, 0]]}]}