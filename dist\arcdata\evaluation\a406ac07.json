{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [9, 9, 8, 8, 7, 7, 6, 6, 5, 5]], "output": [[9, 9, 0, 0, 0, 0, 0, 0, 0, 9], [9, 9, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 8, 8, 0, 0, 0, 0, 0, 8], [0, 0, 8, 8, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 7, 7, 0, 0, 0, 7], [0, 0, 0, 0, 7, 7, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 6, 6, 0, 6], [0, 0, 0, 0, 0, 0, 6, 6, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [9, 9, 8, 8, 7, 7, 6, 6, 5, 5]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [5, 6, 6, 7, 7, 7, 8, 9, 9, 9]], "output": [[5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 6, 6, 0, 0, 0, 0, 0, 0, 6], [0, 6, 6, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 7, 7, 7, 0, 0, 0, 7], [0, 0, 0, 7, 7, 7, 0, 0, 0, 7], [0, 0, 0, 7, 7, 7, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 8, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 9, 9, 9], [0, 0, 0, 0, 0, 0, 0, 9, 9, 9], [5, 6, 6, 7, 7, 7, 8, 9, 9, 9]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [8, 8, 4, 4, 4, 5, 5, 3, 3, 3]], "output": [[8, 8, 0, 0, 0, 0, 0, 0, 0, 8], [8, 8, 0, 0, 0, 0, 0, 0, 0, 8], [8, 8, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 4, 4, 4, 0, 0, 0, 0, 4], [0, 0, 4, 4, 4, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 5, 5, 0, 0, 5], [0, 0, 0, 0, 0, 5, 5, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [8, 8, 4, 4, 4, 5, 5, 3, 3, 3]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [3, 3, 4, 6, 6, 6, 9, 9, 7, 7]], "output": [[3, 3, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 4, 0, 0, 0, 0, 0, 0, 4], [0, 0, 4, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 6, 6, 6, 0, 0, 0, 6], [0, 0, 0, 6, 6, 6, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 9, 9, 0, 9], [0, 0, 0, 0, 0, 0, 0, 0, 7, 7], [3, 3, 4, 6, 6, 6, 9, 9, 7, 7]]}]}