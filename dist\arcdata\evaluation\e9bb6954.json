{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0], [0, 0, 1, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 7, 7, 0, 2, 0], [0, 5, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 7, 7, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 8, 7, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 9, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 9, 0, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 0, 7, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 0, 7, 0, 0, 8, 0], [0, 0, 1, 0, 0, 2, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 9], [0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 0, 7, 7, 7, 0, 2, 0], [7, 7, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 7, 7, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 2, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 7, 0, 0, 7, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 9, 0, 0, 6, 0, 0, 0, 0, 0], [7, 0, 0, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 9], [0, 0, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0], [9, 7, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0], [0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0]], "output": [[0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 9, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 2, 0, 3, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 9, 0, 4, 6, 0, 0, 0, 0, 0], [7, 0, 0, 0, 3, 1, 4, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0], [9, 7, 0, 0, 0, 0, 4, 1, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 6, 0, 0, 0], [0, 0, 0, 7, 0, 0, 4, 0, 0, 0, 1, 0, 0], [0, 0, 0, 8, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 8, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 8, 7, 0, 0, 0, 0, 0, 0], [6, 8, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 0, 1, 0, 0, 0, 0], [0, 0, 2, 6, 5, 0, 3, 0, 0, 0, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 2, 2, 2, 0], [0, 0, 0, 3, 3, 3, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0], [0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 3, 0, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 1], [0, 0, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 6, 7, 0, 0, 0, 0, 0, 0, 0, 8]], "output": [[0, 0, 0, 0, 3, 0, 8, 7, 0, 0, 0, 2, 0, 0], [6, 8, 0, 0, 3, 0, 0, 0, 0, 0, 8, 2, 0, 0], [0, 0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 3, 0, 0, 6, 0, 1, 0, 2, 0, 0], [0, 0, 2, 6, 3, 0, 3, 0, 0, 0, 2, 2, 2, 0], [2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 3, 0, 0, 7, 0, 0, 2, 2, 2, 0], [0, 0, 0, 3, 3, 3, 0, 0, 0, 8, 0, 2, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3], [0, 3, 0, 3, 3, 3, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 2, 3, 0], [0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 2, 0, 0], [5, 0, 0, 0, 3, 0, 3, 0, 4, 0, 0, 2, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 6, 2, 0, 1], [0, 0, 0, 8, 3, 8, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 8, 0, 3, 7, 0, 0, 0, 0, 0, 2, 0, 8]]}, {"input": [[0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 8, 8, 8, 1, 3, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 0, 8, 8, 8, 0, 0, 0, 0], [1, 0, 0, 7, 0, 0, 0, 7, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 5, 0, 0, 0, 8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0], [0, 0, 4, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [1, 3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 4, 0, 0, 0, 8, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 4, 0, 0, 0, 0]], "output": [[0, 0, 0, 1, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 3, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 3, 0], [0, 0, 2, 0, 6, 0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 3, 0], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8], [0, 0, 4, 0, 6, 0, 0, 0, 0, 4, 0, 8, 8, 8, 0, 0, 3, 0], [1, 0, 0, 7, 6, 0, 0, 7, 2, 0, 0, 0, 8, 0, 0, 0, 3, 0], [8, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 3, 3, 0], [0, 5, 0, 0, 6, 8, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 3, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 1, 0, 3, 0], [0, 0, 0, 6, 6, 6, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 3, 0], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 6, 6, 6, 0, 6], [0, 0, 0, 6, 6, 6, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 3, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 3, 0, 3, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 3, 0], [0, 0, 9, 0, 6, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 3, 3, 3], [3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3], [0, 0, 0, 0, 6, 4, 0, 0, 0, 8, 0, 0, 8, 0, 0, 3, 3, 3], [0, 0, 0, 0, 6, 0, 0, 0, 6, 0, 0, 0, 8, 4, 0, 0, 3, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 2, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 7, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0], [0, 5, 0, 0, 0, 7, 4, 4, 4, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 7, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 2, 4], [0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 8, 8, 8, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0], [0, 0, 2, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 6, 0, 0, 0, 2, 0], [7, 7, 7, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], [7, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 6, 9, 9, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 2, 0], [3, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 8, 0, 8, 0, 2, 0, 0], [4, 7, 0, 0, 0, 0, 0, 4, 0, 0, 3, 0, 0, 0, 8, 0, 0, 0, 0], [1, 7, 0, 0, 0, 7, 4, 4, 4, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [0, 7, 0, 0, 0, 7, 4, 4, 4, 0, 0, 0, 1, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 6, 0, 0, 4, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 0], [0, 7, 7, 0, 0, 0, 4, 4, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 8, 8, 8, 0, 2, 4], [8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0], [0, 7, 2, 0, 4, 0, 0, 4, 0, 0, 0, 0, 0, 0, 8, 1, 4, 0, 0], [0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 6, 8, 0, 0, 2, 0], [7, 7, 7, 0, 0, 0, 0, 4, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [7, 7, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7, 7, 7, 0, 7, 7, 7, 7], [7, 7, 7, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 7, 6, 6, 9, 9, 0, 4, 4, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 7, 0, 8, 0, 0, 2, 0], [3, 7, 4, 0, 0, 0, 0, 4, 0, 0, 0, 8, 0, 0, 8, 0, 4, 0, 0], [0, 7, 0, 0, 0, 0, 0, 4, 0, 0, 0, 6, 0, 0, 8, 0, 0, 0, 0]]}]}