{"train": [{"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 3, 3, 1, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 3, 1, 2, 2, 2, 2, 2, 2, 2], [2, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 1, 3, 3, 1, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 3, 1, 2, 2, 2, 2, 2, 2, 2], [2, 1, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2], [2, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 1, 3, 3, 1, 2, 2, 2, 2], [2, 2, 2, 2, 1, 3, 1, 1, 2, 2, 2, 2], [2, 2, 2, 2, 3, 3, 3, 1, 2, 2, 2, 2], [2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 4, 4, 4, 1, 4, 2, 2, 2, 2, 2, 2], [2, 4, 4, 1, 4, 4, 2, 2, 2, 2, 2, 2], [2, 4, 1, 4, 1, 1, 2, 2, 2, 2, 2, 2], [2, 4, 4, 1, 4, 1, 2, 2, 2, 2, 2, 2], [2, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[2, 2, 2, 2, 2, 4, 1, 4, 4, 4, 2, 2], [2, 2, 2, 2, 2, 4, 4, 1, 4, 4, 2, 2], [2, 2, 2, 2, 2, 1, 1, 4, 1, 4, 2, 2], [2, 2, 2, 2, 2, 1, 4, 1, 4, 4, 2, 2], [2, 2, 2, 2, 2, 4, 4, 4, 4, 4, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 4, 4, 4, 1, 4, 2, 2, 2, 2, 2, 2], [2, 4, 4, 1, 4, 4, 2, 2, 2, 2, 2, 2], [2, 4, 1, 4, 1, 1, 2, 2, 2, 2, 2, 2], [2, 4, 4, 1, 4, 1, 2, 2, 2, 2, 2, 2], [2, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1], [1, 3, 3, 3, 2, 3, 3, 1, 1, 1, 1, 1], [1, 2, 2, 2, 3, 2, 3, 1, 1, 1, 1, 1], [1, 3, 3, 3, 2, 3, 3, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1], [1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1], [1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1], [1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1], [1, 3, 3, 3, 2, 3, 3, 1, 1, 1, 1, 1], [1, 2, 2, 2, 3, 2, 3, 1, 1, 1, 1, 1], [1, 3, 3, 3, 2, 3, 3, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 3, 3, 3, 3, 3, 1, 1, 1], [1, 1, 1, 3, 3, 2, 3, 3, 3, 1, 1, 1], [1, 1, 1, 3, 2, 3, 2, 2, 2, 1, 1, 1], [1, 1, 1, 3, 3, 2, 3, 3, 3, 1, 1, 1]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 1, 2, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8], [8, 8, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8], [8, 8, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 1, 2, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 2, 1, 8, 8, 8, 8, 8, 8, 8], [8, 1, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 1, 1, 1, 8, 8, 8, 8, 8, 8], [8, 8, 1, 2, 1, 1, 8, 8, 8, 8, 8, 8], [8, 8, 2, 2, 2, 1, 8, 8, 8, 8, 8, 8]]}]}