{"train": [{"input": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]], "output": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]]}, {"input": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]], "output": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]]}, {"input": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]], "output": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]]}], "test": [{"input": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]], "output": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 2]]}]}