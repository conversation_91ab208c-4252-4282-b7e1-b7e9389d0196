{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 0, 0, 3, 3, 3, 3, 0], [8, 0, 0, 0, 0, 8, 0, 0, 3, 0, 0, 3, 0], [8, 0, 0, 0, 0, 8, 0, 0, 3, 0, 0, 3, 0], [8, 0, 0, 2, 2, 2, 2, 2, 3, 3, 3, 3, 0], [8, 0, 0, 2, 0, 8, 0, 0, 0, 0, 2, 0, 0], [8, 8, 8, 2, 8, 8, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [4, 4, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [4, 4, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2], [2, 8, 8, 8, 8, 8, 8, 2], [2, 8, 3, 3, 3, 3, 8, 2], [2, 8, 3, 4, 4, 3, 8, 2], [2, 8, 3, 4, 4, 3, 8, 2], [2, 8, 3, 3, 3, 3, 8, 2], [2, 8, 8, 8, 8, 8, 8, 2], [2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 4, 0, 0, 0, 4, 0, 0, 1, 1, 1, 0, 0, 0], [0, 0, 0, 4, 0, 0, 0, 4, 0, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 4, 0, 0, 0, 4, 0, 0, 1, 1, 1, 0, 0, 0], [0, 0, 0, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0], [0, 8, 0, 0, 0, 3, 0, 8, 0, 0, 0, 0, 0, 3, 0, 0], [0, 8, 0, 0, 0, 3, 0, 8, 0, 0, 0, 0, 0, 3, 0, 0], [0, 8, 0, 0, 0, 3, 0, 8, 0, 0, 0, 0, 0, 3, 0, 0], [0, 8, 8, 8, 8, 3, 8, 8, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 8, 8, 8, 8, 8, 3], [3, 8, 4, 4, 4, 4, 4, 8, 3], [3, 8, 4, 1, 1, 1, 4, 8, 3], [3, 8, 4, 1, 2, 1, 4, 8, 3], [3, 8, 4, 1, 1, 1, 4, 8, 3], [3, 8, 4, 4, 4, 4, 4, 8, 3], [3, 8, 8, 8, 8, 8, 8, 8, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 2, 2, 0, 0], [0, 0, 6, 0, 8, 8, 8, 8, 8, 8, 0, 6, 0, 0, 0, 2, 2, 0, 0], [0, 0, 6, 0, 8, 0, 0, 0, 0, 8, 0, 6, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 8, 3, 3, 3, 3, 8, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 8, 3, 0, 0, 0, 8, 0, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 8, 3, 0, 0, 0, 8, 0, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 8, 8, 8, 8, 8, 8, 0, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 0, 3, 0, 0, 0, 0, 0, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 6, 6, 6, 3, 6, 6, 6, 6, 6, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 3, 3, 3, 3, 3, 3, 3, 3, 6], [6, 3, 8, 8, 8, 8, 8, 8, 3, 6], [6, 3, 8, 4, 4, 4, 4, 8, 3, 6], [6, 3, 8, 4, 2, 2, 4, 8, 3, 6], [6, 3, 8, 4, 2, 2, 4, 8, 3, 6], [6, 3, 8, 4, 4, 4, 4, 8, 3, 6], [6, 3, 8, 8, 8, 8, 8, 8, 3, 6], [6, 3, 3, 3, 3, 3, 3, 3, 3, 6], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6]]}]}