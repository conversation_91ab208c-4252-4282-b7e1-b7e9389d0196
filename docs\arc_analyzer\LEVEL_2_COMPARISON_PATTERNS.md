# 🔄 Niveau 2 - Patterns de Comparaison

## 📋 Vue d'Ensemble

Le **Niveau 2** compare les analyses du **Niveau 1** entre input et output pour détecter les transformations appliquées.

## 🎯 Principe Fondamental

```
Input Analysis (Niveau 1) + Output Analysis (Niveau 1)
                    ↓
            COMPARAISONS FACTUELLES
                    ↓
        Détection des Transformations
```

## 🔍 Comparaisons par Domaine

### **1. Transformations Structurelles**

#### **Séparations et Bordures**
```python
'structural_transformations': {
    'separator_changes': [
        {
            'separator_id': str,            # Référence au séparateur
            'change_type': str,             # 'removed', 'added', 'moved', 'color_changed'
            'input_state': Dict,            # État dans input
            'output_state': Dict or None,   # État dans output (None si supprimé)
            'transformation_details': {
                'position_change': [int, int] or None,  # Déplacement si applicable
                'color_change': [int, int] or None,     # Changement de couleur
                'dimension_change': Dict or None        # Changement de dimensions
            }
        }
    ],
    'border_changes': [
        {
            'border_side': str,             # 'top', 'bottom', 'left', 'right'
            'change_type': str,             # 'removed', 'added', 'color_changed', 'partial_change'
            'input_border': Dict or None,
            'output_border': Dict or None,
            'change_impact': str            # 'full_removal', 'partial_modification', 'complete_addition'
        }
    ],
    'grid_structure_evolution': {
        'input_grid_size': (int, int) or None,      # (lignes, colonnes) de blocs
        'output_grid_size': (int, int) or None,
        'structure_change': str,                    # 'preserved', 'simplified', 'complexified', 'removed'
        'block_dimension_change': [(int, int), (int, int)] or None  # [(input_dims), (output_dims)]
    }
}
```

#### **Transformations de Blocs**
```python
'block_transformations': {
    'block_mapping': [
        {
            'input_block_id': str,
            'output_block_id': str or None,     # None si bloc supprimé
            'transformation_type': str,         # 'preserved', 'modified', 'removed'
            'content_changes': {
                'color_changes': Dict[int, int],    # {old_color: new_color}
                'pattern_change': str,              # 'preserved', 'modified', 'replaced'
                'uniformity_change': str            # 'became_uniform', 'became_diverse', 'unchanged'
            },
            'position_change': {
                'moved': bool,
                'new_position': [int, int] or None
            }
        }
    ],
    'block_operations': [
        {
            'operation_type': str,              # 'merge', 'split', 'duplicate', 'extract'
            'source_blocks': List[str],         # Blocs sources
            'result_blocks': List[str],         # Blocs résultants
            'operation_details': Dict           # Détails spécifiques à l'opération
        }
    ]
}
```

### **2. Transformations d'Objets**

#### **Objets sur Table**
```python
'object_transformations': {
    'object_mapping': [
        {
            'input_object_id': str,
            'output_object_id': str or None,    # None si objet supprimé
            'transformation_detected': str or None,  # 'rot_90', 'rot_180', 'rot_270', 'flip_h', 'flip_v', None
            'position_change': {
                'moved': bool,
                'input_position': [int, int],
                'output_position': [int, int],
                'displacement_vector': [int, int]
            },
            'anchor_interaction': {
                'moved_to_anchor': bool,
                'anchor_position': [int, int] or None,
                'anchor_confirmed': bool        # Confirme si point rouge était bien un ancrage
            },
            'shape_preservation': {
                'shape_preserved': bool,
                'size_preserved': bool,
                'color_preserved': bool
            }
        }
    ],
    'object_operations': [
        {
            'operation_type': str,              # 'duplicate', 'merge', 'split', 'create', 'destroy'
            'source_objects': List[str],
            'result_objects': List[str],
            'operation_context': str            # Contexte de l'opération
        }
    ]
}
```

#### **Confirmation des Ancrages**
```python
'anchor_confirmations': [
    {
        'anchor_position': [int, int],
        'was_anchor': bool,                     # Confirmé comme point d'ancrage
        'objects_anchored': List[str],          # Objets qui s'y sont ancrés
        'anchor_type_confirmed': str            # Type d'ancrage confirmé
    }
]
```

### **3. Exploitation de la Grille Diff**

#### **Analyse des Changements**
```python
'diff_exploitation': {
    'change_analysis': {
        'total_changes': int,
        'change_ratio': float,
        'change_distribution': {
            'clustered_changes': List[Dict],    # Zones de changements groupés
            'scattered_changes': List[Dict],    # Changements isolés
            'linear_changes': List[Dict]        # Changements en ligne/colonne
        }
    },
    'change_patterns': {
        'systematic_additions': [
            {
                'added_color': int,
                'addition_pattern': str,        # 'fill_blocks', 'create_borders', 'add_objects'
                'affected_regions': List[str]   # Régions affectées
            }
        ],
        'systematic_removals': [
            {
                'removed_color': int,
                'removal_pattern': str,         # 'remove_separators', 'clear_background'
                'affected_regions': List[str]
            }
        ],
        'systematic_substitutions': [
            {
                'color_mapping': Dict[int, int], # {old: new}
                'substitution_scope': str,       # 'global', 'blocks_only', 'objects_only'
                'pattern_preservation': bool     # Motifs préservés lors substitution
            }
        ]
    }
}
```

## 🎯 Patterns de Transformation Détectés

### **1. Patterns Structurels**
- **Suppression de séparateurs** : "Séparateurs gris supprimés, blocs fusionnés"
- **Modification de bordures** : "Bordures noires deviennent rouges"
- **Simplification de grille** : "Grille 3×3 devient zone unique"

### **2. Patterns d'Objets**
- **Ancrage confirmé** : "Objets se déplacent vers points rouges"
- **Rotation systématique** : "Tous objets tournés 90° dans sens horaire"
- **Réorganisation spatiale** : "Objets réarrangés selon pattern géométrique"

### **3. Patterns de Couleurs**
- **Substitution globale** : "Couleur 1 → couleur 3 partout"
- **Remplissage conditionnel** : "Blocs vides remplis avec couleur dominante"
- **Harmonisation** : "Tous objets prennent couleur de leur bloc"

## 🔍 Méthodes de Détection

### **Comparaison par Hash**
```python
def detect_object_transformation(input_obj, output_obj):
    input_hash = input_obj['object_hash']
    output_hash = output_obj['object_hash']
    
    # Vérifier correspondance avec variants
    for transform, variant_hash in input_obj['transformation_variants']['variant_hashes'].items():
        if variant_hash == output_hash:
            return transform
    
    return None  # Pas de transformation simple détectée
```

### **Analyse de Position**
```python
def detect_anchor_usage(input_objects, output_objects, anchor_points):
    confirmations = []
    
    for anchor in anchor_points:
        anchor_pos = anchor['position']
        
        # Chercher objets qui se sont déplacés vers cette position
        for out_obj in output_objects:
            if out_obj['table_position']['absolute_position'] == anchor_pos:
                confirmations.append({
                    'anchor_position': anchor_pos,
                    'was_anchor': True,
                    'objects_anchored': [out_obj['object_id']]
                })
    
    return confirmations
```

## 🛡️ Règles de Comparaison

### **Interdictions**
- ❌ **Pas de synthèse multi-exemples** : Comparaison exemple par exemple uniquement
- ❌ **Pas de règles globales** : Pas d'hypothèses sur la règle du puzzle
- ❌ **Pas de nouvelles données brutes** : Utilise uniquement Niveau 1

### **Autorisations**
- ✅ **Comparaisons factuelles** entre input et output
- ✅ **Détection de transformations** basée sur les données Niveau 1
- ✅ **Confirmation d'hypothèses** (ex: points d'ancrage)

---

**Le Niveau 2 transforme les analyses factuelles du Niveau 1 en compréhension des transformations appliquées.**