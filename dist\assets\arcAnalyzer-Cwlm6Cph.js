class B{extractLevel0Data(n,t){const l=o=>{const e={};for(const s of o)for(const c of s)e[c]=(e[c]||0)+1;return e};return{input_grid:{grid_array:n.grid,width:n.width,height:n.height,value_frequencies:l(n.grid)},output_grid:{grid_array:t.grid,width:t.width,height:t.height,value_frequencies:l(t.grid)}}}analyzeGeometricDetection(n){var c;const t=n.length,l=((c=n[0])==null?void 0:c.length)||0,o=[],e=[];for(let i=0;i<t;i++){const _=n[i],a=_[0];_.every(h=>h===a)&&o.push({index:i,color:a,length:l})}for(let i=0;i<l;i++){const _=n.map(h=>h[i]),a=_[0];_.every(h=>h===a)&&e.push({index:i,color:a,length:t})}const s={};return[...o,...e].forEach(i=>{s[i.color]=(s[i.color]||0)+1}),{uniform_rows:o,uniform_columns:e,uniform_main_diagonals:[],uniform_anti_diagonals:[],line_distribution:{rows:o.length,columns:e.length,main_diagonals:0,anti_diagonals:0},color_frequency_in_lines:s}}analyzeStructuralClassification(n,t){var h;const l=n.length,o=((h=n[0])==null?void 0:h.length)||0,e=t.uniform_rows.find(r=>r.index===0),s=t.uniform_rows.find(r=>r.index===l-1),c=t.uniform_columns.find(r=>r.index===0),i=t.uniform_columns.find(r=>r.index===o-1),_={top_border:{exists:!!e,row:(e==null?void 0:e.index)||-1,color:(e==null?void 0:e.color)||-1,complete:!!e},bottom_border:{exists:!!s,row:(s==null?void 0:s.index)||-1,color:(s==null?void 0:s.color)||-1,complete:!!s},left_border:{exists:!!c,column:(c==null?void 0:c.index)||-1,color:(c==null?void 0:c.color)||-1,complete:!!c},right_border:{exists:!!i,column:(i==null?void 0:i.index)||-1,color:(i==null?void 0:i.color)||-1,complete:!!i},border_completeness:e&&s&&c&&i?"complete":e||s||c||i?"partial":"none"},a=[...t.uniform_rows.filter(r=>r.index>0&&r.index<l-1).map(r=>({separator_id:`horizontal_${r.index}`,type:"horizontal",position:r.index,color:r.color,spans_full_dimension:!0,thickness:1,divides_into_blocks:!0,top_block_height:r.index,bottom_block_height:l-r.index-1})),...t.uniform_columns.filter(r=>r.index>0&&r.index<o-1).map(r=>({separator_id:`vertical_${r.index}`,type:"vertical",position:r.index,color:r.color,spans_full_dimension:!0,thickness:1,divides_into_blocks:!0,left_block_width:r.index,right_block_width:o-r.index-1}))];return{borders:_,separators:a,grid_lines:{has_grid_structure:a.length>1,vertical_grid_lines:a.filter(r=>r.type==="vertical").map(r=>r.position),horizontal_grid_lines:a.filter(r=>r.type==="horizontal").map(r=>r.position),creates_regular_blocks:a.length>=2,block_dimensions:[2,2],grid_size:[2,2]},structural_roles:{containment_lines:[..._.top_border.exists?[0]:[],..._.bottom_border.exists?[l-1]:[],..._.left_border.exists?[0]:[],..._.right_border.exists?[o-1]:[]],division_lines:a.map(r=>r.position),decoration_lines:[],functional_line_ratio:(a.length+(_.top_border.exists?1:0)+(_.bottom_border.exists?1:0)+(_.left_border.exists?1:0)+(_.right_border.exists?1:0))/(t.uniform_rows.length+t.uniform_columns.length||1)}}}analyzeBlocks(n,t,l){var m,j;const o=n.length,e=((m=n[0])==null?void 0:m.length)||0,s=[];if(t.separators.length>0){const p=t.separators.filter(u=>u.type==="vertical").map(u=>u.position).sort((u,b)=>u-b),z=t.separators.filter(u=>u.type==="horizontal").map(u=>u.position).sort((u,b)=>u-b),k=[0,...p,e],g=[0,...z,o];for(let u=0;u<g.length-1;u++)for(let b=0;b<k.length-1;b++){const C=g[u],O=g[u+1],x=k[b],w=k[b+1],$=C+(u>0?1:0),M=O-(u<g.length-2?1:0),I=x+(b>0?1:0),S=w-(b<k.length-2?1:0);if($<M&&I<S){const q=this.extractBlock(n,$,I,M,S,u,b);s.push(q)}}}else{const p=this.extractBlock(n,0,0,o,e,0,0);s.push(p)}const c=s.length>1&&s.every(p=>p.dimensions[0]===s[0].dimensions[0]&&p.dimensions[1]===s[0].dimensions[1]),i=c?(j=s[0])==null?void 0:j.dimensions:void 0,_=s.map(p=>p.block_pattern_hash),a=[...new Set(_)],h=[],r=[],y=[];a.forEach(p=>{const z=s.filter(k=>k.block_pattern_hash===p).map(k=>k.block_id);z.length>1?h.push(z):y.push(z[0])});const v=s.map(p=>p.block_color_analysis.probable_background),f=v.filter(p=>p!==void 0)[0],d=v.every(p=>p===f);return{detected_blocks:s,block_uniformity:{all_same_size:c,uniform_dimensions:i,size_variations:[],total_blocks:s.length},block_content_patterns:{uniform_blocks:s.filter(p=>p.block_uniformity.is_uniform).map(p=>p.block_id),patterned_blocks:s.filter(p=>p.block_uniformity.has_internal_pattern).map(p=>p.block_id),diverse_blocks:s.filter(p=>!p.block_uniformity.is_uniform&&!p.block_uniformity.has_internal_pattern).map(p=>p.block_id),background_consistency:{same_background_across_blocks:d,common_background_color:f,background_variations:v.reduce((p,z)=>(z!==void 0&&(p[z.toString()]=(p[z.toString()]||0)+1),p),{})},content_similarity:{identical_blocks:h,similar_blocks:r,unique_blocks:y}}}}extractBlock(n,t,l,o,e,s,c){var O;const i=e-l,_=o-t,a=[];for(let x=t;x<o;x++){const w=[];for(let $=l;$<e;$++)w.push(((O=n[x])==null?void 0:O[$])||0);a.push(w)}const h={};for(const x of a)for(const w of x)h[w]=(h[w]||0)+1;const r=i*_,y=Object.keys(h).map(x=>parseInt(x)),v=Object.entries(h).reduce((x,w)=>h[parseInt(x[0])]>h[parseInt(w[0])]?x:w)[0],f=parseInt(v),d=y.length===1,m=d?y[0]:void 0,j=d?1:(h[f]||0)/r,p=!d&&y.length>2,z=p?y.length>4?"complex":"simple":"none",k=a.every((x,w)=>x.every(($,M)=>{var I;return $===((I=a[_-1-w])==null?void 0:I[M])})),g=a.every(x=>x.every((w,$)=>w===x[i-1-$])),u=i===_&&a.every((x,w)=>x.every(($,M)=>{var I;return $===((I=a[M])==null?void 0:I[w])})),b=i===_&&a.every((x,w)=>x.every(($,M)=>{var I;return $===((I=a[i-1-M])==null?void 0:I[_-1-w])})),C=[k,g,u,b].filter(Boolean).length;return{block_id:`block_${s}_${c}`,bounds:{top:t,left:l,bottom:o,right:e},dimensions:[i,_],area:r,separated_by:[],block_grid_array:a,block_statistics:{value_frequencies:h,width:i,height:_,total_pixels:r},block_color_analysis:{present_colors:y,dominant_color:f,probable_background:f,non_background_colors:y.filter(x=>x!==f),color_diversity:y.length,background_ratio:(h[f]||0)/r},block_uniformity:{is_uniform:d,uniform_color:m,uniformity_ratio:j,has_internal_pattern:p,pattern_complexity:z},block_symmetries:{horizontal:k,vertical:g,diagonal_main:u,diagonal_anti:b,symmetry_count:C},block_pattern_hash:JSON.stringify(a)}}analyzeMosaics(n,t){var z;const l=n.length,o=((z=n[0])==null?void 0:z.length)||0,e=l*o,s=e>400?"very_large":e>200?"large":e>100?"medium":"small",c=e>150,i=Object.keys(t).map(k=>parseInt(k)),_=Object.values(t),h=Math.max(..._)/e,r=-_.reduce((k,g)=>{const u=g/e;return k+(u>0?u*Math.log2(u):0)},0),y=r/Math.log2(i.length||1),v=y>.7&&h<.6,f=[];for(let k of i){const g=[];for(let u=0;u<l;u++)for(let b=0;b<o;b++)n[u][b]===k&&g.push([u,b]);if(g.length>4){const u=Math.min(...g.map(M=>M[0])),b=Math.max(...g.map(M=>M[0])),C=Math.min(...g.map(M=>M[1])),O=Math.max(...g.map(M=>M[1])),x=O-C+1,w=b-u+1,$=x*w;g.length===$&&f.push({zone_id:`uniform_zone_${k}`,color:k,bounds:[u,C,b,O],area:g.length,shape:x===w?"square":"rectangle",position:u===0||b===l-1||C===0||O===o-1?u===0&&C===0||u===0&&O===o-1||b===l-1&&C===0||b===l-1&&O===o-1?"corner":"edge":"center"})}}const d=f.reduce((k,g)=>k+g.area,0)/e,m=s!=="small"&&i.length>=3&&v&&f.length>=2,j=(s==="very_large"?.3:s==="large"?.2:.1)+(i.length>=5?.3:i.length>=3?.2:.1)+y*.3+(f.length>=3?.2:f.length>=2?.1:0),p=[];return s!=="small"&&p.push(`Taille ${s}`),i.length>=3&&p.push(`${i.length} couleurs`),v&&p.push("Couleurs équilibrées"),f.length>=2&&p.push(`${f.length} zones uniformes`),{mosaic_detection:{is_potential_mosaic:m,confidence_score:Math.min(j,1),detection_reasons:p,size_analysis:{grid_dimensions:[o,l],total_pixels:e,size_category:s,exceeds_average:c},color_balance_analysis:{color_entropy:r,balance_score:y,dominant_color_ratio:h,is_well_balanced:v},uniform_zones_detection:{uniform_zones:f,total_uniform_zones:f.length,uniform_coverage_ratio:d}}}}analyzeObjects(n,t){var r,y,v,f;const l=Object.entries(t).reduce((d,m)=>t[parseInt(d[0])]>t[parseInt(m[0])]?d:m)[0],o=parseInt(l),e=t[o],s=n.length*(((r=n[0])==null?void 0:r.length)||0),c=e/s,i=Object.keys(t).map(d=>parseInt(d)).filter(d=>d!==o),_=[],a=new Set;for(let d=0;d<n.length;d++)for(let m=0;m<(((y=n[0])==null?void 0:y.length)||0);m++){const j=`${d},${m}`;if(!a.has(j)&&n[d][m]!==o){const p=this.extractObject(n,d,m,o,a);p.area>0&&_.push(p)}}const h=[];for(let d=0;d<n.length;d++)for(let m=0;m<(((v=n[0])==null?void 0:v.length)||0);m++)n[d][m]===2&&h.push({anchor_id:`anchor_${d}_${m}`,position:[d,m],size:1,shape:"single_pixel",nearby_objects:[],location_type:this.getLocationTypeForPosition(d,m,n.length,((f=n[0])==null?void 0:f.length)||0)});return{table_analysis:{probable_background:o,background_confidence:c,non_background_colors:i,table_coverage:c,noise_pixels:0},detected_objects:_,anchor_analysis:{possible_anchor_points:h},object_statistics:{total_objects:_.length,objects_by_color:_.reduce((d,m)=>(d[m.color]=(d[m.color]||0)+1,d),{}),objects_by_shape:_.reduce((d,m)=>(d[m.shape_classification.basic_shape]=(d[m.shape_classification.basic_shape]||0)+1,d),{}),table_occupancy:1-c}}}extractObject(n,t,l,o,e){var k;const s=n.length,c=((k=n[0])==null?void 0:k.length)||0,i=n[t][l],_=[],a=[[t,l]];for(;a.length>0;){const[g,u]=a.shift(),b=`${g},${u}`;e.has(b)||g<0||g>=s||u<0||u>=c||n[g][u]!==i||(e.add(b),_.push([g,u]),a.push([g-1,u],[g+1,u],[g,u-1],[g,u+1]))}const h=Math.min(..._.map(g=>g[0])),r=Math.max(..._.map(g=>g[0])),y=Math.min(..._.map(g=>g[1])),v=Math.max(..._.map(g=>g[1])),f=v-y+1,d=r-h+1,m=_.length,j=_.reduce((g,u)=>g+u[0],0)/_.length,p=_.reduce((g,u)=>g+u[1],0)/_.length;let z="irregular";return f===1&&d===1?z="dot":f===1||d===1?z="line":f===d&&m===f*d?z="square":m===f*d&&(z="rectangle"),{object_id:`obj_${i}_${z}_${_.length}`,color:i,multi_color:!1,area:m,center:[j,p],bbox:[h,y,r,v],width:f,height:d,aspect_ratio:f/d,shape_classification:{basic_shape:z,shape_confidence:.8,symmetries:{horizontal:!1,vertical:!1,diagonal_main:!1,diagonal_anti:!1}},object_hash:`${i}_${f}x${d}_${m}`,table_position:{absolute_position:[Math.round(j),Math.round(p)],relative_position:this.getRelativePosition(j,p,s,c),distance_to_edges:{top:Math.round(j),bottom:s-Math.round(j)-1,left:Math.round(p),right:c-Math.round(p)-1},touching_edges:[]}}}getLocationTypeForPosition(n,t,l,o){const e=(n===0||n===l-1)&&(t===0||t===o-1),s=n===0||n===l-1||t===0||t===o-1;return e?"corner":s?"edge":"center"}getRelativePosition(n,t,l,o){const e=l/3,s=o/3,c=n<e?"top":n>2*e?"bottom":"middle",i=t<s?"left":t>2*s?"right":"center";return`${c}_${i}`}analyzeColors(n){const t=Object.keys(n).map(o=>parseInt(o)).sort((o,e)=>o-e),l=Object.entries(n).reduce((o,e)=>n[parseInt(o[0])]>n[parseInt(e[0])]?o:e)[0];return{present_colors:t,dominant_color:parseInt(l),background_color:parseInt(l),non_background_colors:t.filter(o=>o!==parseInt(l)),color_count:t.length,color_distribution:n}}analyzeSymmetries(n){var _;const t=n.length,l=((_=n[0])==null?void 0:_.length)||0,o=n.every((a,h)=>a.every((r,y)=>r===n[t-1-h][y])),e=n.every(a=>a.every((h,r)=>h===a[l-1-r])),s=l===t&&n.every((a,h)=>a.every((r,y)=>r===n[y][h])),c=l===t&&n.every((a,h)=>a.every((r,y)=>r===n[l-1-y][t-1-h])),i=[o,e,s,c].filter(Boolean).length;return{horizontal:o,vertical:e,diagonal_main:s,diagonal_anti:c,symmetry_count:i,symmetry_axes:[]}}analyzePatterns(n){var s;const t=new Map,l=n.length,o=((s=n[0])==null?void 0:s.length)||0;for(let c=0;c<l-1;c++)for(let i=0;i<o-1;i++){const _=[[n[c][i],n[c][i+1]],[n[c+1][i],n[c+1][i+1]]],a=JSON.stringify(_);t.set(a,(t.get(a)||0)+1)}const e=Array.from(t.entries()).filter(([c,i])=>i>1).map(([c,i],_)=>({pattern_id:`pattern_${_}`,pattern_size:[2,2],pattern_hash:c,occurrence_count:i,coverage_ratio:i*4/(l*o),pattern_type:"exact"}));return{detected_patterns:e,pattern_statistics:{total_patterns:e.length,pattern_density:e.length/(l*o),pattern_coverage:e.reduce((c,i)=>c+i.coverage_ratio,0)},complexity_analysis:{pattern_complexity:e.length>5?"complex":e.length>2?"medium":"simple",regularity_index:e.length>0?.7:.3,predictability:e.length>0?.8:.2}}}analyzeLevel1Grid(n){var v,f;const{grid_array:t,value_frequencies:l}=n,o=this.analyzeGeometricDetection(t),e=this.analyzeStructuralClassification(t,o),s=this.analyzeBlocks(t,e,l),c=this.analyzeObjects(t,l),i=this.analyzeColors(l),_=this.analyzeSymmetries(t),a=this.analyzePatterns(t),h=this.analyzeMosaics(t,l),r=t.length*(((v=t[0])==null?void 0:v.length)||0),y=Object.entries(l).filter(([d,m])=>parseInt(d)!==0).reduce((d,[m,j])=>d+j,0);return{geometric_detection:o,structural_classification:e,block_analysis:s,objects:c,colors:i,symmetries:_,repeating_patterns:a,mosaics:h,spatial_properties:{object_density:c.object_statistics.total_objects/r,background_ratio:c.table_analysis.background_confidence,filled_ratio:y/r,center_of_mass:[t.length/2,(((f=t[0])==null?void 0:f.length)||0)/2],spatial_distribution:"dispersed"}}}analyzeTransformation(n){const{input_grid:t,output_grid:l}=n,o=t.width===l.width&&t.height===l.height;let e=null,s=null,c=null,i=null;if(o){e=t.grid_array.map((a,h)=>a.map((r,y)=>{const v=l.grid_array[h][y];return r===v?0:v})),s=[];let _=0;for(let a=0;a<e.length;a++)for(let h=0;h<e[a].length;h++)e[a][h]!==0&&(s.push([a,h]),_++);if(c=_/(t.width*t.height),_>0){const a=this.calculateFrequencies(e),h={grid_array:e,width:t.width,height:t.height,value_frequencies:a};i=this.analyzeLevel1Grid(h)}}return{dimension_compatibility:{same_dimensions:o,width_change:l.width-t.width,height_change:l.height-t.height,transformation_type:o?"same_size":"resize"},diff_grid:{exists:o,grid_array:e||void 0,change_ratio:c,change_positions:s,diff_analysis:i}}}calculateFrequencies(n){const t={};for(const l of n)for(const o of l)t[o]=(t[o]||0)+1;return t}async analyzePuzzle(n){try{if(!n.train||n.train.length===0)throw new Error("No training examples available");const t=n.train.map((a,h)=>{const r=this.extractLevel0Data(a.input,a.output),y=this.analyzeLevel1Grid(r.input_grid),v=this.analyzeLevel1Grid(r.output_grid);return{example_id:`train_${h}`,level0Data:r,input_analysis:y,output_analysis:v}}),l=t[0],{level0Data:o,input_analysis:e,output_analysis:s}=l,c=this.analyzeTransformation(o),i=t.map(a=>({example_id:a.example_id,transformation_analysis:this.analyzeTransformation(a.level0Data)}));return{level_0:{training_examples:t.map(a=>({example_id:a.example_id,input_grid:a.level0Data.input_grid,output_grid:a.level0Data.output_grid})),input_grid:o.input_grid,output_grid:o.output_grid},level_1:{training_examples_analysis:t.map(a=>({example_id:a.example_id,input_analysis:a.input_analysis,output_analysis:a.output_analysis})),input_analysis:e,output_analysis:s},level_2:{training_examples_comparisons:i,transformation_analysis:c},level_3:{recurring_patterns:{global_patterns:[],consistency_score:0,pattern_conflicts:[]},global_rules:{rule_hypotheses:[],best_rule_confidence:0,rule_consistency:0},predictions:{test_predictions:[],prediction_confidence:0,alternative_predictions:[]}},grid_info:{input:{width:o.input_grid.width,height:o.input_grid.height,colors:e.colors.present_colors,total_cells:o.input_grid.width*o.input_grid.height},output:{width:o.output_grid.width,height:o.output_grid.height,colors:s.colors.present_colors,total_cells:o.output_grid.width*o.output_grid.height}},objects:{input:e.objects,output:s.objects},patterns:{input:e.repeating_patterns,output:s.repeating_patterns},transformations:{dimension_change:{same_dimensions:c.dimension_compatibility.same_dimensions,width_change:c.dimension_compatibility.width_change,height_change:c.dimension_compatibility.height_change,transformation_type:c.dimension_compatibility.transformation_type||"unknown"},object_changes:[],pattern_changes:[]},symmetries:{input:e.symmetries,output:s.symmetries},complexity:{input:e.repeating_patterns.complexity_analysis,output:s.repeating_patterns.complexity_analysis,transformation_complexity:"medium"},colors:{input:e.colors,output:s.colors},spatial_relations:{input:e.spatial_properties,output:s.spatial_properties},line_uniformity:{input:e.geometric_detection,output:s.geometric_detection},diff_analysis:c.diff_grid.exists?{change_ratio:c.diff_grid.change_ratio||0,change_positions:c.diff_grid.change_positions||[],change_patterns:[]}:null,enhanced_objects:{input:e.objects.detected_objects,output:s.objects.detected_objects},repeating_patterns:{input:e.repeating_patterns.detected_patterns,output:s.repeating_patterns.detected_patterns}}}catch(t){throw console.error("Error analyzing puzzle:",t),t}}}const L=new B;export{B as ARCAnalyzer,L as arcAnalyzer};
