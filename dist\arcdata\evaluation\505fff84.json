{"train": [{"input": [[2, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2], [2, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 2, 2, 2, 2, 0, 2, 2, 0, 2, 2], [2, 0, 1, 2, 2, 2, 0, 0, 8, 2, 0], [0, 0, 2, 0, 0, 2, 0, 2, 0, 0, 0], [1, 2, 2, 0, 0, 2, 8, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 2, 0, 2, 2, 2], [0, 0, 2, 1, 2, 0, 0, 0, 2, 8, 0], [0, 2, 0, 0, 1, 2, 2, 2, 0, 2, 8], [0, 2, 0, 2, 2, 0, 2, 2, 2, 0, 0], [2, 0, 0, 2, 0, 0, 0, 2, 0, 2, 0], [2, 1, 2, 2, 2, 2, 2, 8, 2, 2, 0], [2, 2, 2, 0, 2, 0, 0, 2, 0, 0, 2], [0, 0, 2, 0, 2, 0, 2, 2, 2, 2, 0]], "output": [[2, 2, 2, 0, 0], [2, 2, 0, 0, 2], [2, 0, 0, 0, 2], [2, 2, 2, 0, 2], [2, 2, 2, 2, 2]]}, {"input": [[2, 0, 0, 0, 0, 2, 0, 0, 0, 2, 2], [2, 2, 0, 0, 2, 0, 0, 2, 0, 2, 0], [2, 1, 0, 2, 0, 0, 0, 0, 2, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0]], "output": [[0, 2, 0, 0, 0, 0, 2]]}, {"input": [[2, 2, 0, 0, 2, 2, 0, 2, 2, 0], [1, 0, 2, 0, 8, 0, 2, 0, 0, 0], [2, 2, 0, 0, 0, 2, 0, 0, 0, 2], [2, 0, 2, 0, 0, 1, 2, 0, 0, 8], [2, 2, 0, 0, 2, 2, 0, 2, 2, 0]], "output": [[0, 2, 0], [2, 0, 0]]}, {"input": [[2, 0, 2, 2, 0, 0, 0, 2, 0, 0, 2, 2], [2, 0, 1, 2, 0, 2, 0, 8, 0, 2, 0, 2], [1, 2, 2, 2, 2, 8, 2, 0, 0, 0, 2, 0], [2, 0, 0, 0, 2, 2, 2, 0, 2, 2, 0, 0], [2, 2, 1, 0, 2, 2, 2, 8, 2, 0, 2, 2], [2, 0, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0]], "output": [[2, 0, 2, 0], [2, 2, 2, 2], [0, 2, 2, 2]]}, {"input": [[1, 2, 0, 2, 0, 0, 0, 8, 2, 0, 0, 2], [1, 2, 0, 2, 0, 2, 0, 8, 0, 0, 0, 2], [1, 0, 2, 2, 0, 2, 2, 8, 0, 0, 2, 2], [2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 2, 2], [0, 2, 2, 0, 0, 0, 0, 0, 2, 2, 2, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2], [0, 2, 0, 2, 0, 0, 2, 0, 2, 0, 2, 2], [2, 0, 0, 2, 0, 0, 2, 2, 2, 0, 0, 0]], "output": [[2, 0, 2, 0, 0, 0], [2, 0, 2, 0, 2, 0], [0, 2, 2, 0, 2, 2]]}], "test": [{"input": [[2, 0, 1, 0, 2, 0, 2, 2, 8, 2, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0, 2], [0, 1, 0, 2, 2, 0, 2, 8, 2, 0, 0, 0, 0, 2], [0, 2, 1, 0, 0, 0, 0, 0, 8, 2, 2, 0, 2, 2], [2, 0, 2, 0, 2, 0, 2, 2, 0, 2, 2, 2, 0, 0], [0, 1, 2, 2, 0, 0, 0, 8, 0, 2, 2, 2, 2, 2], [2, 0, 0, 0, 2, 2, 0, 0, 2, 0, 2, 2, 2, 0], [2, 2, 2, 2, 1, 0, 0, 2, 0, 0, 8, 0, 2, 2], [0, 0, 0, 0, 2, 0, 0, 2, 2, 0, 0, 0, 2, 2]], "output": [[0, 2, 0, 2, 2], [0, 2, 2, 0, 2], [0, 0, 0, 0, 0], [2, 2, 0, 0, 0], [0, 0, 2, 0, 0]]}]}