{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 5, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 0, 8, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 8, 0, 0, 8, 2, 0, 0, 0, 0, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 3, 0, 8, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 1, 1, 1, 0, 0, 8, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 5, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 5, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 8, 8, 8, 8, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 8, 8, 8, 8, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 8, 8, 8, 8, 8, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 8, 8, 8, 8, 8, 2, 0, 0, 0, 0, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 2, 8, 8, 8, 8, 8, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 1, 1, 1, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 3, 0, 2, 0, 0, 6, 6, 6, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 6, 0, 3, 0, 0], [0, 0, 2, 2, 2, 0, 3, 0, 0, 0, 2, 0, 0, 6, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 6, 6, 6, 6, 6], [0, 0, 0, 0, 2, 0, 0, 0, 8, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 0, 8, 0], [3, 0, 0, 0, 0, 0, 0, 4, 8, 0, 3, 0, 8, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 8, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 4, 4, 4, 4, 4, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 3, 3, 3, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 3, 3, 3, 3, 3, 3, 3, 2, 0, 0, 6, 6, 6, 0, 0], [0, 0, 2, 3, 3, 3, 3, 3, 3, 3, 2, 0, 0, 6, 0, 0, 0, 0], [0, 0, 2, 2, 2, 3, 3, 3, 3, 3, 2, 0, 0, 6, 0, 0, 0, 0], [0, 0, 0, 0, 2, 3, 3, 3, 3, 3, 2, 0, 0, 6, 6, 6, 6, 6], [0, 0, 0, 0, 2, 3, 3, 3, 3, 3, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 3, 3, 3, 3, 3, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 8, 8, 8, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 8, 8, 8, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 8, 8, 8, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 8, 8, 8, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 8, 8, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 2, 0, 0, 0, 3, 0, 0, 0], [0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 3, 0, 0, 0, 8, 0, 0, 8, 0, 3, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 3, 3, 0, 0], [0, 0, 0, 3, 0, 6, 0, 0, 2, 0, 0, 3, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 6, 0, 0, 0, 3, 0, 0], [0, 0, 3, 0, 0, 6, 0, 3, 3, 3, 3, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 6, 6, 6, 6, 6, 6, 3, 0, 0, 0], [0, 3, 3, 3, 6, 6, 6, 6, 6, 6, 3, 0, 0, 0], [0, 3, 6, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0, 0], [0, 3, 6, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0, 0], [0, 3, 3, 3, 6, 6, 6, 6, 6, 6, 3, 3, 0, 0], [0, 0, 0, 3, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0], [0, 0, 0, 3, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0], [0, 0, 3, 3, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0], [0, 0, 3, 6, 6, 6, 6, 6, 6, 6, 6, 3, 0, 0], [0, 0, 3, 6, 6, 6, 6, 3, 3, 3, 3, 3, 0, 0], [0, 0, 3, 6, 6, 6, 6, 3, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 1, 0, 0, 3, 3, 3, 3, 3, 3, 0, 1], [0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 2, 0, 6, 0, 0, 8, 0, 0, 2, 0, 0, 0, 0, 3, 0, 0, 6, 0, 3, 0, 0], [0, 0, 0, 2, 0, 0, 1, 0, 0, 0, 0, 2, 0, 0, 3, 3, 3, 0, 0, 0, 0, 3, 0, 0], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 3, 0, 0, 0, 0, 8, 0, 3, 0, 0], [0, 2, 0, 0, 1, 0, 6, 0, 0, 0, 0, 2, 0, 0, 3, 8, 0, 0, 0, 0, 0, 3, 0, 0], [0, 2, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 0, 0, 3, 0, 0, 0, 8, 0, 0, 3, 0, 0], [0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 1, 0, 3, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 3, 3, 3, 3, 0, 0, 0, 3, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 8, 0, 3, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], [0, 0, 8, 0, 4, 0, 0, 0, 0, 3, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 4, 0, 0, 0, 0, 8, 0, 0, 0, 7, 7, 7, 7, 7, 7, 7, 0, 0], [0, 0, 8, 0, 3, 0, 0, 0, 0, 4, 0, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 7, 0, 2, 0, 1, 0, 7, 0, 0], [0, 0, 8, 0, 0, 8, 8, 8, 8, 0, 0, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 0, 0, 0, 7, 0, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0, 0], [0, 0, 0, 2, 1, 1, 1, 1, 1, 2, 2, 2, 0, 0, 0, 0, 3, 8, 8, 8, 8, 3, 0, 0], [0, 0, 0, 2, 1, 1, 1, 1, 1, 1, 1, 2, 0, 0, 0, 0, 3, 8, 8, 8, 8, 3, 0, 0], [0, 0, 0, 2, 1, 1, 1, 1, 1, 1, 1, 2, 0, 0, 3, 3, 3, 8, 8, 8, 8, 3, 0, 0], [0, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 2, 0, 0, 3, 8, 8, 8, 8, 8, 8, 3, 0, 0], [0, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 0, 0, 3, 8, 8, 8, 8, 8, 8, 3, 0, 0], [0, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 0, 0, 3, 8, 8, 8, 8, 8, 8, 3, 0, 0], [0, 2, 1, 1, 1, 1, 1, 2, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 8, 8, 8, 3, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 8, 8, 8, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 8, 8, 8, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 4, 4, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 4, 4, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 4, 4, 4, 4, 4, 4, 4, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 4, 4, 4, 4, 4, 4, 4, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 4, 4, 4, 4, 4, 4, 4, 4, 8, 0, 0, 0, 7, 7, 7, 7, 7, 7, 7, 0, 0], [0, 0, 8, 4, 4, 4, 4, 4, 4, 4, 4, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 8, 4, 4, 4, 4, 4, 4, 4, 4, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 8, 4, 4, 8, 8, 8, 8, 4, 4, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}