import React from 'react';
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box } from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { AnalysisAccordionProps } from '../../types/AnalysisPanelTypes';

export const AnalysisAccordion: React.FC<AnalysisAccordionProps> = ({
  title,
  icon,
  expanded,
  onChange,
  children
}) => {
  return (
    <Accordion expanded={expanded} onChange={onChange}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle1">
          {icon}
          <Box component="span" sx={{ ml: 1 }}>
            {title}
          </Box>
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        {children}
      </AccordionDetails>
    </Accordion>
  );
};