{"train": [{"input": [[2, 2, 1, 0, 2, 2, 2, 0, 1, 2, 1], [1, 2, 2, 0, 2, 2, 2, 0, 1, 1, 2], [2, 2, 2, 0, 1, 2, 2, 0, 2, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 1, 0, 2, 1, 2, 0, 2, 2, 2], [1, 2, 2, 0, 1, 2, 1, 0, 2, 2, 2], [2, 1, 2, 0, 2, 2, 1, 0, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 1, 1, 1, 0, 2, 1, 1], [1, 2, 1, 0, 1, 2, 1, 0, 1, 1, 2]], "output": [[2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 2, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 1, 1, 2, 0, 1, 2, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 1, 0, 2, 1, 2, 0, 1, 2, 1], [1, 2, 2, 0, 1, 2, 1, 0, 1, 1, 2], [2, 1, 2, 0, 2, 2, 1, 0, 2, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 1], [2, 2, 2, 0, 2, 2, 2, 0, 1, 2, 2], [2, 2, 2, 0, 1, 2, 2, 0, 2, 2, 2]]}, {"input": [[1, 1, 2, 0, 2, 1, 2, 0, 2, 1, 1], [2, 1, 2, 0, 2, 1, 2, 0, 1, 1, 1], [1, 2, 2, 0, 1, 2, 2, 0, 1, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 1, 0, 1, 1, 1, 0, 2, 2, 2], [2, 1, 1, 0, 1, 1, 1, 0, 2, 1, 2], [1, 2, 2, 0, 1, 2, 1, 0, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 2, 0, 1, 2, 1, 0, 1, 1, 1], [2, 2, 1, 0, 2, 1, 1, 0, 1, 1, 1], [2, 2, 2, 0, 1, 2, 1, 0, 1, 1, 1]], "output": [[2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 1, 2, 0, 1, 2, 1, 0, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 2, 0, 1, 2, 1, 0, 1, 2, 1], [2, 1, 2, 0, 2, 1, 1, 0, 2, 1, 1], [1, 2, 2, 0, 1, 2, 2, 0, 1, 2, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 1, 2, 2, 0, 2, 1, 2], [2, 1, 2, 0, 2, 2, 1, 0, 2, 1, 2], [2, 2, 2, 0, 2, 2, 2, 0, 1, 2, 2]]}, {"input": [[2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 1], [2, 2, 1, 0, 2, 2, 2, 0, 1, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 1, 1, 1, 0, 2, 1, 1], [2, 1, 2, 0, 1, 1, 1, 0, 1, 2, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 1, 1, 0, 1, 2, 1, 0, 2, 1, 1], [1, 2, 1, 0, 1, 2, 1, 0, 1, 1, 1], [2, 1, 1, 0, 2, 2, 2, 0, 1, 1, 1]], "output": [[1, 1, 1, 0, 2, 1, 1, 0, 1, 1, 1], [2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 1, 1, 1, 0, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 1, 0, 2, 1, 1, 0, 2, 1, 1], [1, 2, 1, 0, 1, 2, 1, 0, 1, 2, 1], [2, 2, 2, 0, 2, 1, 2, 0, 2, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 2, 2, 2, 0, 2, 2, 1], [2, 2, 2, 0, 2, 2, 1, 0, 1, 2, 2], [2, 2, 2, 0, 2, 2, 2, 0, 2, 1, 2]]}], "test": [{"input": [[2, 2, 2, 0, 2, 1, 2, 0, 2, 2, 1], [1, 2, 2, 0, 1, 2, 2, 0, 1, 2, 1], [2, 1, 2, 0, 2, 1, 2, 0, 2, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 2, 0, 1, 1, 1, 0, 1, 1, 1], [1, 2, 1, 0, 1, 1, 1, 0, 1, 1, 2], [1, 2, 1, 0, 1, 1, 1, 0, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 1, 1, 0, 2, 1, 1, 0, 2, 2, 2], [1, 1, 1, 0, 1, 2, 1, 0, 2, 2, 2], [1, 1, 2, 0, 1, 2, 2, 0, 2, 2, 2]], "output": [[2, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1], [1, 1, 1, 0, 1, 1, 2, 0, 1, 1, 1], [1, 1, 2, 0, 1, 1, 1, 0, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 1, 0, 2, 1, 1, 0, 1, 1, 2], [1, 2, 1, 0, 1, 2, 1, 0, 1, 2, 1], [2, 1, 2, 0, 1, 2, 2, 0, 1, 2, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 2, 2, 2, 0, 2, 1, 2], [2, 2, 2, 0, 1, 2, 2, 0, 1, 2, 2], [2, 2, 2, 0, 2, 1, 2, 0, 2, 1, 2]]}]}