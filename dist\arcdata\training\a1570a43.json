{"train": [{"input": [[3, 0, 2, 0, 0, 0, 3], [0, 2, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3]], "output": [[3, 0, 0, 0, 0, 0, 3], [0, 0, 0, 2, 0, 0, 0], [0, 0, 2, 2, 0, 0, 0], [0, 2, 2, 2, 2, 2, 0], [0, 0, 2, 0, 0, 0, 0], [0, 0, 2, 2, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 2, 2, 2, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0, 0], [2, 0, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 2, 0, 2, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 3, 2, 2, 2, 2, 0, 3, 0, 0], [0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 2, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 2, 0, 0, 0], [0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]]}]}