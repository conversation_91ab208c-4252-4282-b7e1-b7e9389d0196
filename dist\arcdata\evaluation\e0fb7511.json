{"train": [{"input": [[1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0], [1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1], [0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1], [1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1]], "output": [[1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 8, 8, 8, 1, 1, 8, 1, 1, 0], [1, 1, 8, 8, 1, 1, 8, 1, 1, 8, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1], [0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1], [1, 0, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1]]}, {"input": [[1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1], [1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1], [1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1], [1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1], [1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0], [1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1], [0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1], [1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1]], "output": [[1, 1, 1, 8, 8, 1, 1, 1, 8, 1, 0, 1, 1], [1, 1, 0, 1, 1, 1, 1, 1, 8, 8, 1, 0, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 8, 8, 1, 0, 1, 1, 0, 1, 1, 1, 1], [1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1], [1, 1, 1, 0, 1, 1, 1, 0, 1, 8, 1, 1, 1], [1, 8, 8, 1, 1, 1, 0, 1, 1, 8, 8, 1, 1], [1, 8, 8, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0], [1, 8, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1], [8, 8, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [8, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 8, 1], [1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 8, 8, 1]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1], [1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1], [0, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 1], [1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0], [1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1], [0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 0, 1], [1, 1, 1, 0, 1, 1, 1, 1, 8, 1, 1, 1, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1], [8, 1, 8, 8, 8, 1, 1, 1, 0, 1, 1, 0, 1], [1, 1, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 8], [1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 0, 1, 8], [0, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 8, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 8, 1], [0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1]]}], "test": [{"input": [[1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1], [0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0], [1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0], [0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1], [1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0], [1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0], [1, 0, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0], [1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1], [0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1], [1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0], [1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1]], "output": [[1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 8, 8, 1], [0, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 8, 8], [1, 1, 8, 8, 8, 1, 1, 0, 1, 1, 0, 1, 8], [0, 1, 1, 8, 1, 1, 1, 1, 1, 0, 1, 0, 1], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1], [1, 8, 1, 0, 1, 1, 8, 1, 1, 1, 0, 1, 8], [1, 8, 8, 1, 0, 1, 8, 1, 1, 1, 1, 1, 8], [1, 8, 1, 0, 1, 1, 1, 1, 8, 8, 8, 1, 8], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 8, 1, 8], [1, 0, 1, 8, 8, 1, 1, 1, 1, 8, 8, 1, 1], [0, 1, 1, 8, 1, 1, 1, 1, 0, 1, 8, 1, 1], [1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 8, 1, 0], [1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1]]}]}