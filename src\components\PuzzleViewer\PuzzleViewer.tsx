import React, { useState } from 'react';
import {
  Box,
  Stack,
  Typography,
  FormControlLabel,
  Checkbox,
  Divider,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import {
  Add as PlusIcon,
  ArrowForward as ArrowIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import ARCGridDisplay from '../ARCGridDisplay/ARCGridDisplay';
import { ARCGrid } from '../../types';

const PuzzleViewer: React.FC = () => {
  const { currentPuzzle, showValues, isLoading } = useSelector(
    (state: RootState) => state.puzzle
  );
  const [showDiffGrids, setShowDiffGrids] = useState(false);

  if (isLoading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Chargement du puzzle...
        </Typography>
      </Box>
    );
  }

  // Fonction pour calculer la grille de différence
  const calculateDiffGrid = (input: ARCGrid, output: ARCGrid): ARCGrid | null => {
    if (input.width !== output.width || input.height !== output.height) {
      return null; // Impossible de calculer la diff si les dimensions diffèrent
    }

    const diffGrid: number[][] = [];
    for (let y = 0; y < input.height; y++) {
      diffGrid[y] = [];
      for (let x = 0; x < input.width; x++) {
        // Si les cellules sont différentes, on met la valeur de l'output, sinon 0 (transparent)
        diffGrid[y][x] = input.grid[y][x] !== output.grid[y][x] ? output.grid[y][x] : 0;
      }
    }

    return {
      grid: diffGrid,
      width: input.width,
      height: input.height,
    };
  };

  if (!currentPuzzle) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Aucun puzzle sélectionné
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Sélectionnez un dataset et naviguez entre les puzzles
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2, height: '100%', overflow: 'auto' }}>
      <Stack spacing={3}>
        {/* Header */}
        <Box>
          <Typography variant="h5" gutterBottom>
            Puzzle {currentPuzzle.id}
          </Typography>
          <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
            <Chip
              label={`${currentPuzzle.train.length} exemples`}
              variant="outlined"
              size="small"
            />
            <Chip
              label={`${currentPuzzle.test.length} test(s)`}
              variant="outlined"
              size="small"
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={showDiffGrids}
                  onChange={() => setShowDiffGrids(!showDiffGrids)}
                  size="small"
                />
              }
              label="Grilles diff"
            />
          </Stack>
        </Box>

        <Divider />

        {/* Training Examples avec symbolique Input + Diff = Output */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Exemples d'entraînement
          </Typography>
          <Stack spacing={3}>
            {currentPuzzle.train.map((example, index) => {
              const diffGrid = calculateDiffGrid(example.input, example.output);

              return (
                <Card key={index} variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Exemple {index + 1}
                    </Typography>

                    {/* Symbolique: Input + Diff = Output */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                      {/* Input */}
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary" gutterBottom>
                          Input
                        </Typography>
                        <ARCGridDisplay
                          grid={example.input}
                          showValues={showValues}
                        />
                      </Box>

                      {/* Plus Icon */}
                      <PlusIcon color="action" sx={{ fontSize: 32 }} />

                      {/* Diff Grid (si disponible et activée) */}
                      {showDiffGrids && diffGrid && (
                        <>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" color="text.secondary" gutterBottom>
                              Diff
                            </Typography>
                            <ARCGridDisplay
                              grid={diffGrid}
                              showValues={showValues}
                            />
                          </Box>
                          <Typography variant="h6" color="action.active">=</Typography>
                        </>
                      )}

                      {/* Arrow (si pas de diff grid) */}
                      {(!showDiffGrids || !diffGrid) && (
                        <ArrowIcon color="action" sx={{ fontSize: 32 }} />
                      )}

                      {/* Output */}
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary" gutterBottom>
                          Output
                        </Typography>
                        <ARCGridDisplay
                          grid={example.output}
                          showValues={showValues}
                        />
                      </Box>
                    </Box>

                    {/* Informations sur les dimensions */}
                    <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={`Input: ${example.input.width}×${example.input.height}`}
                        size="small"
                        variant="outlined"
                      />
                      <Chip
                        label={`Output: ${example.output.width}×${example.output.height}`}
                        size="small"
                        variant="outlined"
                      />
                      {example.input.width !== example.output.width || example.input.height !== example.output.height ? (
                        <Chip
                          label="Dimensions changées"
                          size="small"
                          color="warning"
                        />
                      ) : (
                        <Chip
                          label="Dimensions identiques"
                          size="small"
                          color="success"
                        />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              );
            })}
          </Stack>
        </Box>

        <Divider />

        {/* Test Case */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Test à résoudre
          </Typography>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                {/* Test Input */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary" gutterBottom>
                    Test Input
                  </Typography>
                  <ARCGridDisplay
                    grid={currentPuzzle.test[0].input}
                    showValues={showValues}
                  />
                </Box>

                {/* Arrow vers solution */}
                <ArrowIcon color="action" sx={{ fontSize: 32 }} />

                {/* Expected Output (si disponible) */}
                {currentPuzzle.test[0].output ? (
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Expected Output
                    </Typography>
                    <ARCGridDisplay
                      grid={currentPuzzle.test[0].output}
                      showValues={showValues}
                    />
                  </Box>
                ) : (
                  <Box sx={{
                    textAlign: 'center',
                    p: 3,
                    border: '2px dashed',
                    borderColor: 'divider',
                    borderRadius: 1,
                    minWidth: 100,
                    minHeight: 100,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Typography variant="body2" color="text.secondary">
                      Solution à trouver
                    </Typography>
                  </Box>
                )}
              </Box>

              {/* Informations sur le test */}
              <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={`Input: ${currentPuzzle.test[0].input.width}×${currentPuzzle.test[0].input.height}`}
                  size="small"
                  variant="outlined"
                />
                {currentPuzzle.test[0].output && (
                  <Chip
                    label={`Expected: ${currentPuzzle.test[0].output.width}×${currentPuzzle.test[0].output.height}`}
                    size="small"
                    variant="outlined"
                  />
                )}
                <Chip
                  label={currentPuzzle.test[0].output ? "Solution connue" : "Solution inconnue"}
                  size="small"
                  color={currentPuzzle.test[0].output ? "info" : "warning"}
                />
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Stack>
    </Box>
  );
};

export default PuzzleViewer;