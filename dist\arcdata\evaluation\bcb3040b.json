{"train": [{"input": [[2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0], [0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1], [0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0], [0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0], [1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1], [1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0], [0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1], [0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0], [1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1], [0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1], [0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2]], "output": [[2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0], [0, 2, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0], [0, 1, 2, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1], [0, 1, 1, 0, 3, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0], [0, 0, 0, 1, 1, 3, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1], [0, 1, 0, 0, 0, 0, 2, 0, 0, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0, 1, 1, 0], [1, 0, 0, 0, 1, 0, 0, 1, 2, 1, 0, 1, 0, 0, 1, 1], [1, 1, 1, 1, 1, 1, 0, 0, 1, 2, 1, 1, 0, 1, 0, 0], [0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 2, 1, 0, 0, 0, 1], [0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 2, 0, 1, 0], [1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 3, 0, 1], [0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 2, 1], [0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2]]}, {"input": [[0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0], [0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0], [1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1], [1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0], [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], [2, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1, 2], [0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 1], [0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0]], "output": [[0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0], [0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0], [1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1], [1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0], [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], [2, 2, 3, 3, 3, 2, 2, 3, 2, 2, 3, 2], [0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 1], [0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0]]}, {"input": [[0, 1, 1, 1, 0, 0, 2, 0, 0, 1], [1, 0, 1, 0, 1, 1, 1, 0, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0], [0, 1, 0, 0, 0, 0, 1, 1, 1, 0], [1, 1, 1, 0, 0, 0, 1, 0, 0, 1], [1, 1, 1, 1, 1, 1, 0, 0, 1, 0], [0, 1, 1, 0, 1, 0, 1, 0, 1, 0], [1, 0, 0, 0, 1, 0, 1, 1, 0, 1], [0, 1, 1, 1, 1, 0, 0, 1, 1, 1], [0, 1, 0, 1, 0, 0, 2, 1, 1, 0]], "output": [[0, 1, 1, 1, 0, 0, 2, 0, 0, 1], [1, 0, 1, 0, 1, 1, 3, 0, 1, 1], [0, 0, 0, 0, 0, 0, 2, 0, 1, 0], [0, 1, 0, 0, 0, 0, 3, 1, 1, 0], [1, 1, 1, 0, 0, 0, 3, 0, 0, 1], [1, 1, 1, 1, 1, 1, 2, 0, 1, 0], [0, 1, 1, 0, 1, 0, 3, 0, 1, 0], [1, 0, 0, 0, 1, 0, 3, 1, 0, 1], [0, 1, 1, 1, 1, 0, 2, 1, 1, 1], [0, 1, 0, 1, 0, 0, 2, 1, 1, 0]]}], "test": [{"input": [[1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 2], [1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1], [0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0], [1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1], [0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1], [1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1], [1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1], [0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 0], [1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 0], [0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1], [1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0], [1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0], [1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0], [1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 1, 1], [2, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0]], "output": [[1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 2], [1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3, 1], [0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 2, 0, 0], [1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 3, 0, 0, 1], [0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 2, 0, 1, 0, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 3, 0, 1, 0, 1, 1], [1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 2, 1, 1, 0, 1, 0, 1], [1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 3, 0, 0, 1, 1, 1, 1, 1], [0, 0, 1, 1, 1, 1, 1, 0, 0, 2, 1, 0, 1, 1, 1, 1, 0, 0], [1, 1, 1, 0, 1, 0, 0, 0, 2, 1, 0, 1, 0, 0, 1, 1, 1, 0], [0, 0, 0, 0, 0, 1, 1, 2, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], [0, 0, 1, 1, 1, 0, 3, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1], [1, 1, 1, 1, 0, 3, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1], [1, 1, 0, 0, 3, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0], [1, 0, 0, 3, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0], [1, 1, 3, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0], [1, 3, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 1, 1], [2, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0]]}]}