{"train": [{"input": [[0, 0, 2, 0, 0, 6, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0]], "output": [[0, 0, 2, 0, 0, 6, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 6, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0]]}, {"input": [[9, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 8, 0, 0, 3], [0, 0, 0, 9, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7]], "output": [[9, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 9, 0, 0, 0, 0, 0, 0, 3, 0], [0, 0, 9, 0, 0, 0, 8, 0, 0, 3], [0, 0, 0, 9, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 7, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 7, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7]]}, {"input": [[0, 0, 0, 6, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 0, 9, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 6, 0, 8, 0, 0, 0, 0], [0, 0, 6, 0, 0, 0, 8, 0, 0, 0], [0, 6, 4, 0, 0, 0, 0, 8, 0, 0], [6, 0, 0, 4, 0, 0, 0, 0, 8, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 8], [0, 0, 0, 0, 9, 4, 0, 0, 0, 0], [0, 0, 0, 9, 0, 0, 4, 0, 0, 0], [0, 0, 9, 0, 0, 0, 0, 0, 0, 0], [0, 9, 0, 0, 0, 0, 0, 0, 0, 0], [9, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 3, 0, 0, 9], [7, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 9, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 3, 0, 0, 9], [7, 0, 0, 0, 0, 3, 0, 0, 9, 0], [0, 7, 0, 0, 3, 0, 0, 9, 0, 0], [0, 0, 7, 3, 0, 0, 9, 0, 0, 0], [6, 0, 0, 7, 0, 0, 0, 0, 0, 0], [0, 6, 0, 0, 7, 0, 0, 0, 0, 4], [0, 0, 6, 0, 0, 7, 0, 0, 4, 0], [0, 0, 0, 6, 0, 0, 7, 4, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0]]}]}