import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Stack,
  List,
  ListItem,
  ListItemText,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Send as SendIcon,
  Clear as ClearIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import {
  addMessage,
  setCurrentInput,
  setSelectedProvider,
  setSelectedModel,
  clearMessages,
  setIsLoading,
  setError,
} from '../../store/slices/chatSlice';
import { AI_PROVIDERS, ChatMessage } from '../../types';
import { aiProviderService } from '../../services/aiProviderService';
import { solutionValidator } from '../../services/solutionValidator';
import ARCGridComparison from '../ARCGridComparison/ARCGridComparison';

const ChatInterface: React.FC = () => {
  const dispatch = useDispatch();
  const {
    messages,
    currentInput,
    isLoading,
    selectedProvider,
    selectedModel,
    error,
  } = useSelector((state: RootState) => state.chat);

  const { currentPuzzle, analysis } = useSelector((state: RootState) => state.puzzle);
  const [showSettings, setShowSettings] = useState(false);

  const handleSendMessage = async () => {
    if (!currentInput.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: currentInput,
      timestamp: new Date(),
    };

    dispatch(addMessage(userMessage));
    dispatch(setCurrentInput(''));
    dispatch(setIsLoading(true));
    dispatch(setError(null));

    try {
      // Vérifier que le fournisseur a une clé API si nécessaire
      if (!aiProviderService.hasApiKey(selectedProvider)) {
        throw new Error(`Clé API requise pour ${AI_PROVIDERS[selectedProvider].name}`);
      }

      // Générer le prompt avec le contexte du puzzle
      const prompt = generatePromptWithContext(currentInput, currentPuzzle, analysis);

      // Envoyer la requête à l'IA
      const response = await aiProviderService.sendRequest({
        provider: selectedProvider,
        model: selectedModel,
        prompt,
        temperature: 0.7,
        maxTokens: 2000,
      });

      // Créer le message de réponse
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'chercheur',
        content: response.content,
        timestamp: new Date(),
        metadata: {
          provider: response.provider,
          model: response.model,
          tokens: response.tokens,
          cost: response.cost,
          responseTime: response.responseTime,
        },
      };

      // Tenter de parser une solution si c'est une réponse de résolution
      if (currentPuzzle && currentPuzzle.test[0].output) {
        const proposedSolution = solutionValidator.parseSolutionFromText(response.content);
        if (proposedSolution) {
          const validation = solutionValidator.validateSolution(
            proposedSolution,
            currentPuzzle.test[0].output
          );
          aiMessage.metadata!.validationResult = validation;
        }
      }

      dispatch(addMessage(aiMessage));
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      dispatch(setError((error as Error).message));

      // Ajouter un message d'erreur
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'system',
        content: `Erreur: ${(error as Error).message}`,
        timestamp: new Date(),
      };
      dispatch(addMessage(errorMessage));
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  const generatePromptWithContext = (userInput: string, puzzle: any, analysis: any): string => {
    let prompt = userInput;

    if (puzzle) {
      prompt += '\n\nContexte du puzzle:\n';
      prompt += `ID: ${puzzle.id}\n`;
      prompt += `Exemples d'entraînement: ${puzzle.train.length}\n`;
      prompt += `Tests: ${puzzle.test.length}\n`;

      if (analysis) {
        prompt += '\nAnalyse disponible:\n';
        prompt += `- Couleurs input: ${analysis.colors?.input?.present_colors?.join(', ') || 'N/A'}\n`;
        prompt += `- Couleurs output: ${analysis.colors?.output?.present_colors?.join(', ') || 'N/A'}\n`;
        prompt += `- Objets détectés: ${analysis.objects?.input?.object_statistics?.total_objects || 0}\n`;
        prompt += `- Complexité: ${analysis.complexity?.transformation_complexity || 'N/A'}\n`;
      }
    }

    return prompt;
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(timestamp);
  };

  const getRoleColor = (role: ChatMessage['role']) => {
    switch (role) {
      case 'user':
        return 'primary';
      case 'chercheur':
        return 'secondary';
      case 'demo':
        return 'warning';
      case 'system':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Chat IA</Typography>
          <Stack direction="row" spacing={1}>
            <IconButton
              size="small"
              onClick={() => setShowSettings(!showSettings)}
            >
              <SettingsIcon />
            </IconButton>
            <IconButton size="small" onClick={() => dispatch(clearMessages())}>
              <ClearIcon />
            </IconButton>
          </Stack>
        </Stack>

        {/* Settings Panel */}
        {showSettings && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Stack direction="row" spacing={2}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Fournisseur</InputLabel>
                <Select
                  value={selectedProvider}
                  label="Fournisseur"
                  onChange={(e) => dispatch(setSelectedProvider(e.target.value))}
                >
                  {Object.values(AI_PROVIDERS).map((provider) => (
                    <MenuItem key={provider.id} value={provider.id}>
                      {provider.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Modèle</InputLabel>
                <Select
                  value={selectedModel}
                  label="Modèle"
                  onChange={(e) => dispatch(setSelectedModel(e.target.value))}
                >
                  {AI_PROVIDERS[selectedProvider]?.models.map((model) => (
                    <MenuItem key={model} value={model}>
                      {model}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Stack>
          </Box>
        )}
      </Box>

      {/* Error Display */}
      {error && (
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Alert severity="error" onClose={() => dispatch(setError(null))}>
            {error}
          </Alert>
        </Box>
      )}

      {/* Messages */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {messages.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Commencez une conversation avec l'IA pour analyser le puzzle
            </Typography>
          </Box>
        ) : (
          <List>
            {messages.map((message) => (
              <ListItem key={message.id} alignItems="flex-start">
                <ListItemText
                  primary={
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Chip
                        label={message.role}
                        size="small"
                        color={getRoleColor(message.role) as any}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(message.timestamp)}
                      </Typography>
                      {message.metadata && (
                        <>
                          {message.metadata.provider && (
                            <Chip
                              label={`${message.metadata.provider}/${message.metadata.model}`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                          {message.metadata.tokens && (
                            <Chip
                              label={`${message.metadata.tokens} tokens`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                          {message.metadata.cost && (
                            <Chip
                              label={`$${message.metadata.cost.toFixed(4)}`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                          <Chip
                            label={`${message.metadata.responseTime}ms`}
                            size="small"
                            variant="outlined"
                          />
                        </>
                      )}
                    </Stack>
                  }
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      <Typography
                        variant="body2"
                        sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
                      >
                        {message.content}
                      </Typography>
                      {message.metadata?.validationResult && (
                        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Résultat de validation:
                          </Typography>
                          <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                            <Chip
                              label={message.metadata.validationResult.isValid ? 'Solution correcte' : 'Solution incorrecte'}
                              color={message.metadata.validationResult.isValid ? 'success' : 'error'}
                              size="small"
                            />
                            <Chip
                              label={`Précision: ${(message.metadata.validationResult.accuracy * 100).toFixed(1)}%`}
                              variant="outlined"
                              size="small"
                            />
                          </Stack>
                          {message.metadata.validationResult.feedback && (
                            <Typography variant="body2" color="text.secondary">
                              {message.metadata.validationResult.feedback}
                            </Typography>
                          )}

                          {/* Afficher la comparaison de grilles si disponible */}
                          {currentPuzzle && currentPuzzle.test[0].output && (
                            (() => {
                              const proposedSolution = solutionValidator.parseSolutionFromText(message.content);
                              return proposedSolution ? (
                                <ARCGridComparison
                                  proposed={proposedSolution}
                                  expected={currentPuzzle.test[0].output}
                                  validationResult={message.metadata.validationResult}
                                />
                              ) : null;
                            })()
                          )}
                        </Box>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      <Divider />

      {/* Input */}
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={1}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Tapez votre message..."
            value={currentInput}
            onChange={(e) => dispatch(setCurrentInput(e.target.value))}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            size="small"
          />
          <Button
            variant="contained"
            onClick={handleSendMessage}
            disabled={!currentInput.trim() || isLoading}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            {isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          </Button>
        </Stack>
      </Box>
    </Paper>
  );
};

export default ChatInterface;