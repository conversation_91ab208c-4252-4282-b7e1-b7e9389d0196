import { DemoSession, DemoResult, PuzzleAnalysis } from '../types';

/**
 * Service de persistance des données
 * Utilise localStorage pour le stockage côté client
 * En production, ceci pourrait être remplacé par une vraie base de données
 */

export interface StoredSession {
  id: string;
  startTime: string;
  endTime?: string;
  config: any;
  results: DemoResult[];
  metadata: {
    totalPuzzles: number;
    successCount: number;
    failureCount: number;
    totalCost: number;
    averageResponseTime: number;
  };
}

export interface StoredAnalysis {
  puzzleId: string;
  analysis: PuzzleAnalysis;
  timestamp: string;
  version: string; // Version de l'analyseur
}

class DataStorageService {
  private static instance: DataStorageService;
  private readonly SESSIONS_KEY = 'arc_demo_sessions';
  private readonly ANALYSES_KEY = 'arc_puzzle_analyses';
  private readonly SETTINGS_KEY = 'arc_app_settings';

  private constructor() {}

  static getInstance(): DataStorageService {
    if (!DataStorageService.instance) {
      DataStorageService.instance = new DataStorageService();
    }
    return DataStorageService.instance;
  }

  /**
   * Sauvegarde une session de demo
   */
  saveSession(session: DemoSession): void {
    try {
      const sessions = this.getAllSessions();
      const storedSession: StoredSession = {
        id: session.id,
        startTime: session.startTime.toISOString(),
        endTime: session.endTime?.toISOString(),
        config: {}, // Ajouter la config si nécessaire
        results: session.results,
        metadata: {
          totalPuzzles: session.puzzlesProcessed,
          successCount: session.successCount,
          failureCount: session.failureCount,
          totalCost: session.totalCost,
          averageResponseTime: session.averageResponseTime,
        },
      };

      // Remplacer ou ajouter la session
      const existingIndex = sessions.findIndex(s => s.id === session.id);
      if (existingIndex >= 0) {
        sessions[existingIndex] = storedSession;
      } else {
        sessions.push(storedSession);
      }

      // Limiter à 50 sessions max
      if (sessions.length > 50) {
        sessions.splice(0, sessions.length - 50);
      }

      localStorage.setItem(this.SESSIONS_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la session:', error);
    }
  }

  /**
   * Récupère toutes les sessions sauvegardées
   */
  getAllSessions(): StoredSession[] {
    try {
      const stored = localStorage.getItem(this.SESSIONS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Erreur lors du chargement des sessions:', error);
      return [];
    }
  }

  /**
   * Récupère une session par ID
   */
  getSession(id: string): StoredSession | null {
    const sessions = this.getAllSessions();
    return sessions.find(s => s.id === id) || null;
  }

  /**
   * Supprime une session
   */
  deleteSession(id: string): void {
    try {
      const sessions = this.getAllSessions();
      const filtered = sessions.filter(s => s.id !== id);
      localStorage.setItem(this.SESSIONS_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Erreur lors de la suppression de la session:', error);
    }
  }

  /**
   * Sauvegarde l'analyse d'un puzzle
   */
  saveAnalysis(puzzleId: string, analysis: PuzzleAnalysis): void {
    try {
      const analyses = this.getAllAnalyses();
      const storedAnalysis: StoredAnalysis = {
        puzzleId,
        analysis,
        timestamp: new Date().toISOString(),
        version: '1.0', // Version de l'analyseur
      };

      // Remplacer ou ajouter l'analyse
      const existingIndex = analyses.findIndex(a => a.puzzleId === puzzleId);
      if (existingIndex >= 0) {
        analyses[existingIndex] = storedAnalysis;
      } else {
        analyses.push(storedAnalysis);
      }

      // Limiter à 200 analyses max
      if (analyses.length > 200) {
        analyses.splice(0, analyses.length - 200);
      }

      localStorage.setItem(this.ANALYSES_KEY, JSON.stringify(analyses));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'analyse:', error);
    }
  }

  /**
   * Récupère toutes les analyses sauvegardées
   */
  getAllAnalyses(): StoredAnalysis[] {
    try {
      const stored = localStorage.getItem(this.ANALYSES_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Erreur lors du chargement des analyses:', error);
      return [];
    }
  }

  /**
   * Récupère l'analyse d'un puzzle
   */
  getAnalysis(puzzleId: string): StoredAnalysis | null {
    const analyses = this.getAllAnalyses();
    return analyses.find(a => a.puzzleId === puzzleId) || null;
  }

  /**
   * Supprime l'analyse d'un puzzle
   */
  deleteAnalysis(puzzleId: string): void {
    try {
      const analyses = this.getAllAnalyses();
      const filtered = analyses.filter(a => a.puzzleId !== puzzleId);
      localStorage.setItem(this.ANALYSES_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'analyse:', error);
    }
  }

  /**
   * Sauvegarde les paramètres de l'application
   */
  saveSettings(settings: Record<string, any>): void {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
    }
  }

  /**
   * Récupère les paramètres de l'application
   */
  getSettings(): Record<string, any> {
    try {
      const stored = localStorage.getItem(this.SETTINGS_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
      return {};
    }
  }

  /**
   * Nettoie toutes les données stockées
   */
  clearAllData(): void {
    try {
      localStorage.removeItem(this.SESSIONS_KEY);
      localStorage.removeItem(this.ANALYSES_KEY);
      localStorage.removeItem(this.SETTINGS_KEY);
    } catch (error) {
      console.error('Erreur lors du nettoyage des données:', error);
    }
  }

  /**
   * Exporte toutes les données en JSON
   */
  exportData(): string {
    const data = {
      sessions: this.getAllSessions(),
      analyses: this.getAllAnalyses(),
      settings: this.getSettings(),
      exportDate: new Date().toISOString(),
      version: '1.0',
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * Importe des données depuis JSON
   */
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.sessions) {
        localStorage.setItem(this.SESSIONS_KEY, JSON.stringify(data.sessions));
      }
      
      if (data.analyses) {
        localStorage.setItem(this.ANALYSES_KEY, JSON.stringify(data.analyses));
      }
      
      if (data.settings) {
        localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(data.settings));
      }
      
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'importation des données:', error);
      return false;
    }
  }

  /**
   * Obtient les statistiques de stockage
   */
  getStorageStats(): {
    sessions: number;
    analyses: number;
    totalSize: number;
    usedSpace: string;
  } {
    const sessions = this.getAllSessions();
    const analyses = this.getAllAnalyses();
    
    // Calculer la taille approximative
    const sessionsSize = JSON.stringify(sessions).length;
    const analysesSize = JSON.stringify(analyses).length;
    const settingsSize = JSON.stringify(this.getSettings()).length;
    const totalSize = sessionsSize + analysesSize + settingsSize;
    
    return {
      sessions: sessions.length,
      analyses: analyses.length,
      totalSize,
      usedSpace: this.formatBytes(totalSize),
    };
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const dataStorage = DataStorageService.getInstance();
