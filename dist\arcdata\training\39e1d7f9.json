{"train": [{"input": [[0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 6, 6, 6, 6, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 6, 6, 6, 6, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 6, 6, 6, 6, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 6, 6, 6, 6, 8, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 4, 4, 4, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0]], "output": [[0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 4, 4, 4, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 6, 6, 6, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0]]}, {"input": [[0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 2, 2, 2], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 2, 2, 2], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 2, 2, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 2, 2, 2, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0]], "output": [[4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2], [4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2], [4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4], [4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4], [4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 2, 2, 2, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 4, 4, 4, 8, 4, 4, 4, 8, 4, 4, 4, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0]]}], "test": [{"input": [[0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 6, 6, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 6, 6, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 6, 6, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 6, 6, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [6, 6, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [6, 6, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0]], "output": [[0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0], [3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 3, 3, 4, 6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0], [6, 6, 4, 3, 3, 4, 0, 0, 4, 0, 0, 4, 8, 8, 4, 3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0], [3, 3, 4, 8, 8, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0]]}]}