{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 2, 2]], "output": [[0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0]], "output": [[2, 2, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 8, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 8, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2]], "output": [[0, 0, 0, 8, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 8, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 3, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 3, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 3, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 3, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 3, 0, 0, 0, 0, 2, 2, 2, 2], [0, 0, 3, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 3, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [3, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2]]}]}