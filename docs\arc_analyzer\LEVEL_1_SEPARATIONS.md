# 🔲 Niveau 1 - Séparations, Bordures et Blocs

## 📋 Vue d'Ensemble

Ce domaine analyse les **structures de séparation** dans les grilles ARC AGI : bordures, séparateurs, grilles, et les blocs qu'ils créent.

## 🎯 Sous-Niveaux Séquentiels

### **Niveau 1A : Détection Géométrique Pure**

#### **Responsabilité**
Détecter factuellement tous les éléments linéaires uniformes sans interprétation.

#### **Structure de Données**
```python
geometric_detection = {
    'uniform_rows': [
        {
            'index': int,           # Index de la ligne (0-based)
            'color': int,           # Couleur uniforme de la ligne
            'length': int,          # Largeur de la grille (nombre de colonnes)
            'completeness': float   # 1.0 si complètement uniforme
        }
    ],
    'uniform_columns': [
        {
            'index': int,           # Index de la colonne (0-based)
            'color': int,           # Couleur uniforme de la colonne
            'length': int,          # Hauteur de la grille (nombre de lignes)
            'completeness': float   # 1.0 si complètement uniforme
        }
    ],
    'uniform_main_diagonals': [
        {
            'start': (int, int),    # Point de départ (ligne, colonne)
            'end': (int, int),      # Point d'arrivée (ligne, colonne)
            'color': int,           # Couleur uniforme de la diagonale
            'length': int,          # Nombre de pixels de la diagonale
            'diagonal_type': str    # 'main' pour diagonales principales
        }
    ],
    'uniform_anti_diagonals': [
        {
            'start': (int, int),    # Point de départ (ligne, colonne)
            'end': (int, int),      # Point d'arrivée (ligne, colonne)
            'color': int,           # Couleur uniforme de la diagonale
            'length': int,          # Nombre de pixels de la diagonale
            'diagonal_type': str    # 'anti' pour diagonales anti
        }
    ],
    'line_distribution': {
        'total_uniform_lines': int,
        'rows': int,                # Nombre de lignes uniformes
        'columns': int,             # Nombre de colonnes uniformes
        'main_diagonals': int,      # Nombre de diagonales principales uniformes
        'anti_diagonals': int       # Nombre de diagonales anti uniformes
    },
    'color_frequency_in_lines': {
        # Combien de lignes uniformes par couleur
        0: int,  # Nombre de lignes uniformes couleur 0
        1: int,  # Nombre de lignes uniformes couleur 1
        # etc.
    }
}
```

#### **Algorithmes de Détection**
```python
def detect_uniform_rows(grid_array):
    uniform_rows = []
    for i, row in enumerate(grid_array):
        if len(set(row)) == 1:  # Ligne uniforme
            uniform_rows.append({
                'index': i,
                'color': row[0],
                'length': len(row),
                'completeness': 1.0
            })
    return uniform_rows

def detect_uniform_columns(grid_array):
    uniform_columns = []
    for j in range(grid_array.shape[1]):
        column = grid_array[:, j]
        if len(set(column)) == 1:  # Colonne uniforme
            uniform_columns.append({
                'index': j,
                'color': column[0],
                'length': len(column),
                'completeness': 1.0
            })
    return uniform_columns
```

---

### **Niveau 1B : Classification Structurelle**

#### **Responsabilité**
Classifier les lignes uniformes selon leur rôle structural : bordures, séparateurs, grilles.

#### **Structure de Données**
```python
structural_classification = {
    'borders': {
        'top_border': {
            'exists': bool,         # Bordure détectée
            'row': int,             # Index de la ligne (généralement 0)
            'color': int,           # Couleur de la bordure
            'complete': bool,       # Couvre toute la largeur
            'thickness': int        # Épaisseur de la bordure (généralement 1)
        },
        'bottom_border': {
            'exists': bool,
            'row': int,             # Index de la ligne (généralement height-1)
            'color': int,
            'complete': bool,
            'thickness': int
        },
        'left_border': {
            'exists': bool,
            'column': int,          # Index de la colonne (généralement 0)
            'color': int,
            'complete': bool,
            'thickness': int
        },
        'right_border': {
            'exists': bool,
            'column': int,          # Index de la colonne (généralement width-1)
            'color': int,
            'complete': bool,
            'thickness': int
        },
        'border_completeness': str,  # 'complete', 'partial', 'none'
        'border_colors': List[int],  # Couleurs utilisées pour les bordures
        'corner_analysis': {
            'top_left': {'color': int, 'matches_borders': bool},
            'top_right': {'color': int, 'matches_borders': bool},
            'bottom_left': {'color': int, 'matches_borders': bool},
            'bottom_right': {'color': int, 'matches_borders': bool},
            'corner_consistency': bool  # Coins cohérents avec bordures
        }
    },
    
    'separators': [
        {
            'separator_id': str,        # ID unique du séparateur
            'type': str,                # 'vertical' | 'horizontal'
            'position': int,            # Position (index ligne/colonne)
            'color': int,               # Couleur du séparateur
            'spans_full_dimension': bool, # Couvre toute la hauteur/largeur
            'thickness': int,           # Épaisseur du séparateur
            'divides_into_blocks': bool, # Crée effectivement des blocs
            
            # Pour séparateurs verticaux
            'left_block_width': int or None,
            'right_block_width': int or None,
            
            # Pour séparateurs horizontaux  
            'top_block_height': int or None,
            'bottom_block_height': int or None,
            
            # Propriétés du séparateur
            'is_edge_separator': bool,   # Séparateur en bord de grille
            'separates_uniform_regions': bool # Sépare des zones uniformes
        }
    ],
    
    'grid_lines': {
        'has_grid_structure': bool,
        'vertical_grid_lines': List[int],    # Positions des lignes verticales
        'horizontal_grid_lines': List[int],  # Positions des lignes horizontales
        'grid_line_color': int or None,      # Couleur des lignes de grille (si uniforme)
        'creates_regular_blocks': bool,      # Crée des blocs de taille identique
        'block_dimensions': (int, int) or None, # (hauteur, largeur) des blocs
        'grid_size': (int, int) or None,     # (nb_lignes, nb_colonnes) de blocs
        'grid_regularity': float,            # Score de régularité (0-1)
        'grid_completeness': float           # Score de complétude (0-1)
    },
    
    'structural_roles': {
        'containment_lines': List[int],      # Lignes qui "contiennent" (bordures)
        'division_lines': List[int],         # Lignes qui "divisent" (séparateurs/grille)
        'decoration_lines': List[int],       # Lignes purement décoratives
        'functional_line_ratio': float,      # Ratio lignes fonctionnelles
        'dominant_separator_color': int or None, # Couleur dominante des séparateurs
        'separator_color_consistency': bool   # Même couleur pour tous les séparateurs
    }
}
```

#### **Algorithmes de Classification**
```python
def classify_borders(grid_array, uniform_lines):
    borders = {}
    height, width = grid_array.shape
    
    # Vérifier bordure du haut
    if any(line['index'] == 0 for line in uniform_lines['uniform_rows']):
        top_line = next(line for line in uniform_lines['uniform_rows'] if line['index'] == 0)
        borders['top_border'] = {
            'exists': True,
            'row': 0,
            'color': top_line['color'],
            'complete': True,
            'thickness': 1
        }
    else:
        borders['top_border'] = {'exists': False}
    
    # Répéter pour autres bordures...
    return borders

def classify_separators(grid_array, uniform_lines):
    separators = []
    height, width = grid_array.shape
    
    # Identifier séparateurs verticaux (colonnes uniformes non-bordures)
    for col_line in uniform_lines['uniform_columns']:
        col_idx = col_line['index']
        if 0 < col_idx < width - 1:  # Pas une bordure
            separators.append({
                'separator_id': f'vertical_{col_idx}',
                'type': 'vertical',
                'position': col_idx,
                'color': col_line['color'],
                'spans_full_dimension': True,
                'thickness': 1,
                'divides_into_blocks': True,
                'left_block_width': col_idx,
                'right_block_width': width - col_idx - 1
            })
    
    # Répéter pour séparateurs horizontaux...
    return separators
```

---

### **Niveau 1C : Analyse de Blocs**

#### **Responsabilité**
Analyser les blocs créés par les séparateurs et grilles, avec analyse détaillée du contenu.

#### **Structure de Données**
```python
block_analysis = {
    'detected_blocks': [
        {
            'block_id': str,                # ID unique 'block_row_col'
            'bounds': {
                'top': int, 'left': int, 'bottom': int, 'right': int
            },
            'dimensions': (int, int),       # (hauteur, largeur)
            'area': int,                    # Nombre de pixels
            'separated_by': List[str],      # IDs des séparateurs qui délimitent
            
            # Contenu du bloc
            'block_grid_array': np.ndarray, # Sous-grille extraite
            'block_statistics': {
                'value_frequencies': Dict[int, int], # Comptages dans ce bloc
                'width': int,
                'height': int,
                'total_pixels': int
            },
            
            # Analyse des couleurs du bloc
            'block_color_analysis': {
                'present_colors': List[int],
                'dominant_color': int,
                'probable_background': int or None,
                'non_background_colors': List[int],
                'color_diversity': int,
                'background_ratio': float
            },
            
            # Uniformité du bloc
            'block_uniformity': {
                'is_uniform': bool,
                'uniform_color': int or None,
                'uniformity_ratio': float,
                'has_internal_pattern': bool,
                'pattern_complexity': str    # 'none', 'simple', 'complex'
            },
            
            # Symétries du bloc
            'block_symmetries': {
                'horizontal': bool,
                'vertical': bool,
                'diagonal_main': bool,
                'diagonal_anti': bool,
                'symmetry_count': int
            },
            
            # Hash pour comparaisons
            'block_pattern_hash': str,
            
            # Variants pour comparaisons futures (Niveau 2)
            'transformation_variants': {
                'rotations': {
                    'rot_90': np.ndarray,
                    'rot_180': np.ndarray,
                    'rot_270': np.ndarray
                },
                'flips': {
                    'flip_horizontal': np.ndarray,
                    'flip_vertical': np.ndarray
                },
                'variant_hashes': {
                    'original': str,
                    'rot_90': str,
                    'rot_180': str,
                    'rot_270': str,
                    'flip_h': str,
                    'flip_v': str
                }
            }
        }
    ],
    
    # Analyse globale des blocs
    'block_uniformity': {
        'all_same_size': bool,
        'uniform_dimensions': (int, int) or None,
        'size_variations': List[Dict],
        'total_blocks': int
    },
    
    # Patterns de contenu entre blocs
    'block_content_patterns': {
        'uniform_blocks': List[str],        # IDs des blocs uniformes
        'patterned_blocks': List[str],      # IDs des blocs avec motifs
        'diverse_blocks': List[str],        # IDs des blocs variés
        
        'background_consistency': {
            'same_background_across_blocks': bool,
            'common_background_color': int or None,
            'background_variations': Dict[str, int]
        },
        
        'content_similarity': {
            'identical_blocks': List[List[str]],    # Groupes de blocs identiques
            'similar_blocks': List[List[str]],      # Groupes de blocs similaires
            'unique_blocks': List[str]              # Blocs uniques
        },
        
        'pattern_relationships': {
            'same_pattern_groups': List[List[str]], # Même hash
            'rotation_groups': [
                {
                    'base_block': str,
                    'rotated_variants': List[Dict],
                    'rotation_type': str
                }
            ],
            'flip_groups': [
                {
                    'base_block': str,
                    'flipped_variants': List[Dict],
                    'flip_type': str
                }
            ]
        }
    },
    
    # Détection d'anomalies entre blocs
    'anomaly_detection': {
        'color_frequency_analysis': {
            'per_color_counts': Dict[int, List[int]], # {couleur: [count_par_bloc]}
            'color_progressions': [
                {
                    'color': int,
                    'sequence': List[int],
                    'progression_type': str,    # 'arithmetic', 'geometric', 'constant'
                    'step': int or float,
                    'anomaly_blocks': List[str]
                }
            ]
        },
        
        'missing_color_patterns': [
            {
                'missing_color': int,
                'present_in_blocks': List[str],
                'absent_from_blocks': List[str],
                'pattern_type': str,            # 'single_missing', 'systematic'
                'anomaly_severity': str         # 'minor', 'major', 'critical'
            }
        ],
        
        'statistical_outliers': [
            {
                'block_id': str,
                'anomaly_type': str,
                'expected_value': float,
                'actual_value': float,
                'deviation_score': float,
                'severity': str
            }
        ]
    },
    
    # Opérations logiques entre blocs de même taille
    'logical_operations_analysis': {
        'same_size_pairs': [
            {
                'block1': str,
                'block2': str,
                'dimensions': (int, int),
                'logical_results': {
                    'xor_result': np.ndarray,
                    'and_result': np.ndarray,
                    'or_result': np.ndarray,
                    'xor_hash': str,
                    'and_hash': str,
                    'or_hash': str
                },
                'operation_properties': {
                    'xor_uniform': bool,
                    'and_uniform': bool,
                    'or_uniform': bool,
                    'xor_empty': bool,
                    'and_empty': bool
                }
            }
        ],
        
        'result_block_matching': {
            'xor_matches_existing': List[Dict],
            'and_matches_existing': List[Dict],
            'or_matches_existing': List[Dict]
        }
    }
}
```

## 🎯 Cas d'Usage Spécifiques

### **1. Grilles avec Séparateurs Gris (Couleur 5)**
- Détection automatique des colonnes/lignes grises
- Classification comme séparateurs
- Extraction des blocs délimités
- Analyse du contenu de chaque bloc

### **2. Bordures Noires Complètes**
- Détection des 4 bordures
- Classification comme containment
- Analyse du contenu interne

### **3. Grilles Régulières**
- Détection de la structure de grille
- Extraction de tous les blocs
- Analyse des patterns entre blocs
- Détection d'anomalies

## 🛡️ Règles de Pureté

### **Interdictions**
- ❌ Pas de comparaison input/output
- ❌ Pas de prédiction de transformation
- ❌ Pas d'interprétation de la règle du puzzle

### **Autorisations**
- ✅ Détection factuelle des structures
- ✅ Classification basée sur la position et les propriétés
- ✅ Précalcul des variants pour comparaisons futures

---

**Ce domaine fournit une analyse complète et structurée de tous les éléments de séparation dans les grilles ARC AGI.**