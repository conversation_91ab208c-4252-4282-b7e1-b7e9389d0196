{"train": [{"input": [[1, 4, 8, 8, 8, 8, 8, 8, 8, 8], [3, 2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 0, 0, 0, 1, 0, 0, 0], [8, 8, 0, 1, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 1, 0, 1], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 1, 0, 0, 1, 0, 0], [8, 8, 0, 0, 0, 0, 0, 1, 0, 0], [8, 8, 0, 1, 0, 0, 1, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0, 0, 1]], "output": [[1, 4, 8, 8, 8, 8, 8, 8, 8, 8], [3, 2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 0, 0, 0, 4, 0, 0, 0], [8, 8, 0, 1, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 4, 0, 4], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 3, 0, 0, 2, 0, 0], [8, 8, 0, 0, 0, 0, 0, 2, 0, 0], [8, 8, 0, 3, 0, 0, 2, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0, 0, 2]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 8, 8], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [2, 0, 0, 0, 0, 0, 2, 2, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 8, 8], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 8, 8], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 2], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 8, 8], [0, 4, 0, 0, 0, 0, 0, 0, 6, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [4, 0, 0, 0, 0, 0, 6, 6, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 8, 8], [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 1, 0, 0, 0, 0, 0, 2, 0, 0, 8, 8], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 1, 0, 0, 0, 0, 2, 8, 8]]}], "test": [{"input": [[0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 8, 8], [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 8, 8], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 8, 8], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 4]], "output": [[0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 3, 0, 0, 0, 1, 0, 1, 0, 0, 8, 8], [3, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 8, 8], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [7, 0, 0, 0, 0, 7, 0, 0, 4, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 4, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 4, 4, 0, 0, 0, 8, 8], [0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 4]]}]}