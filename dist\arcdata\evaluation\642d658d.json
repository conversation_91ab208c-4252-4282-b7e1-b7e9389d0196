{"train": [{"input": [[0, 8, 8, 8, 3, 8, 0, 0, 8, 1, 0, 8, 0, 3, 8, 0, 1, 8, 0, 8, 8, 0, 0, 8], [1, 2, 8, 0, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 0, 0, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 8, 8, 0, 8], [8, 8, 8, 8, 1, 3, 8, 8, 8, 8, 8, 8, 8, 8, 2, 8, 8, 8, 3, 4, 3, 8, 8, 3], [0, 8, 8, 8, 2, 3, 0, 0, 8, 0, 0, 8, 0, 8, 8, 0, 0, 8, 8, 3, 8, 8, 8, 8], [8, 3, 8, 2, 4, 2, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8], [0, 8, 8, 8, 2, 8, 0, 0, 8, 8, 0, 8, 0, 8, 8, 0, 1, 8, 1, 8, 1, 0, 0, 8], [0, 1, 8, 1, 0, 8, 0, 0, 1, 8, 0, 3, 0, 8, 8, 0, 0, 8, 8, 0, 8, 8, 8, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 8, 8, 8], [0, 8, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 0, 8, 0, 0, 0, 8, 0, 8, 8, 0, 0, 8], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8], [0, 8, 8, 2, 0, 8, 0, 0, 8, 0, 0, 8, 0, 8, 8, 0, 0, 8, 2, 8, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 8, 0, 0, 8], [8, 3, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 0, 8, 8, 1, 8, 8, 8, 3, 8, 8, 8, 8], [0, 8, 8, 8, 1, 8, 0, 0, 8, 8, 0, 8, 0, 2, 8, 0, 0, 8, 8, 8, 8, 8, 8, 2], [0, 8, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 0, 8], [8, 8, 0, 8, 8, 8, 8, 8, 8, 1, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 0, 0, 8, 0, 0, 8], [0, 8, 1, 4, 1, 8, 0, 0, 8, 8, 0, 8, 0, 8, 8, 0, 0, 8, 8, 8, 8, 8, 8, 8], [8, 8, 0, 1, 8, 8, 3, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 8, 8, 8, 0, 8, 8, 3, 0, 8, 0, 0, 8, 0, 0, 8, 0, 8, 8, 0, 2, 8], [0, 0, 8, 8, 8, 8, 0, 8, 8, 8, 0, 3, 0, 0, 8, 8, 0, 8, 0, 8, 8, 0, 0, 8], [8, 3, 0, 8, 8, 8, 8, 3, 8, 8, 8, 8, 1, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8], [8, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 3, 2, 4, 2, 8, 3, 8, 8, 3, 8, 8], [0, 0, 8, 8, 0, 8, 3, 8, 3, 8, 1, 8, 0, 0, 3, 2, 8, 8, 0, 8, 8, 0, 0, 8]], "output": [[2]]}, {"input": [[0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 0, 0, 2, 2, 0, 2, 0, 8, 0, 0], [0, 2, 0, 2, 2, 0, 0, 2, 0, 2, 2, 3, 0, 0, 6, 2, 0, 2, 0, 2, 2, 0], [0, 0, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 0, 2, 2, 0, 1, 0, 2, 2, 0], [0, 2, 2, 7, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 2, 2], [2, 8, 6, 2, 2, 0, 3, 4, 3, 2, 2, 2, 2, 0, 2, 2, 2, 2, 1, 0, 2, 7], [2, 2, 2, 2, 2, 0, 2, 3, 2, 2, 2, 2, 2, 2, 0, 2, 2, 1, 4, 1, 2, 2], [2, 0, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 1, 0, 2, 2, 0, 2, 1, 2, 0, 0], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 0], [2, 1, 0, 2, 3, 2, 0, 2, 0, 2, 0, 2, 0, 0, 2, 2, 2, 2, 0, 2, 1, 0], [2, 2, 2, 3, 4, 3, 2, 2, 2, 3, 2, 2, 8, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 2, 0, 2, 3, 2, 0, 2, 2, 1, 0, 2, 2, 2, 0, 2, 0, 0, 0, 2, 3, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 6, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 2, 7, 2, 0, 2, 0, 0, 0, 0, 6, 4, 6, 2, 0, 2, 0, 2, 0, 0], [0, 0, 0, 2, 2, 2, 0, 2, 2, 2, 2, 0, 0, 6, 2, 2, 0, 2, 2, 2, 0, 6], [2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 2, 2, 8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 7, 2, 2, 2], [0, 0, 0, 2, 2, 8, 4, 8, 0, 2, 0, 2, 0, 0, 2, 2, 0, 7, 4, 7, 0, 0], [2, 2, 2, 0, 2, 0, 8, 2, 2, 2, 2, 2, 0, 2, 2, 2, 0, 0, 7, 2, 2, 2], [0, 0, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 6, 0, 2, 0, 2, 0, 0], [2, 1, 2, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 2, 2, 2, 0, 2, 0, 2, 0, 2, 0, 0, 2, 2, 0, 2, 0, 2, 7, 0], [0, 0, 0, 2, 2, 2, 0, 8, 0, 0, 2, 1, 0, 0, 2, 2, 0, 2, 0, 2, 0, 0]], "output": [[3]]}, {"input": [[0, 0, 5, 5, 5, 5, 0, 5, 0, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0], [0, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 3, 5, 5, 5, 5, 0, 5], [5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 5, 0, 3, 4, 3, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 3, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 8, 4, 8, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5, 0, 0], [5, 5, 0, 5, 8, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 8, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 0], [0, 0, 0, 5, 5, 5, 0, 5, 5, 8, 4, 8, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 8, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5], [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 0], [5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5]], "output": [[8]]}], "test": [{"input": [[0, 0, 9, 9, 0, 9, 0, 9, 0, 6, 0, 9, 0, 0, 9, 9, 0, 9, 0, 0, 9, 0], [0, 0, 9, 9, 9, 9, 3, 9, 9, 9, 0, 9, 0, 0, 9, 9, 0, 6, 0, 9, 9, 0], [9, 9, 2, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 0, 9], [9, 2, 4, 2, 9, 9, 9, 0, 9, 9, 0, 9, 0, 3, 9, 9, 9, 1, 9, 9, 2, 9], [9, 9, 2, 9, 9, 9, 6, 9, 9, 9, 6, 9, 9, 9, 2, 0, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 0, 9, 9, 0, 0, 9, 9, 9, 9, 0, 9, 9, 9, 9, 9, 9, 9], [0, 0, 9, 9, 9, 9, 0, 9, 9, 9, 0, 9, 3, 0, 9, 9, 0, 9, 0, 9, 9, 0], [9, 9, 9, 0, 9, 9, 9, 3, 9, 9, 9, 9, 0, 9, 9, 9, 9, 0, 9, 9, 9, 9], [6, 9, 9, 0, 9, 9, 3, 4, 3, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 0, 9, 9, 9, 3, 9, 3, 9, 9, 9, 9, 9, 9, 6, 9, 9, 0, 0, 3], [0, 0, 0, 1, 9, 9, 0, 9, 9, 9, 0, 9, 0, 0, 9, 6, 4, 6, 0, 9, 9, 0], [9, 9, 9, 9, 9, 9, 9, 0, 9, 9, 9, 0, 2, 9, 9, 9, 6, 9, 9, 0, 9, 1], [0, 0, 9, 9, 9, 9, 0, 9, 9, 9, 0, 9, 0, 0, 9, 9, 0, 9, 0, 9, 0, 0], [0, 0, 9, 2, 9, 9, 3, 9, 0, 6, 0, 9, 0, 0, 9, 9, 0, 9, 0, 9, 9, 0], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 0, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 0, 0, 9, 0, 9, 9, 9, 2, 9], [0, 6, 3, 9, 9, 9, 0, 9, 9, 0, 0, 9, 0, 3, 9, 9, 0, 2, 0, 0, 9, 0], [9, 9, 9, 9, 9, 9, 0, 3, 9, 0, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [0, 0, 9, 9, 9, 9, 0, 9, 9, 2, 0, 9, 0, 0, 9, 9, 0, 9, 0, 9, 9, 0], [9, 9, 9, 9, 0, 9, 9, 9, 9, 9, 9, 6, 0, 9, 9, 9, 9, 9, 9, 9, 6, 9], [9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 0, 9, 9, 9, 9, 3, 9, 9, 9, 9, 9, 9], [0, 0, 0, 1, 4, 1, 0, 9, 9, 0, 0, 9, 0, 0, 0, 9, 0, 9, 2, 9, 0, 0], [0, 0, 9, 9, 1, 9, 0, 9, 9, 9, 0, 9, 0, 0, 9, 9, 0, 9, 0, 9, 9, 0], [3, 9, 9, 9, 9, 0, 9, 9, 0, 9, 9, 9, 2, 9, 0, 9, 9, 9, 9, 0, 0, 9], [9, 9, 9, 9, 9, 9, 6, 9, 9, 9, 9, 2, 4, 2, 9, 9, 0, 9, 9, 9, 9, 9], [0, 0, 9, 9, 9, 9, 0, 0, 9, 1, 0, 9, 2, 0, 9, 9, 0, 9, 6, 9, 9, 0], [9, 9, 0, 0, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 0, 9, 9]], "output": [[2]]}]}