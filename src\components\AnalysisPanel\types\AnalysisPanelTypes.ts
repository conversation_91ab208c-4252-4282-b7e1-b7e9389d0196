import { PuzzleAnalysis } from '../../../types';

export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  className?: string;
}

export interface LevelHeaderProps {
  level: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  hasData: boolean;
  lastAnalysisTime?: string;
}

export interface AnalysisAccordionProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  expanded: boolean;
  onChange: (event: React.SyntheticEvent, isExpanded: boolean) => void;
  children: React.ReactNode;
}

export interface Level0PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export interface Level1PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export interface Level2PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export interface Level3PanelProps {
  analysis: PuzzleAnalysis;
}

export type LevelStatus = 'pending' | 'complete';