import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Stack,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Box,

} from '@mui/material';
import {
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Shuffle as RandomIcon,
  PlayArrow as DemoIcon,
  Brightness4 as ThemeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, useLoadARCPuzzlesQuery } from '../../store';
import {
  nextPuzzle,
  previousPuzzle,
  randomPuzzle,
  setSelectedSubset,
  setPuzzleList,
  setLoading,
  setCurrentPuzzleIndex,
} from '../../store/slices/puzzleSlice';
import { setDemoMode } from '../../store/slices/demoSlice';
import { setTheme } from '../../store/slices/settingsSlice';
import ApiKeyManager from '../ApiKeyManager/ApiKeyManager';
import { demoRunner } from '../../services/demoRunner';

const Header: React.FC = () => {
  const dispatch = useDispatch();
  const { currentPuzzleIndex, puzzleList, selectedSubset } = useSelector(
    (state: RootState) => state.puzzle
  );
  const { mode: demoMode, isRunning } = useSelector((state: RootState) => state.demo);
  const { theme } = useSelector((state: RootState) => state.settings);
  
  const [showDemoDialog, setShowDemoDialog] = useState(false);
  const [showApiKeyManager, setShowApiKeyManager] = useState(false);
  const [showPuzzleSelector, setShowPuzzleSelector] = useState(false);
  const [demoConfig, setDemoConfig] = useState(demoMode);
  const [puzzleSearchTerm, setPuzzleSearchTerm] = useState('');

  // Charger les puzzles ARC réels
  const { data: arcPuzzles, isLoading: isLoadingARC, error: arcError } = useLoadARCPuzzlesQuery(selectedSubset);

  // Mettre à jour la liste des puzzles quand les données ARC sont chargées
  React.useEffect(() => {
    if (arcPuzzles && arcPuzzles.length > 0) {
      dispatch(setPuzzleList(arcPuzzles));
      dispatch(setLoading(false));
    }
  }, [arcPuzzles, dispatch]);

  React.useEffect(() => {
    dispatch(setLoading(isLoadingARC));
  }, [isLoadingARC, dispatch]);

  const handleSubsetChange = (newSubset: 'training' | 'evaluation' | 'both') => {
    dispatch(setSelectedSubset(newSubset));
    dispatch(setLoading(true));
  };

  const handlePuzzleSelect = (puzzleId: string) => {
    if (!puzzleId || !puzzleList) return;
    
    const puzzleIndex = puzzleList.findIndex(puzzle => puzzle.id === puzzleId);
    if (puzzleIndex !== -1) {
      dispatch(setCurrentPuzzleIndex(puzzleIndex));
      setShowPuzzleSelector(false);
      setPuzzleSearchTerm('');
    }
  };

  const filteredPuzzles = puzzleList.filter(puzzle => 
    puzzle.id.toLowerCase().includes(puzzleSearchTerm.toLowerCase())
  );

  const handleStartDemo = async () => {
    try {
      // Vérifier qu'il y a des puzzles chargés
      if (!puzzleList || puzzleList.length === 0) {
        alert('Aucun puzzle chargé. Veuillez d\'abord charger un dataset.');
        return;
      }

      // Filtrer les puzzles selon le subset sélectionné
      let puzzlesToProcess = puzzleList;
      if (demoConfig.selectedSubset !== 'both') {
        // Ici on pourrait filtrer selon le subset, pour l'instant on prend tous
        puzzlesToProcess = puzzleList.slice(0, Math.min(10, puzzleList.length)); // Limiter à 10 pour les tests
      }

      // Mettre à jour le store Redux
      dispatch(setDemoMode(demoConfig));

      // Démarrer la demo avec le nouveau DemoRunner
      await demoRunner.startDemo(demoConfig, puzzlesToProcess);

      setShowDemoDialog(false);
    } catch (error) {
      console.error('Erreur lors du démarrage de la demo:', error);
      alert(`Erreur: ${(error as Error).message}`);
    }
  };

  const cycleTheme = () => {
    const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    dispatch(setTheme(themes[nextIndex]));
  };

  return (
    <>
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ mr: 3 }}>
            ARC AGI Testing
          </Typography>

          {/* Puzzle Selector */}
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mr: 3 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel sx={{ color: 'white' }}>Dataset</InputLabel>
              <Select
                value={selectedSubset}
                label="Dataset"
                onChange={(e) => handleSubsetChange(e.target.value as any)}
                sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'rgba(255,255,255,0.3)' } }}
                disabled={isLoadingARC}
              >
                <MenuItem value="training">Training</MenuItem>
                <MenuItem value="evaluation">Evaluation</MenuItem>
                <MenuItem value="both">Both</MenuItem>
              </Select>
            </FormControl>



            <Chip
              label={isLoadingARC ? 'Chargement...' : `${currentPuzzleIndex + 1} / ${puzzleList.length}`}
              variant="outlined"
              sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)' }}
            />
            {arcError && (
              <Chip
                label="Erreur chargement"
                variant="outlined"
                color="error"
                sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)' }}
              />
            )}
          </Stack>

          {/* Navigation */}
          <Stack direction="row" spacing={1} sx={{ mr: 3 }}>
            <IconButton
              color="inherit"
              onClick={() => dispatch(previousPuzzle())}
              disabled={currentPuzzleIndex <= 0}
            >
              <PrevIcon />
            </IconButton>
            
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                console.log('Puzzle selector clicked', { puzzleList: puzzleList.length, currentIndex: currentPuzzleIndex });
                setShowPuzzleSelector(true);
              }}
              disabled={false}
              sx={{ 
                color: 'white', 
                borderColor: 'rgba(255,255,255,0.3)',
                '&:hover': { borderColor: 'rgba(255,255,255,0.5)' },
                minWidth: '100px',
                backgroundColor: 'rgba(255,255,255,0.1)'
              }}
            >
              {puzzleList[currentPuzzleIndex]?.id || `Puzzle (${puzzleList.length})`}
            </Button>
            
            <IconButton
              color="inherit"
              onClick={() => dispatch(nextPuzzle())}
              disabled={currentPuzzleIndex >= puzzleList.length - 1}
            >
              <NextIcon />
            </IconButton>
            <IconButton
              color="inherit"
              onClick={() => dispatch(randomPuzzle())}
              disabled={puzzleList.length === 0}
            >
              <RandomIcon />
            </IconButton>
          </Stack>

          <Box sx={{ flexGrow: 1 }} />

          {/* Demo Button */}
          <Button
            variant={isRunning ? 'outlined' : 'contained'}
            color={isRunning ? 'warning' : 'secondary'}
            startIcon={<DemoIcon />}
            onClick={() => setShowDemoDialog(true)}
            sx={{ mr: 2 }}
            disabled={isRunning}
          >
            {isRunning ? 'Demo Active' : 'Demo'}
          </Button>

          {/* Theme Toggle */}
          <IconButton color="inherit" onClick={cycleTheme}>
            <ThemeIcon />
          </IconButton>

          {/* Settings */}
          <IconButton
            color="inherit"
            onClick={() => setShowApiKeyManager(true)}
            title="Configuration des clés API"
          >
            <SettingsIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* API Key Manager Dialog */}
      <ApiKeyManager
        open={showApiKeyManager}
        onClose={() => setShowApiKeyManager(false)}
      />

      {/* Demo Configuration Dialog */}
      <Dialog open={showDemoDialog} onClose={() => setShowDemoDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Configuration Demo</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Dataset</InputLabel>
              <Select
                value={demoConfig.selectedSubset}
                label="Dataset"
                onChange={(e) => setDemoConfig({ ...demoConfig, selectedSubset: e.target.value as any })}
              >
                <MenuItem value="training">Training</MenuItem>
                <MenuItem value="evaluation">Evaluation</MenuItem>
                <MenuItem value="both">Both</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Fournisseur IA</InputLabel>
              <Select
                value={demoConfig.selectedProvider}
                label="Fournisseur IA"
                onChange={(e) => setDemoConfig({ ...demoConfig, selectedProvider: e.target.value })}
              >
                <MenuItem value="openai">OpenAI</MenuItem>
                <MenuItem value="groq">Groq</MenuItem>
                <MenuItem value="ollama">Ollama</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Délai entre puzzles (secondes)"
              type="number"
              value={demoConfig.autoAdvanceDelay}
              onChange={(e) => setDemoConfig({ ...demoConfig, autoAdvanceDelay: parseInt(e.target.value) || 0 })}
              fullWidth
            />

            <TextField
              label="Taille du batch"
              type="number"
              value={demoConfig.batchSize}
              onChange={(e) => setDemoConfig({ ...demoConfig, batchSize: parseInt(e.target.value) || 1 })}
              fullWidth
            />

            <FormControlLabel
              control={
                <Switch
                  checked={demoConfig.stopOnError}
                  onChange={(e) => setDemoConfig({ ...demoConfig, stopOnError: e.target.checked })}
                />
              }
              label="Arrêter en cas d'erreur"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDemoDialog(false)}>Annuler</Button>
          <Button onClick={handleStartDemo} variant="contained">
            Démarrer Demo
          </Button>
        </DialogActions>
      </Dialog>

      {/* Puzzle Selector Dialog */}
      <Dialog open={showPuzzleSelector} onClose={() => setShowPuzzleSelector(false)} maxWidth="md" fullWidth>
        <DialogTitle>Sélectionner un puzzle</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Rechercher par ID"
            fullWidth
            variant="outlined"
            value={puzzleSearchTerm}
            onChange={(e) => setPuzzleSearchTerm(e.target.value)}
            sx={{ mb: 2 }}
          />
          <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
            <Stack spacing={1}>
              {filteredPuzzles.slice(0, 50).map((puzzle) => (
                <Button
                  key={puzzle.id}
                  variant={puzzle.id === puzzleList[currentPuzzleIndex]?.id ? "contained" : "outlined"}
                  onClick={() => handlePuzzleSelect(puzzle.id)}
                  sx={{ justifyContent: 'flex-start' }}
                >
                  {puzzle.id} {puzzle.id === puzzleList[currentPuzzleIndex]?.id && '(actuel)'}
                </Button>
              ))}
              {filteredPuzzles.length > 50 && (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
                  {filteredPuzzles.length - 50} autres puzzles... Affinez votre recherche.
                </Typography>
              )}
            </Stack>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPuzzleSelector(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Header;