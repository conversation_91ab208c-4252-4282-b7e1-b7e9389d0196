{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 8, 8, 0, 0, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0], [0, 0, 0, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 0, 0, 8, 0, 0], [0, 8, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 0, 8, 0, 0, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 0, 0], [0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 4, 4, 0, 4, 0, 0, 8, 8, 0, 0, 4, 0, 4, 4, 0, 8, 8, 0], [0, 8, 8, 0, 4, 4, 0, 0, 8, 0, 4, 4, 0, 8, 8, 0, 0, 8, 0], [0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 4, 0, 0, 8, 8, 0, 8, 8, 0], [0, 0, 0, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 0, 0, 8, 0, 0], [0, 8, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 0, 8, 0, 0, 8, 0], [0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 0, 0, 8, 0, 0], [0, 4, 4, 0, 4, 4, 0, 8, 0, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 0, 8, 0], [0, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 8, 8, 8, 8, 8, 8, 4, 4, 4, 0], [0, 4, 0, 4, 8, 8, 8, 8, 8, 8, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 4, 4, 4, 8, 8, 4, 4, 0, 4, 4, 0], [0, 4, 4, 4, 4, 4, 8, 8, 4, 4, 0, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 0, 8, 0], [0, 8, 8, 8, 8, 8, 6, 6, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 8, 8, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 0, 8, 8, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 8, 0, 8, 8, 8, 8, 4, 4, 4, 0], [0, 4, 4, 4, 8, 8, 8, 0, 8, 8, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0], [0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 8, 8, 0], [0, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8, 0, 0, 0, 8, 0, 8, 8, 0, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 4, 4, 0, 8, 0, 0, 4, 4, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 0, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 6, 6, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 0, 4, 0, 8, 8, 0, 8, 8, 0], [0, 8, 8, 0, 8, 8, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 8, 0, 0, 8, 8, 0], [0, 8, 8, 0, 4, 0, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 8, 0, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0], [0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0], [0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 8, 8, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0], [0, 4, 4, 0, 4, 4, 0, 4, 0, 0, 8, 8, 0, 4, 4, 0, 4, 0, 0, 4, 4, 0], [0, 4, 4, 0, 4, 0, 0, 4, 4, 0, 8, 0, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 6, 6, 0, 8, 8, 8, 8, 0, 0, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 8, 0, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 0], [0, 8, 8, 8, 0, 8, 8, 0, 0, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 4, 0, 4, 4, 0], [0, 4, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 4, 8, 8, 8, 8, 0, 8, 4, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 8, 8, 8, 8, 8, 8, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 4, 4, 4, 8, 8, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 0, 4, 4, 4, 4, 8, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 6, 6, 0, 8, 8, 8, 8, 0, 0, 8, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 8, 0, 8, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 4, 0, 0, 8, 8, 4, 4, 4, 4, 0, 0, 4, 4, 0], [0, 4, 4, 4, 4, 4, 4, 4, 4, 8, 8, 4, 4, 4, 4, 4, 4, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 4, 8, 8, 8, 8, 8, 8, 4, 4, 4, 4, 0, 4, 0], [0, 4, 4, 4, 0, 4, 4, 0, 0, 8, 8, 0, 8, 4, 4, 4, 4, 0, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 0], [0, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}