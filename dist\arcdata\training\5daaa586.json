{"train": [{"input": [[0, 0, 0, 0, 0, 3, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 2, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 8, 0, 0, 2, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 3, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 2, 2, 2, 2, 2], [0, 0, 0, 0, 0, 3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 2, 0]], "output": [[3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8], [3, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 8], [3, 0, 0, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 0, 0, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 0, 0, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 0, 0, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 0, 2, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 0, 2, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 2, 2, 0, 2, 0, 0, 0, 0, 2, 2, 8], [3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8]]}, {"input": [[0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0], [8, 8, 4, 8, 8, 8, 8, 8, 8, 1, 8, 8], [0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 4, 0, 0, 0, 8, 0, 0, 1, 0, 8], [0, 0, 4, 8, 0, 0, 8, 0, 0, 1, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 4, 0, 0, 0, 0, 8, 0, 1, 0, 8], [6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6], [0, 0, 4, 0, 0, 0, 8, 0, 0, 1, 0, 0], [0, 8, 4, 0, 0, 0, 0, 8, 0, 1, 0, 0]], "output": [[4, 8, 8, 8, 8, 8, 8, 1], [4, 8, 0, 0, 8, 8, 0, 1], [4, 8, 0, 0, 8, 8, 0, 1], [4, 8, 0, 0, 8, 8, 0, 1], [4, 8, 0, 0, 8, 8, 0, 1], [4, 0, 0, 0, 0, 8, 0, 1], [4, 0, 0, 0, 0, 8, 0, 1], [6, 6, 6, 6, 6, 6, 6, 1]]}, {"input": [[0, 0, 4, 3, 0, 0, 0, 4, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 0, 4, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 4, 4, 4, 0, 4], [2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 4, 2, 2, 2], [4, 0, 0, 3, 4, 4, 0, 4, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [4, 0, 0, 3, 0, 0, 0, 0, 4, 0, 4, 4, 0, 0, 0], [4, 0, 0, 3, 0, 0, 4, 0, 0, 0, 4, 4, 0, 0, 0], [8, 8, 8, 3, 8, 8, 8, 8, 8, 8, 8, 4, 8, 8, 8], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 4, 4, 0, 0, 4], [0, 0, 0, 3, 4, 0, 0, 4, 0, 0, 0, 4, 0, 0, 0], [0, 0, 4, 3, 0, 0, 0, 0, 0, 4, 0, 4, 0, 0, 0]], "output": [[3, 2, 2, 2, 2, 2, 2, 2, 4], [3, 4, 4, 4, 4, 4, 4, 4, 4], [3, 0, 0, 0, 0, 0, 0, 0, 4], [3, 0, 0, 0, 0, 4, 4, 4, 4], [3, 0, 0, 4, 4, 4, 4, 4, 4], [3, 8, 8, 8, 8, 8, 8, 8, 4]]}], "test": [{"input": [[0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 2, 0, 0], [3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 3, 2, 3, 3], [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 2, 0, 0], [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 1], [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0], [0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 2, 0, 0], [0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 2, 0, 0]], "output": [[1, 3, 3, 3, 3, 3, 3, 3, 3, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 1, 1, 1, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 1, 0, 0, 0, 2], [1, 1, 1, 1, 1, 1, 1, 1, 0, 2], [1, 1, 1, 1, 1, 1, 1, 0, 0, 2], [1, 1, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}]}