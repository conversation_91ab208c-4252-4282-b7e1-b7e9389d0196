{"test": [{"input": [[4, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 1, 1, 2, 2, 3, 3, 4], [3, 4, 3, 3, 4, 4, 4, 5, 5, 5, 0, 0, 6, 7, 7, 7, 8, 8, 8, 9, 9], [4, 3, 4, 3, 3, 3, 4, 4, 4, 4, 0, 0, 5, 5, 6, 6, 6, 6, 7, 7, 7], [4, 3, 3, 4, 3, 3, 3, 3, 4, 4, 0, 0, 4, 5, 5, 5, 5, 5, 6, 6, 6], [5, 4, 3, 3, 4, 3, 3, 3, 3, 3, 0, 0, 4, 4, 4, 4, 5, 5, 5, 5, 5], [5, 4, 3, 3, 3, 4, 3, 3, 3, 3, 0, 0, 4, 4, 4, 4, 4, 4, 4, 5, 5], [6, 4, 4, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4], [6, 5, 4, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4], [0, 0, 0, 0, 0, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4], [8, 6, 5, 4, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 6, 5, 4, 4, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3], [9, 6, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3], [9, 7, 5, 5, 4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3], [1, 7, 6, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3], [1, 7, 6, 5, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 3], [2, 8, 6, 5, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 3], [2, 8, 6, 5, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3], [3, 8, 7, 6, 5, 4, 4, 4, 4, 3, 3, 3, 0, 0, 0, 0, 0, 3, 4, 3, 3], [3, 9, 7, 6, 5, 5, 4, 4, 4, 3, 3, 3, 0, 0, 0, 0, 0, 3, 3, 4, 3], [4, 9, 7, 6, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4]], "output": [[4, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 1, 1, 2, 2, 3, 3, 4], [3, 4, 3, 3, 4, 4, 4, 5, 5, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8, 9, 9], [4, 3, 4, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7], [4, 3, 3, 4, 3, 3, 3, 3, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 6, 6, 6], [5, 4, 3, 3, 4, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5], [5, 4, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4, 5, 5], [6, 4, 4, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 4], [6, 5, 4, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4], [7, 5, 4, 4, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4], [7, 5, 4, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4], [8, 6, 5, 4, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 6, 5, 4, 4, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3], [9, 6, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3], [9, 7, 5, 5, 4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3], [1, 7, 6, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3], [1, 7, 6, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3], [2, 8, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3], [2, 8, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3], [3, 8, 7, 6, 5, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3], [3, 9, 7, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3], [4, 9, 7, 6, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4]]}], "train": [{"input": [[1, 6, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 1, 1, 2, 2, 3, 3, 4], [6, 1, 6, 6, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 6, 6], [1, 6, 1, 6, 6, 6, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4], [1, 6, 6, 1, 6, 6, 6, 6, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3], [2, 1, 6, 6, 1, 6, 6, 6, 6, 6, 1, 0, 0, 0, 1, 1, 2, 2, 2, 2, 2], [2, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2], [3, 1, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1, 1, 1], [3, 2, 1, 6, 6, 6, 6, 1, 0, 0, 0, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1], [4, 2, 1, 1, 6, 6, 6, 6, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 1, 1, 1], [4, 2, 1, 1, 6, 6, 6, 6, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1], [5, 3, 2, 1, 1, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [5, 3, 2, 1, 1, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6], [6, 3, 2, 1, 1, 1, 6, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6], [6, 4, 2, 2, 1, 1, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 6, 6, 6, 6], [1, 4, 3, 2, 1, 1, 1, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 6, 6, 6, 6], [1, 4, 3, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6], [2, 5, 3, 2, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6], [2, 5, 3, 2, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6], [3, 5, 4, 3, 2, 1, 1, 1, 1, 6, 0, 0, 0, 0, 6, 6, 6, 6, 1, 6, 6], [3, 6, 4, 3, 2, 2, 1, 1, 1, 6, 0, 0, 0, 0, 6, 6, 6, 6, 6, 1, 6], [4, 6, 4, 3, 2, 2, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1]], "output": [[1, 6, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 1, 1, 2, 2, 3, 3, 4], [6, 1, 6, 6, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 6, 6], [1, 6, 1, 6, 6, 6, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4], [1, 6, 6, 1, 6, 6, 6, 6, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3], [2, 1, 6, 6, 1, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2], [2, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1, 1, 1, 2, 2], [3, 1, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1, 1, 1], [3, 2, 1, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 1, 1, 1, 1, 1], [4, 2, 1, 1, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 1, 1], [4, 2, 1, 1, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1], [5, 3, 2, 1, 1, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [5, 3, 2, 1, 1, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 3, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6, 6], [6, 4, 2, 2, 1, 1, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6], [1, 4, 3, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6], [1, 4, 3, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6, 6], [2, 5, 3, 2, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 6], [2, 5, 3, 2, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6], [3, 5, 4, 3, 2, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6], [3, 6, 4, 3, 2, 2, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1, 6], [4, 6, 4, 3, 2, 2, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 1]]}, {"input": [[6, 5, 6, 6, 7, 7, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 1], [5, 6, 5, 5, 6, 6, 6, 7, 7, 7, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4], [6, 5, 6, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 1, 1, 1, 1, 2, 2, 2], [6, 5, 5, 0, 0, 0, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 1, 1, 1], [7, 6, 5, 0, 0, 0, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7], [7, 6, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 7, 7], [1, 6, 6, 0, 0, 0, 6, 5, 5, 0, 0, 0, 5, 5, 6, 6, 6, 6, 6, 6, 6], [1, 7, 6, 0, 0, 0, 5, 6, 5, 0, 0, 0, 5, 5, 5, 5, 6, 6, 6, 6, 6], [2, 7, 6, 6, 5, 5, 5, 5, 6, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 6, 6], [2, 7, 6, 6, 5, 5, 5, 5, 5, 6, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 6], [3, 1, 7, 6, 6, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 1, 7, 6, 6, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5], [4, 1, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5], [4, 2, 7, 7, 6, 6, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5], [5, 2, 0, 0, 0, 0, 6, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5], [5, 2, 0, 0, 0, 0, 6, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5], [6, 3, 1, 7, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5], [6, 3, 1, 7, 7, 6, 6, 6, 0, 0, 0, 0, 5, 5, 5, 5, 5, 6, 5, 5, 5], [7, 3, 2, 1, 7, 6, 6, 6, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 6, 5, 5], [7, 4, 2, 1, 7, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5], [1, 4, 2, 1, 7, 7, 6, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6]], "output": [[6, 5, 6, 6, 7, 7, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 1], [5, 6, 5, 5, 6, 6, 6, 7, 7, 7, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4], [6, 5, 6, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 1, 1, 1, 1, 2, 2, 2], [6, 5, 5, 6, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 1, 1, 1], [7, 6, 5, 5, 6, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7], [7, 6, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 7, 7], [1, 6, 6, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6], [1, 7, 6, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6], [2, 7, 6, 6, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6], [2, 7, 6, 6, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6], [3, 1, 7, 6, 6, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 1, 7, 6, 6, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5], [4, 1, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5], [4, 2, 7, 7, 6, 6, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5], [5, 2, 1, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5], [5, 2, 1, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5], [6, 3, 1, 7, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5], [6, 3, 1, 7, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5], [7, 3, 2, 1, 7, 6, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5], [7, 4, 2, 1, 7, 7, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5], [1, 4, 2, 1, 7, 7, 6, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6]]}, {"input": [[5, 4, 5, 5, 6, 6, 7, 7, 8, 8, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6], [4, 5, 4, 4, 5, 5, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8, 1, 1, 1, 2, 2], [5, 4, 5, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 0, 0, 8, 8, 8], [5, 4, 4, 5, 4, 4, 4, 4, 5, 5, 5, 5, 5, 6, 6, 6, 0, 0, 7, 7, 7], [6, 5, 4, 4, 5, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 0, 0, 6, 6, 6], [6, 5, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 5, 6, 6], [7, 5, 5, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 5, 5, 0, 0, 5, 5, 5], [7, 6, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5], [8, 6, 5, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5], [8, 6, 5, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5], [1, 7, 6, 5, 5, 4, 4, 4, 4, 4, 5, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4], [1, 7, 6, 5, 5, 4, 4, 4, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4], [2, 7, 6, 5, 5, 5, 4, 4, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4], [2, 8, 6, 6, 5, 5, 4, 4, 4, 4, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [3, 8, 7, 6, 5, 5, 5, 4, 4, 0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [3, 8, 7, 6, 5, 5, 5, 4, 4, 0, 0, 0, 0, 0, 0, 5, 4, 4, 4, 4, 4], [4, 1, 7, 6, 6, 5, 5, 5, 4, 0, 0, 0, 4, 4, 4, 4, 5, 4, 4, 4, 4], [4, 1, 7, 6, 6, 5, 5, 5, 4, 0, 0, 0, 4, 4, 4, 4, 4, 5, 4, 4, 4], [5, 1, 8, 7, 6, 5, 5, 5, 5, 0, 0, 0, 4, 4, 4, 4, 4, 4, 5, 4, 4], [5, 2, 8, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4], [6, 2, 8, 7, 6, 6, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5]], "output": [[5, 4, 5, 5, 6, 6, 7, 7, 8, 8, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6], [4, 5, 4, 4, 5, 5, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8, 1, 1, 1, 2, 2], [5, 4, 5, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8], [5, 4, 4, 5, 4, 4, 4, 4, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7], [6, 5, 4, 4, 5, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6], [6, 5, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 6, 6], [7, 5, 5, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5], [7, 6, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5], [8, 6, 5, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5], [8, 6, 5, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5], [1, 7, 6, 5, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 7, 6, 5, 5, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4], [2, 7, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4], [2, 8, 6, 6, 5, 5, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4], [3, 8, 7, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4], [3, 8, 7, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4], [4, 1, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4], [4, 1, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4, 4], [5, 1, 8, 7, 6, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4, 4], [5, 2, 8, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 4], [6, 2, 8, 7, 6, 6, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5]]}]}