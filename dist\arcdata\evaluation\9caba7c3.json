{"train": [{"input": [[0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0], [0, 5, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0], [5, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 5], [0, 5, 0, 5, 2, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 2, 5, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 0, 5], [0, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 5, 0, 5, 0, 2, 5, 5, 0, 0, 5, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 2, 5, 5, 0, 5, 5, 5, 5, 0], [5, 5, 2, 2, 2, 5, 0, 0, 0, 0, 5, 2, 2, 5, 5, 5, 0, 0, 5], [5, 0, 5, 5, 2, 5, 5, 5, 0, 0, 0, 5, 5, 0, 5, 5, 5, 0, 5], [0, 0, 2, 5, 2, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5], [0, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0], [5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 2, 5, 0, 5, 5], [5, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5], [5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5, 0]], "output": [[0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0], [0, 5, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0], [5, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 5, 7, 7, 7, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 5], [0, 5, 0, 5, 2, 4, 7, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0], [5, 5, 5, 5, 7, 2, 7, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 0, 5], [0, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 5, 0, 5, 0, 2, 7, 7, 0, 0, 5, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 2, 4, 7, 0, 5, 5, 5, 5, 0], [5, 5, 2, 2, 2, 5, 0, 0, 0, 0, 7, 2, 2, 5, 5, 5, 0, 0, 5], [5, 0, 7, 4, 2, 5, 5, 5, 0, 0, 0, 5, 5, 0, 5, 5, 5, 0, 5], [0, 0, 2, 7, 2, 5, 0, 5, 0, 0, 0, 0, 5, 7, 7, 7, 0, 0, 5], [0, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5, 7, 4, 7, 0, 5, 0], [5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 0, 7, 2, 7, 0, 5, 5], [5, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5], [5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5, 0]]}, {"input": [[5, 5, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5], [0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 5, 2, 2, 5, 5, 5, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 2, 5, 5, 0, 5, 0, 0, 0], [5, 0, 5, 2, 2, 5, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5], [0, 5, 5, 5, 2, 5, 0, 0, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5], [0, 0, 2, 2, 2, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 5], [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 0, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 5, 0, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 2, 2, 5], [0, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 5, 5, 5], [5, 0, 0, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 2], [5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5], [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 2, 5, 2, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 0, 2, 2, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 5, 5, 0], [0, 5, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 5, 5, 0, 5, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 5, 5]], "output": [[5, 5, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5], [0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 5, 2, 2, 7, 5, 5, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 2, 4, 7, 0, 5, 0, 0, 0], [5, 0, 7, 2, 2, 5, 5, 0, 0, 5, 5, 7, 7, 7, 5, 0, 5, 5, 5], [0, 5, 7, 4, 2, 5, 0, 0, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5], [0, 0, 2, 2, 2, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 5], [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 0, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 5, 0, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 2, 2, 7], [0, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 7, 4, 7], [5, 0, 0, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 0, 5, 7, 7, 2], [5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5], [0, 0, 5, 5, 7, 7, 7, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 2, 4, 2, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 0, 2, 2, 7, 5, 5, 0, 0, 0, 0, 5, 5, 0, 5, 5, 0], [0, 5, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 5, 5, 0, 5, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 5, 5]]}, {"input": [[0, 0, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 2, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 2, 2, 5, 5, 0], [5, 5, 5, 2, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5, 2, 5, 2, 5, 5], [0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5, 2, 5, 0, 0], [0, 5, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 0, 0, 5, 5, 5, 5, 5], [5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5], [5, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 0, 0], [0, 5, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 5], [5, 5, 5, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5], [0, 5, 2, 2, 5, 5, 0, 0, 5, 0, 0, 5, 2, 5, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 2, 5, 0, 0, 0, 5], [0, 5, 5, 5, 2, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0], [5, 0, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 0], [5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 5], [5, 0, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 5]], "output": [[0, 0, 7, 7, 7, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 2, 4, 7, 5, 5, 5, 5, 0, 5, 0, 0, 0, 2, 2, 7, 5, 0], [5, 5, 7, 2, 7, 0, 5, 5, 5, 0, 5, 5, 0, 5, 2, 4, 2, 5, 5], [0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 7, 2, 7, 0, 0], [0, 5, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 0, 0, 5, 5, 5, 5, 5], [5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5], [5, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 0, 0], [0, 5, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 5], [5, 5, 5, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5], [0, 5, 2, 2, 7, 5, 0, 0, 5, 0, 0, 7, 2, 7, 5, 5, 0, 5, 5], [5, 5, 7, 4, 7, 0, 0, 5, 0, 0, 0, 7, 4, 2, 5, 0, 0, 0, 5], [0, 5, 7, 7, 2, 0, 0, 5, 5, 5, 5, 7, 7, 7, 0, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0], [5, 0, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0], [5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 0], [5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 5], [5, 0, 5, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 5]]}], "test": [{"input": [[5, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 0, 5], [0, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5], [0, 5, 5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 2, 5, 5, 5, 0, 5], [5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 2, 2, 5, 5, 0, 0], [5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 0, 5, 5], [5, 2, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 5], [5, 5, 2, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5], [5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 0], [5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5], [5, 5, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5], [5, 5, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 5, 2, 5], [5, 5, 5, 5, 5, 2, 5, 5, 5, 5, 0, 5, 0, 0, 5, 0, 2, 5, 5], [5, 0, 5, 0, 5, 5, 2, 5, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 2, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 5], [5, 5, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5]], "output": [[5, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 0, 5], [0, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 7, 7, 7, 5, 0, 5], [0, 5, 5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 2, 4, 7, 5, 0, 5], [5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 2, 2, 7, 5, 0, 0], [5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 0, 5, 5], [7, 2, 7, 0, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 5], [7, 4, 2, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5], [7, 7, 7, 0, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 0], [5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5], [5, 5, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5], [5, 5, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 0, 0, 5, 5, 7, 2, 7], [5, 5, 5, 5, 7, 2, 7, 5, 5, 5, 0, 5, 0, 0, 5, 0, 2, 4, 7], [5, 0, 5, 0, 7, 4, 2, 5, 0, 0, 5, 0, 0, 5, 0, 5, 7, 7, 7], [5, 5, 5, 5, 7, 2, 7, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 5], [5, 5, 0, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5]]}]}