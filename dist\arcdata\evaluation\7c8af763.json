{"train": [{"input": [[1, 5, 1, 5, 5, 2, 5, 5, 5, 1], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [0, 0, 0, 5, 0, 0, 0, 2, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [5, 2, 5, 5, 5, 5, 5, 5, 2, 5], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [0, 0, 0, 1, 0, 0, 0, 5, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [5, 2, 5, 5, 5, 5, 1, 5, 5, 2]], "output": [[1, 5, 1, 5, 5, 2, 5, 5, 5, 1], [1, 1, 1, 5, 2, 2, 2, 5, 2, 2], [1, 1, 1, 5, 2, 2, 2, 2, 2, 2], [1, 1, 1, 5, 2, 2, 2, 5, 2, 2], [5, 2, 5, 5, 5, 5, 5, 5, 2, 5], [2, 2, 2, 5, 1, 1, 1, 5, 2, 2], [2, 2, 2, 1, 1, 1, 1, 5, 2, 2], [2, 2, 2, 5, 1, 1, 1, 5, 2, 2], [2, 2, 2, 5, 1, 1, 1, 5, 2, 2], [5, 2, 5, 5, 5, 5, 1, 5, 5, 2]]}, {"input": [[2, 5, 5, 5, 1, 5, 5, 5, 5, 2], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 1, 0, 0, 0, 0, 1, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [5, 1, 5, 5, 5, 2, 5, 5, 5, 1], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 2, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [1, 5, 5, 5, 2, 5, 5, 5, 5, 1]], "output": [[2, 5, 5, 5, 1, 5, 5, 5, 5, 2], [1, 1, 5, 1, 1, 1, 1, 5, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 5, 1, 1, 1, 1, 5, 1, 1], [1, 1, 5, 1, 1, 1, 1, 5, 1, 1], [5, 1, 5, 5, 5, 2, 5, 5, 5, 1], [1, 1, 5, 2, 2, 2, 2, 5, 1, 1], [1, 1, 5, 2, 2, 2, 2, 2, 1, 1], [1, 1, 5, 2, 2, 2, 2, 5, 1, 1], [1, 5, 5, 5, 2, 5, 5, 5, 5, 1]]}, {"input": [[1, 5, 2, 5, 2, 5, 5, 5, 5, 1], [0, 0, 0, 5, 0, 0, 2, 0, 0, 0], [0, 0, 0, 5, 0, 0, 5, 0, 0, 0], [5, 2, 5, 5, 5, 1, 5, 5, 2, 5], [0, 0, 0, 5, 0, 0, 2, 0, 0, 0], [0, 0, 0, 1, 0, 0, 5, 0, 0, 0], [2, 2, 5, 5, 1, 5, 5, 5, 1, 5], [0, 0, 0, 5, 0, 0, 1, 0, 0, 0], [0, 0, 0, 5, 0, 0, 5, 0, 0, 0], [5, 5, 1, 5, 5, 1, 5, 5, 2, 5]], "output": [[1, 5, 2, 5, 2, 5, 5, 5, 5, 1], [2, 2, 2, 5, 2, 2, 2, 2, 2, 2], [2, 2, 2, 5, 2, 2, 5, 2, 2, 2], [5, 2, 5, 5, 5, 1, 5, 5, 2, 5], [2, 2, 2, 5, 1, 1, 2, 2, 2, 2], [2, 2, 2, 1, 1, 1, 5, 2, 2, 2], [2, 2, 5, 5, 1, 5, 5, 5, 1, 5], [2, 2, 2, 5, 1, 1, 1, 1, 1, 1], [2, 2, 2, 5, 1, 1, 5, 1, 1, 1], [5, 5, 1, 5, 5, 1, 5, 5, 2, 5]]}], "test": [{"input": [[5, 1, 5, 5, 5, 5, 5, 5, 1, 5], [0, 0, 5, 0, 0, 5, 0, 0, 0, 0], [0, 0, 5, 0, 0, 1, 0, 0, 0, 0], [5, 5, 5, 5, 1, 5, 5, 2, 1, 5], [0, 0, 2, 0, 0, 2, 0, 0, 0, 0], [0, 0, 5, 0, 0, 5, 0, 0, 0, 0], [2, 5, 5, 5, 2, 5, 5, 1, 2, 5], [0, 0, 1, 0, 0, 2, 0, 0, 0, 0], [0, 0, 5, 0, 0, 5, 0, 0, 0, 0], [5, 2, 5, 5, 5, 5, 1, 5, 5, 1]], "output": [[5, 1, 5, 5, 5, 5, 5, 5, 1, 5], [1, 1, 5, 1, 1, 5, 1, 1, 1, 1], [1, 1, 5, 1, 1, 1, 1, 1, 1, 1], [5, 5, 5, 5, 1, 5, 5, 2, 1, 5], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 5, 2, 2, 5, 2, 2, 2, 2], [2, 5, 5, 5, 2, 5, 5, 1, 2, 5], [2, 2, 1, 2, 2, 2, 1, 1, 1, 1], [2, 2, 5, 2, 2, 5, 1, 1, 1, 1], [5, 2, 5, 5, 5, 5, 1, 5, 5, 1]]}]}