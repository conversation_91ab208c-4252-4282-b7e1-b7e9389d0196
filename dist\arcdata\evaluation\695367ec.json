{"train": [{"input": [[3, 3, 3], [3, 3, 3], [3, 3, 3]], "output": [[0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0]]}, {"input": [[8, 8], [8, 8]], "output": [[0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[2, 2, 2, 2], [2, 2, 2, 2], [2, 2, 2, 2], [2, 2, 2, 2]], "output": [[0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[4, 4, 4, 4, 4], [4, 4, 4, 4, 4], [4, 4, 4, 4, 4], [4, 4, 4, 4, 4], [4, 4, 4, 4, 4]], "output": [[0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0, 0]]}]}