{"train": [{"input": [[9, 4, 0, 0, 4, 9, 0, 0, 9, 9], [4, 9, 9, 4, 9, 9, 0, 0, 9, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 9], [9, 4, 5, 9, 0, 9, 9, 5, 0, 4], [4, 4, 5, 0, 0, 4, 0, 5, 4, 4], [9, 4, 5, 4, 9, 0, 9, 5, 0, 0], [0, 9, 5, 0, 4, 0, 0, 5, 0, 4], [0, 4, 5, 5, 5, 5, 5, 5, 4, 4], [9, 0, 9, 9, 4, 0, 9, 0, 0, 0], [9, 9, 9, 0, 9, 4, 9, 9, 0, 0]], "output": [[9, 4, 0, 0, 4, 9, 0, 0, 9, 9], [4, 9, 9, 4, 9, 9, 0, 0, 9, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 9], [9, 4, 5, 4, 0, 4, 4, 5, 0, 4], [4, 4, 5, 0, 0, 9, 0, 5, 4, 4], [9, 4, 5, 9, 4, 0, 4, 5, 0, 0], [0, 9, 5, 0, 9, 0, 0, 5, 0, 4], [0, 4, 5, 5, 5, 5, 5, 5, 4, 4], [9, 0, 9, 9, 4, 0, 9, 0, 0, 0], [9, 9, 9, 0, 9, 4, 9, 9, 0, 0]]}, {"input": [[0, 0, 8, 6, 0, 6, 0, 8, 0, 8], [8, 5, 5, 5, 5, 5, 5, 5, 5, 0], [0, 5, 0, 8, 8, 6, 6, 0, 5, 8], [6, 5, 6, 6, 6, 8, 0, 6, 5, 8], [0, 5, 6, 6, 8, 6, 0, 6, 5, 8], [6, 5, 8, 8, 8, 6, 8, 0, 5, 8], [6, 5, 6, 8, 6, 8, 6, 8, 5, 8], [0, 5, 6, 0, 6, 8, 8, 8, 5, 8], [8, 5, 5, 5, 5, 5, 5, 5, 5, 6], [8, 8, 8, 0, 8, 8, 6, 0, 6, 6]], "output": [[0, 0, 8, 6, 0, 6, 0, 8, 0, 8], [8, 5, 5, 5, 5, 5, 5, 5, 5, 0], [0, 5, 0, 6, 6, 8, 8, 0, 5, 8], [6, 5, 8, 8, 8, 6, 0, 8, 5, 8], [0, 5, 8, 8, 6, 8, 0, 8, 5, 8], [6, 5, 6, 6, 6, 8, 6, 0, 5, 8], [6, 5, 8, 6, 8, 6, 8, 6, 5, 8], [0, 5, 8, 0, 8, 6, 6, 6, 5, 8], [8, 5, 5, 5, 5, 5, 5, 5, 5, 6], [8, 8, 8, 0, 8, 8, 6, 0, 6, 6]]}, {"input": [[0, 0, 3, 3, 3, 3, 2, 0, 2, 0], [3, 5, 5, 5, 5, 5, 5, 5, 5, 3], [3, 5, 3, 2, 2, 2, 2, 0, 5, 2], [0, 5, 0, 3, 0, 3, 2, 2, 5, 2], [3, 5, 2, 0, 2, 3, 2, 2, 5, 3], [3, 5, 3, 3, 0, 2, 3, 3, 5, 3], [3, 5, 3, 3, 3, 0, 3, 2, 5, 2], [0, 5, 3, 0, 3, 3, 3, 0, 5, 3], [0, 5, 5, 5, 5, 5, 5, 5, 5, 3], [2, 0, 3, 3, 3, 2, 3, 2, 3, 0]], "output": [[0, 0, 3, 3, 3, 3, 2, 0, 2, 0], [3, 5, 5, 5, 5, 5, 5, 5, 5, 3], [3, 5, 2, 3, 3, 3, 3, 0, 5, 2], [0, 5, 0, 2, 0, 2, 3, 3, 5, 2], [3, 5, 3, 0, 3, 2, 3, 3, 5, 3], [3, 5, 2, 2, 0, 3, 2, 2, 5, 3], [3, 5, 2, 2, 2, 0, 2, 3, 5, 2], [0, 5, 2, 0, 2, 2, 2, 0, 5, 3], [0, 5, 5, 5, 5, 5, 5, 5, 5, 3], [2, 0, 3, 3, 3, 2, 3, 2, 3, 0]]}], "test": [{"input": [[7, 0, 1, 1, 7, 0, 0, 7, 7, 7], [1, 5, 5, 5, 5, 5, 5, 5, 5, 7], [1, 5, 0, 0, 1, 0, 1, 7, 5, 7], [0, 5, 7, 1, 7, 0, 1, 7, 5, 1], [7, 5, 7, 7, 0, 1, 7, 1, 5, 1], [7, 5, 0, 1, 7, 0, 7, 7, 5, 1], [1, 5, 7, 7, 1, 1, 1, 1, 5, 0], [0, 5, 1, 7, 7, 7, 7, 0, 5, 7], [0, 5, 5, 5, 5, 5, 5, 5, 5, 0], [0, 1, 7, 1, 0, 7, 0, 0, 7, 7]], "output": [[7, 0, 1, 1, 7, 0, 0, 7, 7, 7], [1, 5, 5, 5, 5, 5, 5, 5, 5, 7], [1, 5, 0, 0, 7, 0, 7, 1, 5, 7], [0, 5, 1, 7, 1, 0, 7, 1, 5, 1], [7, 5, 1, 1, 0, 7, 1, 7, 5, 1], [7, 5, 0, 7, 1, 0, 1, 1, 5, 1], [1, 5, 1, 1, 7, 7, 7, 7, 5, 0], [0, 5, 7, 1, 1, 1, 1, 0, 5, 7], [0, 5, 5, 5, 5, 5, 5, 5, 5, 0], [0, 1, 7, 1, 0, 7, 0, 0, 7, 7]]}]}