{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 6, 6, 0, 6, 6, 0], [0, 6, 0, 0, 0, 6, 0], [0, 6, 6, 6, 6, 6, 0]], "output": [[0, 0, 0, 4, 0, 0, 0], [4, 0, 0, 4, 0, 0, 4], [0, 4, 0, 4, 0, 4, 0], [0, 0, 4, 4, 4, 0, 0], [0, 6, 6, 4, 6, 6, 0], [0, 6, 4, 4, 4, 6, 0], [0, 6, 6, 6, 6, 6, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 7, 0, 0, 0, 7], [0, 0, 0, 0, 7, 7, 7, 7, 7]], "output": [[4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 7, 7, 7, 7, 7], [0, 0, 0, 4, 7, 4, 4, 4, 7], [4, 4, 4, 4, 4, 4, 4, 4, 7], [4, 4, 4, 4, 4, 4, 4, 4, 7], [4, 4, 4, 4, 4, 4, 4, 4, 7], [0, 0, 0, 4, 7, 4, 4, 4, 7], [0, 0, 4, 0, 7, 7, 7, 7, 7]]}, {"input": [[3, 3, 3, 3, 3, 3], [3, 0, 0, 0, 0, 3], [3, 0, 0, 0, 0, 3], [3, 3, 0, 0, 3, 3], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[3, 3, 3, 3, 3, 3], [3, 4, 4, 4, 4, 3], [3, 4, 4, 4, 4, 3], [3, 3, 4, 4, 3, 3], [0, 4, 4, 4, 4, 0], [4, 0, 4, 4, 0, 4]]}], "test": [{"input": [[0, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 2, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 2, 2, 2, 2, 0, 4, 0, 0, 0], [0, 2, 4, 4, 2, 4, 0, 0, 0, 0], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4], [0, 2, 4, 4, 2, 4, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0]]}]}