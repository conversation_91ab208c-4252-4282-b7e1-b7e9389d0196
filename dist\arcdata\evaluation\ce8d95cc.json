{"train": [{"input": [[0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [3, 3, 3, 8, 3, 3, 3, 3, 6, 3, 3], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [5, 5, 5, 8, 5, 5, 5, 5, 6, 5, 5], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 6, 0, 0]], "output": [[0, 8, 0, 6, 0], [3, 8, 3, 6, 3], [0, 8, 0, 6, 0], [5, 8, 5, 6, 5], [0, 8, 0, 6, 0]]}, {"input": [[0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [2, 2, 1, 2, 2, 8, 2, 3, 2, 2, 2], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [5, 5, 1, 5, 5, 8, 5, 3, 5, 5, 5], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0], [0, 0, 1, 0, 0, 8, 0, 3, 0, 0, 0]], "output": [[0, 1, 0, 8, 0, 3, 0], [2, 1, 2, 8, 2, 3, 2], [0, 1, 0, 8, 0, 3, 0], [5, 1, 5, 8, 5, 3, 5], [0, 1, 0, 8, 0, 3, 0]]}, {"input": [[0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [3, 3, 4, 3, 3, 3, 3, 3, 3], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 0, 0]], "output": [[0, 4, 0], [3, 4, 3], [0, 4, 0], [8, 8, 8], [0, 4, 0]]}, {"input": [[0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [7, 7, 3, 7, 7, 7, 7, 1, 7, 7, 7], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 3, 0, 0, 0, 0, 1, 0, 0, 0]], "output": [[0, 3, 0, 1, 0], [7, 3, 7, 1, 7], [0, 3, 0, 1, 0], [2, 2, 2, 1, 2], [0, 3, 0, 1, 0], [8, 8, 8, 8, 8], [0, 3, 0, 1, 0]]}], "test": [{"input": [[0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [6, 6, 6, 6, 6, 2, 6, 7, 6, 6, 4, 6, 6], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [1, 1, 1, 1, 1, 2, 1, 7, 1, 1, 4, 1, 1], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 4, 8, 8], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0], [0, 0, 3, 0, 0, 2, 0, 7, 0, 0, 4, 0, 0]], "output": [[0, 3, 0, 2, 0, 7, 0, 4, 0], [6, 6, 6, 2, 6, 7, 6, 4, 6], [0, 3, 0, 2, 0, 7, 0, 4, 0], [1, 1, 1, 2, 1, 7, 1, 4, 1], [0, 3, 0, 2, 0, 7, 0, 4, 0], [8, 8, 8, 8, 8, 7, 8, 4, 8], [0, 3, 0, 2, 0, 7, 0, 4, 0]]}]}