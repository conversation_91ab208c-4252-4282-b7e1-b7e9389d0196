{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 4, 1, 1, 4, 0, 0, 0, 0, 0], [0, 4, 1, 1, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0], [0, 0, 0, 4, 2, 2, 2, 2, 4, 0], [0, 0, 0, 4, 2, 2, 2, 2, 4, 0], [0, 0, 0, 4, 4, 4, 4, 4, 4, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 4, 2, 2, 2, 4, 0, 0, 0, 0], [0, 4, 2, 2, 2, 4, 0, 0, 0, 0], [0, 4, 2, 2, 2, 4, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 4, 1, 1, 4, 0], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0]]}], "test": [{"input": [[4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 4, 4, 4, 4, 4, 4]], "output": [[4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 4, 1, 1, 1, 1, 4], [0, 0, 0, 0, 4, 4, 4, 4, 4, 4]]}]}