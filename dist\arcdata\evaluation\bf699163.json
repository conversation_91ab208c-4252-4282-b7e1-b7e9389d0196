{"train": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 8, 8, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 8, 5, 8, 5, 5, 5, 5, 5, 5, 5, 2, 2, 2, 5, 5], [5, 8, 8, 8, 5, 5, 5, 5, 5, 5, 5, 2, 5, 2, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 2, 2, 2, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 3, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 3, 3, 3, 5, 7, 7, 7, 7, 5, 5, 7, 7], [5, 5, 5, 5, 5, 5, 5, 5, 7, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 1, 1, 1, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 7, 5, 1, 5, 1, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 7, 5, 1, 1, 1, 5, 5, 5], [5, 6, 6, 6, 5, 5, 5, 5, 7, 5, 5, 5, 5, 5, 5, 5], [5, 6, 5, 6, 5, 5, 5, 5, 7, 5, 5, 5, 5, 5, 5, 5], [5, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 7, 5, 5, 5, 5, 5, 5, 5]], "output": [[1, 1, 1], [1, 5, 1], [1, 1, 1]]}, {"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 1, 5, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 3, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 3, 3, 3, 5, 5, 5, 5, 2, 2, 2, 5], [5, 7, 7, 7, 5, 7, 7, 7, 5, 5, 5, 5, 5, 5, 2, 5, 2, 5], [5, 7, 5, 5, 5, 5, 5, 7, 5, 5, 5, 5, 5, 5, 2, 2, 2, 5], [5, 7, 5, 4, 4, 4, 5, 7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 4, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 7, 5, 4, 4, 4, 5, 7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 7, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 7, 5, 5, 5, 5, 5, 7, 5, 5, 5, 8, 8, 8, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 7, 5, 5, 5, 8, 5, 8, 5, 5, 5, 5], [5, 7, 5, 5, 5, 5, 5, 7, 5, 5, 5, 8, 8, 8, 5, 5, 5, 5]], "output": [[4, 4, 4], [4, 5, 4], [4, 4, 4]]}], "test": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 7, 5, 2, 2, 2, 5], [5, 5, 6, 6, 6, 5, 5, 5, 7, 5, 2, 5, 2, 5], [5, 5, 6, 5, 6, 5, 5, 5, 7, 5, 2, 2, 2, 5], [5, 5, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 7, 7, 5, 5, 7, 7], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 8, 8, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 8, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 8, 8, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 1, 5, 1, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[2, 2, 2], [2, 5, 2], [2, 2, 2]]}]}