{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1, 0, 0], [0, 0, 2, 8, 8, 8, 2, 0, 0, 0, 1, 8, 1, 0, 0], [0, 0, 2, 8, 8, 8, 2, 0, 0, 0, 1, 1, 1, 0, 0], [0, 0, 2, 8, 8, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 8, 8, 8, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 8, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 8, 8, 2, 0, 0, 0, 0, 0, 0], [0, 2, 8, 8, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 8, 8, 8, 8, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 2, 2, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 2, 2, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 2, 2, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 8, 8, 2, 0, 0, 0], [3, 8, 8, 3, 0, 0, 2, 8, 8, 2, 0, 0, 0], [3, 8, 8, 3, 0, 0, 2, 8, 8, 2, 0, 0, 0], [3, 3, 3, 3, 0, 0, 2, 8, 8, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 1, 8, 8, 8, 8, 8, 8, 1, 0, 0, 0], [0, 0, 1, 8, 8, 8, 8, 8, 8, 1, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0]]}], "test": [{"input": [[0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 0, 3, 3, 3, 3], [0, 0, 2, 2, 2, 2, 2, 2, 0, 3, 3, 3, 3], [0, 0, 2, 2, 2, 2, 2, 2, 0, 3, 3, 3, 3], [0, 0, 2, 2, 2, 2, 2, 2, 0, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3]], "output": [[0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 8, 8, 8, 1, 0, 0, 1, 1, 1, 0, 0], [0, 1, 8, 8, 8, 1, 0, 0, 1, 8, 1, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 2, 8, 8, 8, 8, 2, 0, 0, 0, 0, 0], [0, 0, 2, 8, 8, 8, 8, 2, 0, 3, 3, 3, 3], [0, 0, 2, 8, 8, 8, 8, 2, 0, 3, 8, 8, 3], [0, 0, 2, 8, 8, 8, 8, 2, 0, 3, 8, 8, 3], [0, 0, 2, 2, 2, 2, 2, 2, 0, 3, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3]]}]}