{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 4, 4, 1, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 1, 4, 4, 1, 0, 1, 4, 4, 1], [0, 0, 0, 0, 0, 0, 4, 2, 2, 4], [0, 0, 0, 0, 0, 0, 4, 2, 2, 4], [0, 0, 0, 0, 0, 0, 4, 2, 2, 4], [0, 0, 0, 0, 0, 0, 1, 4, 4, 1]]}, {"input": [[5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5]], "output": [[1, 4, 4, 4, 4, 1, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [4, 2, 2, 2, 2, 4, 0, 0, 0, 0], [1, 4, 4, 4, 4, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 4, 4, 4, 4, 1], [0, 0, 0, 0, 4, 2, 2, 2, 2, 4], [0, 0, 0, 0, 4, 2, 2, 2, 2, 4], [0, 0, 0, 0, 1, 4, 4, 4, 4, 1]]}], "test": [{"input": [[0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 5, 5, 5]], "output": [[0, 1, 4, 4, 1, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 4, 2, 2, 4, 0, 0, 0, 0, 0], [0, 1, 4, 4, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 4, 4, 4, 4, 1], [0, 0, 0, 0, 4, 2, 2, 2, 2, 4], [0, 0, 0, 0, 1, 4, 4, 4, 4, 1]]}]}