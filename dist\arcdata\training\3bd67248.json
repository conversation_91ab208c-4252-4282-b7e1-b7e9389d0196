{"train": [{"input": [[6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[5, 0, 0], [5, 0, 0], [5, 0, 0]], "output": [[5, 0, 2], [5, 2, 0], [5, 4, 4]]}, {"input": [[8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0]], "output": [[8, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 2, 0], [8, 0, 0, 0, 2, 0, 0], [8, 0, 0, 2, 0, 0, 0], [8, 0, 2, 0, 0, 0, 0], [8, 2, 0, 0, 0, 0, 0], [8, 4, 4, 4, 4, 4, 4]]}], "test": [{"input": [[3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 0, 0, 0, 0, 0, 0, 0, 0, 2], [3, 0, 0, 0, 0, 0, 0, 0, 2, 0], [3, 0, 0, 0, 0, 0, 0, 2, 0, 0], [3, 0, 0, 0, 0, 0, 2, 0, 0, 0], [3, 0, 0, 0, 0, 2, 0, 0, 0, 0], [3, 0, 0, 0, 2, 0, 0, 0, 0, 0], [3, 0, 0, 2, 0, 0, 0, 0, 0, 0], [3, 0, 2, 0, 0, 0, 0, 0, 0, 0], [3, 2, 0, 0, 0, 0, 0, 0, 0, 0], [3, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}]}