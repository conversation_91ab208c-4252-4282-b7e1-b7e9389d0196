{"train": [{"input": [[0, 3, 3, 9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 3, 9, 3, 3, 0, 0, 0, 0], [0, 9, 3, 3, 9, 3, 0, 3, 3, 3, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 9, 3, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [3, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 3, 3, 0, 0, 3, 9, 3, 0, 0, 0, 3, 3, 9, 3], [3, 9, 3, 0, 0, 3, 3, 9, 0, 0, 0, 9, 3, 3, 3], [3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 9, 3], [9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9, 3, 3], [3, 3, 9, 0, 3, 3, 3, 9, 3, 0, 0, 3, 3, 9, 3], [0, 0, 0, 0, 9, 3, 9, 3, 3, 0, 0, 3, 3, 3, 9], [0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 0, 9, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 3, 3, 9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 3, 9, 3, 3, 0, 0, 0, 0], [0, 9, 3, 3, 9, 3, 0, 3, 3, 3, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 9, 3, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0], [3, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 9, 3], [3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 3, 3], [3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 9, 3], [9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9, 3, 3], [3, 3, 9, 0, 3, 3, 3, 9, 3, 0, 0, 3, 3, 9, 3], [0, 0, 0, 0, 9, 3, 9, 3, 3, 0, 0, 3, 3, 3, 9], [0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 0, 9, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 3, 9, 0, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [0, 3, 9, 3, 0, 0, 3, 3, 3, 9, 3, 0, 0, 0, 0], [0, 3, 3, 9, 0, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [0, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [0, 0, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 9, 3, 9, 9, 3, 3], [0, 3, 9, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 9, 3, 3, 3, 3], [0, 3, 3, 3, 9, 3, 0, 0, 3, 3, 3, 3, 3, 3, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 9, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 9, 3, 9, 9, 3, 3], [0, 3, 9, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 3, 3, 9, 3, 3, 3, 3], [0, 3, 3, 3, 9, 3, 0, 0, 3, 3, 3, 3, 3, 3, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 3, 9, 3], [0, 3, 3, 9, 3, 3, 0, 3, 3, 3, 3, 0, 3, 9, 3], [0, 3, 9, 3, 3, 3, 0, 3, 9, 9, 3, 0, 3, 3, 9], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 9, 3, 3], [0, 0, 0, 0, 0, 0, 0, 3, 3, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0], [3, 3, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 3, 9, 0], [3, 9, 3, 3, 3, 0, 0, 3, 3, 3, 3, 0, 3, 3, 0], [3, 3, 3, 9, 3, 0, 0, 3, 9, 3, 9, 0, 3, 9, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 9, 3, 0], [0, 0, 0, 0, 0, 0, 0, 3, 9, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9, 3], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 3, 9, 3], [0, 0, 0, 0, 0, 0, 0, 3, 9, 9, 3, 0, 3, 3, 9], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 9, 3, 3], [0, 0, 0, 0, 0, 0, 0, 3, 3, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0], [3, 3, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 3, 9, 0], [3, 9, 3, 3, 3, 0, 0, 3, 3, 3, 3, 0, 3, 3, 0], [3, 3, 3, 9, 3, 0, 0, 3, 9, 3, 9, 0, 3, 9, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 9, 3, 0], [0, 0, 0, 0, 0, 0, 0, 3, 9, 9, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 9, 3, 3, 3, 3, 0, 0, 0, 3, 3, 9, 3, 9], [3, 3, 3, 3, 3, 9, 3, 0, 0, 0, 3, 3, 3, 3, 3], [3, 9, 3, 9, 3, 3, 3, 0, 0, 0, 3, 9, 3, 3, 9], [3, 3, 3, 3, 9, 3, 3, 0, 0, 0, 3, 3, 3, 3, 3], [3, 3, 9, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 0, 0], [0, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 9, 3, 0, 0], [0, 3, 9, 3, 9, 0, 3, 9, 3, 3, 3, 3, 3, 0, 0], [0, 3, 3, 3, 9, 0, 3, 3, 3, 9, 3, 3, 3, 0, 0], [0, 9, 3, 9, 3, 0, 3, 3, 3, 3, 3, 3, 3, 0, 0], [0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 9, 3, 3, 3, 3, 0, 0, 0, 3, 3, 9, 3, 9], [3, 3, 3, 3, 3, 9, 3, 0, 0, 0, 3, 3, 3, 3, 3], [3, 9, 3, 9, 3, 3, 3, 0, 0, 0, 3, 9, 3, 3, 9], [3, 3, 3, 3, 9, 3, 3, 0, 0, 0, 3, 3, 3, 3, 3], [3, 3, 9, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 9, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0], [0, 0, 3, 3, 3, 3, 9, 3, 0, 3, 9, 3, 3, 3, 0], [0, 0, 3, 9, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 0, 3, 3, 3, 3, 3, 9, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0], [3, 3, 3, 3, 3, 0, 0, 0, 0, 3, 3, 9, 3, 3, 0], [3, 9, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 3, 9, 3, 0, 3, 3, 9, 9, 3, 0, 0, 0, 0], [3, 3, 9, 3, 3, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 3, 9, 3, 9, 3, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 3, 3, 9], [3, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [3, 9, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9, 9], [9, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 9, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 9, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 3, 9, 3, 0, 3, 3, 9, 9, 3, 0, 0, 0, 0], [3, 3, 9, 3, 3, 0, 3, 3, 3, 3, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 3, 9, 3, 9, 3, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 3, 3, 9], [3, 3, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [3, 9, 3, 9, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9, 9], [9, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3]]}]}