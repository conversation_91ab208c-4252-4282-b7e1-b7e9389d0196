{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 1, 8, 8, 0, 8, 8, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 8, 1, 1, 1, 8, 0, 8, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 1, 0, 8, 1, 1, 1, 1, 0], [0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 1, 1, 0, 8, 8, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0, 8, 8, 1, 1, 1, 0], [0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0, 8, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 8, 0, 8, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 8, 8, 0, 8, 8, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 8, 8, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 8, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0, 1, 1, 1, 1, 1, 0], [0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 1, 1, 1, 0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0], [0, 8, 1, 1, 1, 1, 0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 1, 1, 1, 0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0], [0, 8, 1, 1, 1, 1, 0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 8, 1, 1, 1, 1, 0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0], [0, 8, 8, 1, 1, 1, 0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0], [0, 8, 1, 1, 1, 1, 0, 8, 1, 1, 1, 8, 0, 1, 1, 1, 1, 8, 0], [0, 8, 8, 1, 1, 1, 0, 8, 8, 1, 8, 8, 0, 1, 1, 1, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 2, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 2, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 2, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 2, 2, 0], [0, 3, 3, 3, 3, 3, 0, 3, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 2, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0], [0, 3, 2, 3, 2, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 2, 3, 2, 3, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 2, 3, 3, 3, 2, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 3, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 3, 2, 0], [0, 3, 2, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 2, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 2, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 2, 3, 2, 3, 0, 3, 3, 3, 3, 3, 0], [0, 2, 2, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 2, 2, 0], [0, 3, 3, 3, 3, 3, 0, 3, 2, 3, 2, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 2, 3, 3, 3, 2, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0], [0, 3, 2, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 2, 3, 0], [0, 2, 3, 3, 3, 3, 0, 3, 3, 2, 3, 3, 0, 3, 3, 3, 3, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 6, 6, 6, 6, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 6, 6, 0], [0, 8, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 6, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 6, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 8, 0], [0, 6, 6, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 6, 6, 6, 6, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 6, 8, 8, 8, 0, 6, 6, 6, 6, 6, 0, 8, 8, 8, 6, 6, 0], [0, 6, 6, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 6, 6, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 6, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 6, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 6, 8, 8, 8, 8, 0, 6, 6, 8, 6, 6, 0, 8, 8, 8, 8, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0], [0, 6, 6, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 6, 6, 0], [0, 6, 6, 8, 8, 8, 0, 6, 6, 6, 6, 6, 0, 8, 8, 8, 6, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 1, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 1, 0, 4, 4, 1, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 1, 1, 0, 4, 1, 1, 1, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 1, 1, 0, 4, 1, 1, 1, 4, 0, 1, 1, 4, 1, 1, 0], [0, 4, 4, 4, 4, 1, 0, 4, 4, 1, 4, 4, 0, 1, 4, 4, 4, 1, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 1, 4, 4, 4, 1, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 1, 1, 4, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 1, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 1, 0], [0, 1, 1, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 1, 1, 0], [0, 1, 4, 4, 4, 4, 0, 1, 4, 4, 4, 4, 0, 4, 4, 4, 4, 1, 0], [0, 4, 4, 4, 4, 4, 0, 1, 1, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 4, 4, 4, 0, 4, 1, 1, 1, 4, 0, 4, 4, 4, 1, 1, 0], [0, 1, 4, 4, 4, 4, 0, 4, 4, 1, 4, 4, 0, 4, 4, 4, 4, 1, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 1, 1, 4, 1, 1, 0, 4, 4, 4, 4, 4, 0], [0, 1, 4, 4, 4, 4, 0, 1, 4, 4, 4, 1, 0, 4, 4, 4, 4, 1, 0], [0, 1, 1, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 1, 1, 0], [0, 1, 4, 4, 4, 4, 0, 1, 4, 4, 4, 1, 0, 4, 4, 4, 4, 1, 0], [0, 4, 4, 4, 4, 4, 0, 1, 1, 4, 1, 1, 0, 4, 4, 4, 4, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0], [0, 1, 4, 4, 4, 4, 0, 4, 4, 1, 4, 4, 0, 4, 4, 4, 4, 1, 0], [0, 1, 1, 4, 4, 4, 0, 4, 1, 1, 1, 4, 0, 4, 4, 4, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}