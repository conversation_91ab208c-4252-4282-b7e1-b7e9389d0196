{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 6, 0, 0, 0, 0, 0, 0, 0], [0, 9, 4, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 8, 8]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 6, 0, 0, 0], [0, 0, 0, 0, 0, 9, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 6, 0, 0, 0, 0, 0, 0], [0, 0, 9, 4, 0, 0, 0, 0, 7, 6], [0, 0, 0, 0, 0, 0, 0, 0, 9, 4]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 7, 7, 0, 0, 0], [0, 0, 0, 0, 0, 6, 6, 6, 0, 0], [0, 0, 0, 8, 8, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 7, 0, 0, 0, 0, 0, 0, 0], [0, 6, 6, 6, 0, 0, 7, 7, 0, 0], [0, 0, 0, 0, 0, 0, 6, 6, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 7, 7, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 0, 0, 0, 8, 8, 0, 0], [8, 8, 8, 8, 0, 8, 8, 8, 8, 0], [0, 0, 8, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 0, 0, 0, 0, 0, 0, 0], [3, 4, 3, 3, 0, 0, 8, 8, 0, 0], [0, 0, 3, 0, 0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 0, 0, 0, 4, 4, 0, 0], [3, 4, 3, 3, 0, 3, 4, 3, 3, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 4, 0, 0], [0, 0, 0, 0, 0, 3, 4, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}