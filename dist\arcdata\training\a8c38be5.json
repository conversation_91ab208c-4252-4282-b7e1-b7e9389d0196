{"train": [{"input": [[5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 5, 5, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [8, 8, 5, 0, 0, 0, 0, 0, 5, 2, 5, 0, 0, 0], [0, 0, 2, 5, 5, 0, 0, 0, 5, 5, 5, 0, 0, 0], [0, 0, 2, 2, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 0], [0, 5, 1, 1, 0, 0, 5, 5, 5, 0, 5, 4, 5, 0], [0, 5, 5, 1, 0, 0, 0, 0, 0, 0, 4, 4, 4, 0], [0, 5, 5, 5, 0, 0, 5, 5, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 3, 3, 0, 0, 0, 0, 0], [5, 5, 5, 0, 0, 0, 5, 5, 3, 0, 6, 6, 5, 0], [5, 5, 9, 0, 0, 0, 0, 0, 0, 0, 6, 5, 5, 0], [5, 9, 9, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0]], "output": [[6, 6, 5, 2, 2, 2, 5, 1, 1], [6, 5, 5, 5, 2, 5, 5, 5, 1], [5, 5, 5, 5, 5, 5, 5, 5, 5], [2, 5, 5, 5, 5, 5, 5, 5, 3], [2, 2, 5, 5, 5, 5, 5, 3, 3], [2, 5, 5, 5, 5, 5, 5, 5, 3], [5, 5, 5, 5, 5, 5, 5, 5, 5], [8, 5, 5, 5, 4, 5, 5, 5, 9], [8, 8, 5, 4, 4, 4, 5, 9, 9]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 4], [0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 4, 4], [0, 3, 5, 5, 0, 5, 8, 8, 0, 0, 0, 5, 5, 4], [0, 3, 3, 5, 0, 5, 5, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 9, 0], [0, 1, 1, 1, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0], [0, 5, 1, 5, 0, 0, 5, 5, 5, 0, 6, 5, 5, 0], [0, 5, 5, 5, 0, 0, 5, 5, 5, 0, 6, 6, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 5, 5, 0], [0, 0, 0, 0, 7, 7, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 5, 5, 0, 0, 5, 5, 5, 0, 0], [0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 2, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0]], "output": [[7, 7, 5, 1, 1, 1, 5, 8, 8], [7, 5, 5, 5, 1, 5, 5, 5, 8], [5, 5, 5, 5, 5, 5, 5, 5, 5], [6, 5, 5, 5, 5, 5, 5, 5, 4], [6, 6, 5, 5, 5, 5, 5, 4, 4], [6, 5, 5, 5, 5, 5, 5, 5, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5], [3, 5, 5, 5, 2, 5, 5, 5, 9], [3, 3, 5, 2, 2, 2, 5, 9, 9]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0], [0, 1, 5, 5, 0, 0, 0, 0, 0, 0, 6, 5, 5, 0, 0], [0, 1, 1, 5, 0, 2, 2, 2, 0, 0, 6, 6, 5, 0, 0], [0, 1, 5, 5, 0, 5, 2, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0], [0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0], [0, 0, 5, 8, 5, 0, 5, 5, 1, 0, 5, 5, 5, 0, 0], [0, 0, 8, 8, 8, 0, 5, 1, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 5, 1, 0, 0, 0, 0, 0, 0], [0, 5, 4, 4, 0, 0, 0, 0, 0, 0, 0, 3, 3, 5, 0], [0, 5, 5, 4, 0, 0, 0, 0, 0, 0, 0, 3, 5, 5, 0], [0, 5, 5, 5, 0, 0, 5, 5, 5, 0, 0, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 5, 5, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 7, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 3, 5, 2, 2, 2, 5, 4, 4], [3, 5, 5, 5, 2, 5, 5, 5, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 5, 5, 5, 5, 5, 5, 5, 1], [1, 1, 5, 5, 5, 5, 5, 1, 1], [1, 5, 5, 5, 5, 5, 5, 5, 1], [5, 5, 5, 5, 5, 5, 5, 5, 5], [6, 5, 5, 5, 8, 5, 5, 5, 7], [6, 6, 5, 8, 8, 8, 5, 7, 7]]}]}