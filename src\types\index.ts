export interface ARCGrid {
  grid: number[][];
  width: number;
  height: number;
}

export interface ARCPuzzle {
  id: string;
  train: { input: ARCGrid; output: ARCGrid }[];
  test: { input: ARCGrid; output?: ARCGrid }[];
}

export interface ChatMessage {
  id: string;
  role: 'demo' | 'user' | 'chercheur' | 'system';
  content: string;
  timestamp: Date;
  attachments?: {
    grids?: ARCGrid[];
    analysis?: PuzzleAnalysis;
    visualGrid?: string;
  };
  metadata?: {
    provider: string;
    model: string;
    tokens: number;
    cost?: number;
    responseTime: number;
    validationResult?: ValidationResult;
  };
}

export interface ValidationResult {
  isValid: boolean;
  accuracy: number;
  errorGrid?: boolean[][];
  feedback: string;
}

// Architecture Pure en 4 Niveaux pour l'Analyse ARC AGI
export interface PuzzleAnalysis {
  // Niveau 0 : Données Brutes
  level_0?: Level0Data;

  // Niveau 1 : Analyses Dérivées par Domaine
  level_1?: Level1Analysis;

  // Niveau 2 : Comparaisons Input/Output
  level_2?: Level2Analysis;

  // Niveau 3 : Synthèse par Entraînement
  level_3?: Level3Synthesis;

  // Rétrocompatibilité avec l'ancienne structure
  grid_info?: {
    input: {
      width: number;
      height: number;
      colors: number[];
      total_cells: number;
    };
    output: {
      width: number;
      height: number;
      colors: number[];
      total_cells: number;
    };
  };
  objects?: {
    input: ObjectsAnalysis;
    output: ObjectsAnalysis;
  };
  patterns?: {
    input: PatternsAnalysis;
    output: PatternsAnalysis;
  };
  transformations?: {
    dimension_change: {
      same_dimensions: boolean;
      width_change: number;
      height_change: number;
      transformation_type: string;
    };
    object_changes: any[];
    pattern_changes: any[];
  };
  symmetries?: {
    input: SymmetriesAnalysis;
    output: SymmetriesAnalysis;
  };
  complexity?: {
    input: ComplexityAnalysis;
    output: ComplexityAnalysis;
    transformation_complexity: string;
  };
  colors?: {
    input: ColorsAnalysis;
    output: ColorsAnalysis;
  };
  spatial_relations?: {
    input: SpatialProperties;
    output: SpatialProperties;
  };
  line_uniformity?: {
    input: GeometricDetection;
    output: GeometricDetection;
  };
  diff_analysis?: {
    change_ratio: number;
    change_positions: Array<[number, number]>;
    change_patterns: any[];
  } | null;
  enhanced_objects?: {
    input: DetectedObject[];
    output: DetectedObject[];
  };
  repeating_patterns?: {
    input: DetectedPattern[];
    output: DetectedPattern[];
  };
}

// Niveau 0 : Données Brutes
export interface Level0Data {
  // Tous les exemples d'entraînement
  training_examples?: Array<{
    example_id: string;
    input_grid: {
      grid_array: number[][];
      width: number;
      height: number;
      value_frequencies: Record<number, number>;
    };
    output_grid: {
      grid_array: number[][];
      width: number;
      height: number;
      value_frequencies: Record<number, number>;
    };
  }>;

  // Exemple principal (rétrocompatibilité)
  input_grid: {
    grid_array: number[][];
    width: number;
    height: number;
    value_frequencies: Record<number, number>;
  };
  output_grid: {
    grid_array: number[][];
    width: number;
    height: number;
    value_frequencies: Record<number, number>;
  };
}

// Niveau 1 : Analyses Dérivées par Domaine
export interface Level1Analysis {
  // Analyses de tous les exemples d'entraînement
  training_examples_analysis?: Array<{
    example_id: string;
    input_analysis: SingleGridAnalysis;
    output_analysis: SingleGridAnalysis;
  }>;
  
  // Analyse principale (premier exemple pour rétrocompatibilité)
  input_analysis: SingleGridAnalysis;
  output_analysis: SingleGridAnalysis;
}

export interface SingleGridAnalysis {
  // Sous-Niveau 1A : Détection Géométrique Pure
  geometric_detection: GeometricDetection;

  // Sous-Niveau 1B : Classification Structurelle
  structural_classification: StructuralClassification;

  // Sous-Niveau 1C : Analyse de Blocs
  block_analysis?: BlockAnalysis;

  // Domaines Parallèles
  colors: ColorsAnalysis;
  symmetries: SymmetriesAnalysis;
  objects: ObjectsAnalysis;
  repeating_patterns: PatternsAnalysis;
  spatial_properties: SpatialProperties;
  mosaics?: MosaicsAnalysis;
}

export interface StructuralClassification {
  borders: {
    top_border: { exists: boolean; row?: number; color?: number; complete?: boolean; thickness?: number };
    bottom_border: { exists: boolean; row?: number; color?: number; complete?: boolean; thickness?: number };
    left_border: { exists: boolean; column?: number; color?: number; complete?: boolean; thickness?: number };
    right_border: { exists: boolean; column?: number; color?: number; complete?: boolean; thickness?: number };
    border_completeness: 'complete' | 'partial' | 'none';
    border_colors?: number[];
    [key: string]: any; // Allow string indexing for dynamic border access
  };
  separators: Array<{
    separator_id: string;
    type: 'vertical' | 'horizontal';
    position: number;
    color: number;
    spans_full_dimension: boolean;
    thickness: number;
    divides_into_blocks: boolean;
    left_block_width?: number;
    right_block_width?: number;
    top_block_height?: number;
    bottom_block_height?: number;
  }>;
  grid_lines: {
    has_grid_structure: boolean;
    vertical_grid_lines?: number[];
    horizontal_grid_lines?: number[];
    grid_line_color?: number;
    creates_regular_blocks?: boolean;
    block_dimensions?: [number, number];
    grid_size?: [number, number];
    grid_regularity?: number;
    grid_completeness?: number;
  };
  structural_roles: {
    containment_lines: number[];
    division_lines: number[];
    decoration_lines: number[];
    functional_line_ratio: number;
  };
}

export interface BlockAnalysis {
  detected_blocks: Array<{
    block_id: string;
    bounds: { top: number; left: number; bottom: number; right: number };
    dimensions: [number, number];
    area: number;
    separated_by: string[];
    block_grid_array: number[][];
    block_statistics: {
      value_frequencies: Record<number, number>;
      width: number;
      height: number;
      total_pixels: number;
    };
    block_color_analysis: {
      present_colors: number[];
      dominant_color: number;
      probable_background?: number;
      non_background_colors: number[];
      color_diversity: number;
      background_ratio: number;
    };
    block_uniformity: {
      is_uniform: boolean;
      uniform_color?: number;
      uniformity_ratio: number;
      has_internal_pattern: boolean;
      pattern_complexity: 'none' | 'simple' | 'complex';
    };
    block_symmetries: {
      horizontal: boolean;
      vertical: boolean;
      diagonal_main: boolean;
      diagonal_anti: boolean;
      symmetry_count: number;
    };
    block_pattern_hash: string;
    transformation_variants?: {
      rotations: {
        rot_90: number[][];
        rot_180: number[][];
        rot_270: number[][];
      };
      flips: {
        flip_horizontal: number[][];
        flip_vertical: number[][];
      };
      variant_hashes: {
        original: string;
        rot_90: string;
        rot_180: string;
        rot_270: string;
        flip_h: string;
        flip_v: string;
      };
    };
  }>;
  block_uniformity: {
    all_same_size: boolean;
    uniform_dimensions?: [number, number];
    size_variations: any[];
    total_blocks: number;
  };
  block_content_patterns: {
    uniform_blocks: string[];
    patterned_blocks: string[];
    diverse_blocks: string[];
    background_consistency: {
      same_background_across_blocks: boolean;
      common_background_color?: number;
      background_variations: Record<string, number>;
    };
    content_similarity: {
      identical_blocks: string[][];
      similar_blocks: string[][];
      unique_blocks: string[];
    };
  };
}

export interface MosaicsAnalysis {
  mosaic_detection: {
    is_potential_mosaic: boolean;
    confidence_score: number;
    detection_reasons: string[];
    size_analysis: {
      grid_dimensions: [number, number];
      total_pixels: number;
      size_category: 'small' | 'medium' | 'large' | 'very_large';
      exceeds_average: boolean;
    };
    color_balance_analysis: {
      color_entropy: number;
      balance_score: number;
      dominant_color_ratio: number;
      is_well_balanced: boolean;
    };
    uniform_zones_detection: {
      uniform_zones: Array<{
        zone_id: string;
        color: number;
        bounds: [number, number, number, number];
        area: number;
        shape: 'rectangle' | 'square' | 'irregular';
        position: 'corner' | 'edge' | 'center';
      }>;
      total_uniform_zones: number;
      uniform_coverage_ratio: number;
    };
  };
  folding_analysis?: {
    vertical_folds: any[];
    horizontal_folds: any[];
    diagonal_folds: any[];
    best_fold_candidate?: {
      fold_type: 'vertical' | 'horizontal' | 'diagonal';
      fold_id: string;
      confidence: number;
      match_quality: number;
    };
  };
}

// Niveau 2 : Comparaisons Input/Output
export interface Level2Analysis {
  // Comparaisons par exemple d'entraînement
  training_examples_comparisons?: Array<{
    example_id: string;
    transformation_analysis: {
      dimension_compatibility: {
        same_dimensions: boolean;
        width_change: number;
        height_change: number;
        transformation_type?: 'same_size' | 'resize' | 'crop' | 'expand';
      };
      diff_grid: {
        exists: boolean;
        grid_array?: number[][];
        change_ratio: number | null;
        change_positions: Array<[number, number]> | null;
        // Analyse complète de la grille diff
        diff_analysis?: SingleGridAnalysis;
      };
    };
  }>;
  
  // Analyse principale (premier exemple pour rétrocompatibilité)
  transformation_analysis: {
    dimension_compatibility: {
      same_dimensions: boolean;
      width_change: number;
      height_change: number;
      transformation_type?: 'same_size' | 'resize' | 'crop' | 'expand';
    };
    diff_grid: {
      exists: boolean;
      grid_array?: number[][];
      change_ratio: number | null;
      change_positions: Array<[number, number]> | null;
      // Analyse complète de la grille diff
      diff_analysis?: SingleGridAnalysis;
    };
  };
}

// Interface de compatibilité (à supprimer progressivement)
export interface Level2Comparisons {
  structural_transformations: {
    separator_changes: Array<{
      separator_id: string;
      change_type: 'removed' | 'added' | 'moved' | 'color_changed';
      input_state?: any;
      output_state?: any;
      transformation_details?: {
        position_change?: [number, number];
        color_change?: [number, number];
        dimension_change?: any;
      };
    }>;
    border_changes: Array<{
      border_side: 'top' | 'bottom' | 'left' | 'right';
      change_type: 'removed' | 'added' | 'color_changed' | 'partial_change';
      input_border?: any;
      output_border?: any;
      change_impact: 'full_removal' | 'partial_modification' | 'complete_addition';
    }>;
    grid_structure_evolution: {
      input_grid_size?: [number, number];
      output_grid_size?: [number, number];
      structure_change: 'preserved' | 'simplified' | 'complexified' | 'removed';
      block_dimension_change?: [[number, number], [number, number]];
    };
  };
  object_transformations: {
    object_mapping: Array<{
      input_object_id: string;
      output_object_id?: string;
      transformation_detected?: 'rot_90' | 'rot_180' | 'rot_270' | 'flip_h' | 'flip_v';
      position_change: {
        moved: boolean;
        input_position: [number, number];
        output_position: [number, number];
        displacement_vector: [number, number];
      };
      anchor_interaction: {
        moved_to_anchor: boolean;
        anchor_position?: [number, number];
        anchor_confirmed: boolean;
      };
      shape_preservation: {
        shape_preserved: boolean;
        size_preserved: boolean;
        color_preserved: boolean;
      };
    }>;
    object_operations: Array<{
      operation_type: 'duplicate' | 'merge' | 'split' | 'create' | 'destroy';
      source_objects: string[];
      result_objects: string[];
      operation_context: string;
    }>;
  };
  anchor_confirmations: Array<{
    anchor_position: [number, number];
    was_anchor: boolean;
    objects_anchored: string[];
    anchor_type_confirmed: string;
  }>;
  diff_exploitation: {
    change_analysis: {
      total_changes: number;
      change_ratio: number;
      change_distribution: {
        clustered_changes: any[];
        scattered_changes: any[];
        linear_changes: any[];
      };
    };
    change_patterns: {
      systematic_additions: Array<{
        added_color: number;
        addition_pattern: 'fill_blocks' | 'create_borders' | 'add_objects';
        affected_regions: string[];
      }>;
      systematic_removals: Array<{
        removed_color: number;
        removal_pattern: 'remove_separators' | 'clear_background';
        affected_regions: string[];
      }>;
      systematic_substitutions: Array<{
        color_mapping: Record<number, number>;
        substitution_scope: 'global' | 'blocks_only' | 'objects_only';
        pattern_preservation: boolean;
      }>;
    };
  };
}

// Niveau 3 : Synthèse par Entraînement
export interface Level3Synthesis {
  recurring_patterns: {
    global_patterns: Array<{
      pattern_id: string;
      pattern_description: string;
      occurrence_frequency: number;
      confidence_score: number;
      examples_supporting: string[];
    }>;
    consistency_score: number;
    pattern_conflicts: any[];
  };
  global_rules: {
    rule_hypotheses: Array<{
      rule_id: string;
      rule_description: string;
      rule_type: 'structural' | 'object_based' | 'color_based' | 'pattern_based';
      confidence: number;
      supporting_examples: string[];
      contradicting_examples: string[];
    }>;
    best_rule_confidence: number;
    rule_consistency: number;
  };
  predictions: {
    test_predictions: Array<{
      prediction_id: string;
      predicted_output: number[][];
      prediction_method: string;
      confidence: number;
      reasoning: string;
    }>;
    prediction_confidence: number;
    alternative_predictions: any[];
  };
}

export interface ObjectsAnalysis {
  table_analysis: {
    probable_background: number;
    background_confidence: number;
    non_background_colors: number[];
    table_coverage: number;
    noise_pixels: number;
  };
  detected_objects: DetectedObject[];
  anchor_analysis: {
    possible_anchor_points: AnchorPoint[];
  };
  object_statistics: {
    total_objects: number;
    objects_by_color: Record<number, number>;
    objects_by_shape: Record<string, number>;
    table_occupancy: number;
    spatial_distribution?: {
      object_density: number;
      clustering_coefficient: number;
      dispersion_index: number;
    };
  };
}

export interface DetectedObject {
  object_id: string;
  color: number;
  multi_color: boolean;
  area: number;
  center: [number, number];
  bbox: [number, number, number, number];
  width: number;
  height: number;
  aspect_ratio: number;
  shape_classification: {
    basic_shape: 'square' | 'rectangle' | 'line' | 'L_shape' | 'T_shape' | 'cross' | 'dot' | 'irregular';
    shape_confidence: number;
    symmetries: {
      horizontal: boolean;
      vertical: boolean;
      diagonal_main: boolean;
      diagonal_anti: boolean;
    };
  };
  object_hash: string;
  table_position: {
    absolute_position: [number, number];
    relative_position: string;
    distance_to_edges: { top: number; bottom: number; left: number; right: number };
    touching_edges: string[];
  };
}

export interface AnchorPoint {
  anchor_id: string;
  position: [number, number];
  size: number;
  shape: 'single_pixel' | 'small_cluster' | 'line' | 'cross';
  nearby_objects: string[];
  location_type: 'corner' | 'edge' | 'center' | 'isolated';
}

export interface PatternsAnalysis {
  detected_patterns: DetectedPattern[];
  pattern_statistics: {
    total_patterns: number;
    pattern_density: number;
    pattern_coverage: number;
  };
  complexity_analysis: ComplexityAnalysis;
}

export interface DetectedPattern {
  pattern_id: string;
  pattern_size: [number, number];
  pattern_hash: string;
  occurrence_count: number;
  coverage_ratio: number;
  pattern_type: 'exact' | 'rotated' | 'flipped';
}

export interface ComplexityAnalysis {
  pattern_complexity: 'simple' | 'medium' | 'complex';
  regularity_index: number;
  predictability: number;
}

export interface SymmetriesAnalysis {
  horizontal: boolean;
  vertical: boolean;
  diagonal_main: boolean;
  diagonal_anti: boolean;
  symmetry_count: number;
  symmetry_axes: Array<{ type: string; position: number }>;
}

export interface ColorsAnalysis {
  present_colors: number[];
  dominant_color: number;
  background_color: number;
  non_background_colors: number[];
  color_count: number;
  color_distribution: Record<number, number>;
}

export interface SpatialProperties {
  object_density: number;
  background_ratio: number;
  filled_ratio: number;
  center_of_mass: [number, number];
  spatial_distribution: 'clustered' | 'dispersed' | 'uniform';
}

export interface GeometricDetection {
  uniform_rows: Array<{ index: number; color: number; length: number }>;
  uniform_columns: Array<{ index: number; color: number; length: number }>;
  uniform_main_diagonals: Array<{ start: [number, number]; end: [number, number]; color: number; length: number }>;
  uniform_anti_diagonals: Array<{ start: [number, number]; end: [number, number]; color: number; length: number }>;
  line_distribution: { rows: number; columns: number; main_diagonals: number; anti_diagonals: number };
  color_frequency_in_lines: Record<number, number>;
}

export interface DemoMode {
  isActive: boolean;
  currentIndex: number;
  totalPuzzles: number;
  autoAdvanceDelay: number;
  selectedSubset: 'training' | 'evaluation' | 'both';
  selectedProvider: string;
  selectedModel: string;
  stopOnError: boolean;
  batchSize: number;
}

export interface DemoSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  puzzlesProcessed: number;
  successCount: number;
  failureCount: number;
  averageResponseTime: number;
  totalCost: number;
  results: DemoResult[];
}

export interface DemoResult {
  puzzleId: string;
  success: boolean;
  responseTime: number;
  cost: number;
  accuracy: number;
  errorCount: number;
  attempts: number;
}

export interface AIProvider {
  id: string;
  name: string;
  endpoint: string;
  requiresKey: boolean;
  models: string[];
  category: 'local' | 'api';
}

export const AI_PROVIDERS: Record<string, AIProvider> = {
  ollama: {
    id: 'ollama',
    name: 'Ollama',
    endpoint: 'http://localhost:11434',
    requiresKey: false,
    models: ['llama2', 'codellama', 'mistral'],
    category: 'local'
  },
  lmstudio: {
    id: 'lmstudio',
    name: 'LM Studio',
    endpoint: 'http://localhost:1234',
    requiresKey: false,
    models: ['local-model'],
    category: 'local'
  },
  openrouter: {
    id: 'openrouter',
    name: 'OpenRouter',
    endpoint: 'https://openrouter.ai/api/v1',
    requiresKey: true,
    models: ['anthropic/claude-3-sonnet', 'openai/gpt-4-turbo'],
    category: 'api'
  },
  groq: {
    id: 'groq',
    name: 'Groq',
    endpoint: 'https://api.groq.com/openai/v1',
    requiresKey: true,
    models: ['llama2-70b-4096', 'mixtral-8x7b-32768'],
    category: 'api'
  },
  openai: {
    id: 'openai',
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1',
    requiresKey: true,
    models: ['gpt-4-turbo', 'gpt-3.5-turbo'],
    category: 'api'
  },
  perplexity: {
    id: 'perplexity',
    name: 'Perplexity',
    endpoint: 'https://api.perplexity.ai',
    requiresKey: true,
    models: ['llama-3-sonar-large-32k-online'],
    category: 'api'
  },
  deepseek: {
    id: 'deepseek',
    name: 'DeepSeek',
    endpoint: 'https://api.deepseek.com/v1',
    requiresKey: true,
    models: ['deepseek-chat', 'deepseek-coder'],
    category: 'api'
  }
};