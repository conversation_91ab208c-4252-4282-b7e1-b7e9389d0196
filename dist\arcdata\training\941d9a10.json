{"train": [{"input": [[0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0]], "output": [[1, 1, 5, 0, 0, 0, 0, 5, 0, 0], [1, 1, 5, 0, 0, 0, 0, 5, 0, 0], [1, 1, 5, 0, 0, 0, 0, 5, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 2, 2, 2, 2, 5, 0, 0], [0, 0, 5, 2, 2, 2, 2, 5, 0, 0], [0, 0, 5, 2, 2, 2, 2, 5, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 5, 0, 0, 0, 0, 5, 3, 3], [0, 0, 5, 0, 0, 0, 0, 5, 3, 3]]}, {"input": [[0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0]], "output": [[1, 1, 1, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 2, 2, 2, 2, 5, 0], [0, 0, 0, 5, 2, 2, 2, 2, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 5, 0, 0, 0, 0, 5, 3]]}, {"input": [[0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0], [0, 5, 0, 0, 5, 0, 5, 0, 5, 0]], "output": [[1, 5, 0, 0, 5, 0, 5, 0, 5, 0], [1, 5, 0, 0, 5, 0, 5, 0, 5, 0], [1, 5, 0, 0, 5, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 0, 5, 2, 5, 0, 5, 0], [0, 5, 0, 0, 5, 2, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 0, 5, 0, 5, 0, 5, 3], [0, 5, 0, 0, 5, 0, 5, 0, 5, 3], [0, 5, 0, 0, 5, 0, 5, 0, 5, 3]]}], "test": [{"input": [[0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0]], "output": [[1, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 2, 2, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 0, 5, 0, 0, 5, 0, 5, 3]]}]}