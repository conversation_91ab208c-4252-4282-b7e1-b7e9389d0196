{"train": [{"input": [[5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0], [5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5], [0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5], [5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 0, 5], [0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 5], [0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 0, 0, 0, 5], [5, 5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 0, 0, 5], [0, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5], [5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 0, 5], [5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5], [0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5], [5, 0, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0], [0, 5, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 5], [5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 0, 5]], "output": [[5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0], [5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5], [0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5], [5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 0, 5], [0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 5, 5, 5, 1, 1, 1, 5], [0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 1, 1, 1, 5], [5, 5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 1, 1, 1, 5], [0, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5], [5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 0, 5], [5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5], [0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 5], [5, 0, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0], [0, 5, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 0, 5, 0], [5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 5], [5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 0, 5]]}, {"input": [[3, 3, 3, 3, 0, 3, 0, 3, 0, 3, 3, 0, 0, 3, 3, 3, 0, 3, 0, 0], [0, 0, 3, 3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 3, 3, 0], [3, 3, 3, 3, 3, 0, 0, 3, 0, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 3], [3, 0, 3, 3, 0, 0, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 0, 3, 3, 0], [0, 0, 0, 3, 0, 3, 0, 3, 3, 3, 0, 3, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 0, 0, 3, 3, 0, 3, 3, 3, 3, 0, 0, 3, 0, 3, 3, 3, 3, 0], [0, 3, 0, 0, 0, 0, 3, 3, 0, 3, 0, 0, 3, 0, 0, 0, 3, 0, 3, 0], [3, 0, 3, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 0, 0, 0, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3, 3, 3], [0, 0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 3, 0, 3, 0, 3, 0, 3, 3, 3], [3, 0, 3, 3, 0, 3, 3, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 0, 3], [3, 0, 0, 3, 0, 0, 0, 3, 3, 3, 3, 0, 0, 3, 0, 3, 0, 3, 3, 3], [0, 3, 3, 0, 0, 0, 3, 3, 0, 3, 3, 3, 3, 0, 0, 3, 0, 0, 3, 3], [0, 0, 3, 0, 3, 3, 3, 3, 0, 0, 0, 3, 3, 3, 0, 0, 3, 0, 3, 0], [3, 0, 3, 3, 3, 0, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3], [0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 3, 0, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3], [0, 3, 0, 3, 0, 0, 3, 3, 3, 0, 3, 3, 3, 0, 0, 3, 3, 0, 0, 0], [3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3, 0, 0, 3, 3, 3, 3, 3, 3], [3, 0, 3, 3, 0, 3, 3, 3, 0, 0, 0, 3, 0, 3, 0, 3, 3, 3, 0, 3]], "output": [[3, 3, 3, 3, 0, 3, 0, 3, 0, 3, 3, 0, 0, 3, 3, 3, 0, 3, 0, 0], [0, 0, 3, 3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 3, 3, 0], [3, 3, 3, 3, 3, 0, 0, 3, 0, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 3], [3, 0, 3, 3, 0, 0, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 0, 3, 3, 0], [0, 0, 0, 3, 0, 3, 0, 3, 3, 3, 0, 3, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 0, 0, 3, 3, 0, 3, 3, 3, 3, 0, 0, 3, 0, 3, 3, 3, 3, 0], [0, 3, 0, 1, 1, 1, 3, 3, 0, 3, 0, 0, 3, 0, 0, 0, 3, 0, 3, 0], [3, 0, 3, 1, 1, 1, 0, 0, 0, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3], [0, 3, 3, 1, 1, 1, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3, 3, 3], [0, 0, 0, 3, 3, 0, 0, 3, 3, 3, 3, 3, 0, 3, 0, 3, 0, 3, 3, 3], [3, 0, 3, 3, 0, 3, 3, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 0, 3], [3, 0, 0, 3, 0, 0, 0, 3, 3, 3, 3, 0, 0, 3, 0, 3, 0, 3, 3, 3], [0, 3, 3, 0, 0, 0, 3, 3, 0, 3, 3, 3, 3, 0, 0, 3, 0, 0, 3, 3], [0, 0, 3, 0, 3, 3, 3, 3, 0, 0, 0, 3, 3, 3, 0, 0, 3, 0, 3, 0], [3, 0, 3, 3, 3, 0, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3], [0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 3, 0, 3, 3, 0, 3, 3, 3, 0, 0], [3, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3], [0, 3, 0, 3, 0, 0, 3, 3, 3, 0, 3, 3, 3, 0, 0, 3, 3, 0, 0, 0], [3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3, 0, 0, 3, 3, 3, 3, 3, 3], [3, 0, 3, 3, 0, 3, 3, 3, 0, 0, 0, 3, 0, 3, 0, 3, 3, 3, 0, 3]]}, {"input": [[7, 0, 7, 7, 7, 7, 0, 7, 7, 0, 0, 7, 7, 0, 0, 7, 0, 7, 7, 7], [0, 0, 7, 0, 7, 0, 7, 0, 7, 7, 7, 0, 0, 0, 0, 7, 7, 0, 0, 7], [0, 0, 0, 0, 0, 7, 0, 0, 7, 7, 7, 7, 0, 7, 0, 0, 0, 0, 7, 0], [7, 0, 7, 0, 7, 0, 7, 7, 0, 0, 0, 7, 7, 0, 0, 7, 7, 0, 7, 0], [0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 7, 7, 7, 7, 0, 0, 7, 0, 0, 7], [7, 7, 7, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 0, 0, 0, 7, 0, 7], [0, 0, 0, 7, 0, 7, 0, 0, 7, 7, 0, 7, 0, 7, 0, 0, 0, 0, 7, 7], [0, 7, 7, 7, 7, 0, 7, 0, 7, 0, 0, 7, 7, 7, 0, 0, 0, 0, 0, 7], [0, 0, 0, 7, 0, 0, 0, 0, 7, 7, 7, 0, 0, 7, 7, 0, 0, 0, 7, 7], [7, 7, 0, 7, 7, 7, 0, 7, 0, 0, 7, 0, 7, 7, 0, 7, 7, 0, 7, 0], [7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 7, 0, 0, 0, 0, 7, 7, 0], [7, 7, 0, 0, 7, 7, 7, 0, 7, 7, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7], [0, 7, 0, 7, 7, 7, 0, 0, 0, 7, 7, 0, 7, 7, 0, 7, 0, 0, 7, 7], [0, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 7, 0, 7, 7], [0, 0, 7, 7, 7, 0, 7, 0, 7, 7, 0, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 0, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 0, 0, 7, 7, 7, 0, 0], [7, 7, 7, 0, 0, 0, 7, 7, 7, 0, 7, 7, 0, 7, 0, 7, 0, 0, 0, 0], [7, 7, 7, 0, 0, 0, 7, 0, 7, 7, 0, 7, 0, 0, 7, 0, 0, 0, 0, 0], [7, 0, 0, 0, 0, 0, 7, 7, 0, 7, 0, 0, 0, 7, 0, 7, 7, 7, 0, 7], [0, 7, 7, 0, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7, 0, 0, 7, 0, 7, 7]], "output": [[7, 0, 7, 7, 7, 7, 0, 7, 7, 0, 0, 7, 7, 0, 0, 7, 0, 7, 7, 7], [0, 0, 7, 0, 7, 0, 7, 0, 7, 7, 7, 0, 0, 0, 0, 7, 7, 0, 0, 7], [0, 0, 0, 0, 0, 7, 0, 0, 7, 7, 7, 7, 0, 7, 0, 0, 0, 0, 7, 0], [7, 0, 7, 0, 7, 0, 7, 7, 0, 0, 0, 7, 7, 0, 0, 7, 7, 0, 7, 0], [0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 7, 7, 7, 7, 0, 0, 7, 0, 0, 7], [7, 7, 7, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 1, 1, 1, 7, 0, 7], [0, 0, 0, 7, 0, 7, 0, 0, 7, 7, 0, 7, 0, 7, 1, 1, 1, 0, 7, 7], [0, 7, 7, 7, 7, 0, 7, 0, 7, 0, 0, 7, 7, 7, 1, 1, 1, 0, 0, 7], [0, 0, 0, 7, 0, 0, 0, 0, 7, 7, 7, 0, 0, 7, 7, 0, 0, 0, 7, 7], [7, 7, 0, 7, 7, 7, 0, 7, 0, 0, 7, 0, 7, 7, 0, 7, 7, 0, 7, 0], [7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 7, 0, 0, 0, 0, 7, 7, 0], [7, 7, 0, 0, 7, 7, 7, 0, 7, 7, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7], [0, 7, 0, 7, 7, 7, 0, 0, 0, 7, 7, 0, 7, 7, 0, 7, 0, 0, 7, 7], [0, 0, 7, 7, 0, 7, 7, 7, 7, 7, 0, 7, 7, 0, 7, 7, 7, 0, 7, 7], [0, 0, 7, 7, 7, 0, 7, 0, 7, 7, 0, 7, 0, 7, 7, 7, 0, 7, 7, 7], [7, 0, 7, 7, 7, 0, 7, 0, 7, 7, 7, 7, 7, 0, 0, 7, 7, 7, 0, 0], [7, 7, 7, 1, 1, 1, 7, 7, 7, 0, 7, 7, 0, 7, 0, 7, 0, 0, 0, 0], [7, 7, 7, 1, 1, 1, 7, 0, 7, 7, 0, 7, 0, 0, 7, 0, 0, 0, 0, 0], [7, 0, 0, 1, 1, 1, 7, 7, 0, 7, 0, 0, 0, 7, 0, 7, 7, 7, 0, 7], [0, 7, 7, 0, 7, 7, 0, 7, 0, 0, 7, 7, 7, 7, 0, 0, 7, 0, 7, 7]]}], "test": [{"input": [[0, 4, 0, 4, 4, 0, 4, 4, 4, 0, 0, 0, 4, 0, 4, 4, 4, 4, 4, 0], [0, 0, 4, 4, 0, 0, 4, 0, 4, 4, 0, 0, 0, 0, 4, 4, 4, 4, 4, 0], [4, 4, 4, 0, 0, 4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 0], [4, 4, 0, 4, 0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 4, 4, 0, 4, 0, 0], [4, 0, 0, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 0, 4, 0, 4], [4, 4, 0, 0, 4, 0, 0, 4, 4, 4, 4, 4, 4, 0, 0, 4, 4, 0, 4, 0], [0, 0, 0, 4, 0, 0, 0, 0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4], [4, 0, 4, 4, 0, 0, 0, 4, 4, 0, 0, 0, 0, 4, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 0, 0], [4, 0, 0, 0, 4, 4, 0, 0, 4, 0, 0, 4, 0, 4, 4, 4, 0, 4, 0, 4], [0, 0, 0, 4, 0, 4, 0, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 4, 4], [0, 4, 4, 0, 0, 4, 4, 4, 4, 0, 0, 0, 4, 4, 4, 4, 4, 0, 4, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 4, 4, 0, 0, 0, 4, 4], [4, 0, 4, 4, 0, 4, 0, 0, 4, 4, 4, 0, 0, 0, 0, 4, 4, 4, 0, 0], [0, 4, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 4, 4, 0, 4, 4, 4, 4, 4], [4, 0, 0, 4, 4, 0, 4, 0, 4, 0, 0, 4, 0, 4, 0, 4, 0, 4, 0, 0], [4, 4, 0, 4, 0, 4, 0, 4, 4, 0, 0, 4, 4, 4, 0, 0, 0, 0, 4, 4], [4, 0, 0, 0, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 0, 0, 0, 4, 4, 4], [0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 4, 0, 0, 4, 0, 0, 0, 0, 0, 0], [4, 4, 0, 0, 0, 0, 0, 4, 4, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4, 4]], "output": [[0, 4, 0, 4, 4, 0, 4, 4, 4, 0, 0, 0, 4, 0, 4, 4, 4, 4, 4, 0], [0, 0, 4, 4, 0, 0, 4, 0, 4, 4, 0, 0, 0, 0, 4, 4, 4, 4, 4, 0], [4, 4, 4, 0, 0, 4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 0], [4, 4, 0, 4, 0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 4, 4, 0, 4, 0, 0], [4, 0, 0, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 0, 4, 0, 4], [4, 4, 0, 0, 4, 0, 0, 4, 4, 4, 4, 4, 4, 0, 0, 4, 4, 0, 4, 0], [0, 0, 0, 4, 1, 1, 1, 0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4], [4, 0, 4, 4, 1, 1, 1, 4, 4, 0, 0, 0, 0, 4, 4, 0, 0, 0, 0, 0], [0, 4, 4, 4, 1, 1, 1, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 0, 0], [4, 0, 0, 0, 4, 4, 0, 0, 4, 0, 0, 4, 0, 4, 4, 4, 0, 4, 0, 4], [0, 0, 0, 4, 0, 4, 0, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 4, 4], [0, 4, 4, 0, 0, 4, 4, 4, 4, 0, 0, 0, 4, 4, 4, 4, 4, 0, 4, 0], [0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 4, 4, 0, 0, 0, 4, 4], [4, 0, 4, 4, 0, 4, 0, 0, 4, 4, 4, 0, 0, 0, 0, 4, 4, 4, 0, 0], [0, 4, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 4, 4, 0, 4, 4, 4, 4, 4], [4, 0, 0, 4, 4, 0, 4, 0, 4, 0, 0, 4, 0, 4, 0, 4, 0, 4, 0, 0], [4, 4, 0, 4, 0, 4, 0, 4, 4, 0, 0, 4, 4, 4, 1, 1, 1, 0, 4, 4], [4, 0, 1, 1, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 1, 1, 1, 4, 4, 4], [0, 0, 1, 1, 1, 4, 4, 4, 4, 0, 4, 0, 0, 4, 1, 1, 1, 0, 0, 0], [4, 4, 1, 1, 1, 0, 0, 4, 4, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4, 4]]}]}