{"train": [{"input": [[1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8], [1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 0, 1, 1, 1, 0, 1, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8], [0, 1, 1, 0, 1, 1, 1, 1, 0, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8, 8], [1, 0, 1, 1, 1, 1, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8], [1, 1, 0, 1, 1, 1, 1, 1, 0, 8, 8, 8, 0, 8, 8, 8, 0, 8, 0, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 8, 8, 0, 8, 8, 8, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 6, 6, 6, 0, 6, 6, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 0, 0], [6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0], [0, 6, 0, 6, 6, 6, 0, 6, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 1], [6, 6, 6, 0, 6, 6, 6, 6, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1], [6, 0, 6, 6, 0, 6, 0, 6, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1], [6, 6, 6, 6, 6, 0, 6, 6, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [6, 6, 6, 6, 6, 0, 6, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [6, 6, 6, 0, 6, 6, 0, 6, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1], [0, 6, 6, 6, 0, 0, 6, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0], [6, 0, 0, 0, 6, 0, 6, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1], [6, 6, 0, 6, 0, 6, 6, 6, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 0]], "output": [[1, 8], [6, 1]]}, {"input": [[4, 4, 4, 4, 4, 0, 0, 8, 0, 8, 8, 8, 0, 0, 3, 3, 3, 0, 0, 3, 3, 3], [4, 4, 4, 0, 0, 4, 0, 8, 8, 8, 8, 8, 0, 0, 3, 3, 3, 3, 0, 3, 3, 0], [4, 4, 4, 4, 0, 0, 0, 8, 8, 0, 0, 8, 0, 0, 3, 3, 3, 0, 3, 0, 3, 3], [4, 4, 0, 0, 4, 4, 0, 8, 8, 8, 8, 8, 8, 0, 3, 3, 3, 3, 0, 3, 3, 3], [4, 4, 4, 4, 4, 4, 0, 0, 8, 8, 8, 8, 8, 0, 3, 0, 3, 0, 3, 0, 3, 0], [0, 0, 4, 4, 4, 4, 0, 8, 0, 8, 0, 8, 0, 0, 3, 0, 3, 3, 3, 3, 3, 3], [4, 4, 0, 4, 4, 0, 0, 8, 8, 8, 8, 0, 8, 0, 3, 0, 0, 3, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 2, 0, 2, 2, 2, 2, 0, 8, 0, 8, 0, 0, 8, 8, 8], [1, 0, 1, 1, 0, 1, 0, 2, 0, 2, 2, 2, 0, 0, 8, 8, 8, 0, 0, 8, 8, 8], [1, 1, 1, 0, 1, 0, 0, 2, 0, 2, 2, 2, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 0, 1, 0, 1, 0, 2, 2, 2, 2, 0, 2, 0, 0, 0, 8, 8, 8, 0, 8, 8], [1, 1, 1, 0, 1, 0, 0, 2, 2, 0, 2, 2, 0, 0, 0, 8, 0, 8, 8, 8, 8, 0], [1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 0, 2, 0, 8, 8, 0, 0, 8, 0, 8, 8], [1, 1, 1, 0, 0, 0, 0, 2, 0, 2, 2, 2, 2, 0, 8, 8, 0, 0, 0, 8, 8, 8], [1, 0, 0, 1, 0, 1, 0, 2, 2, 0, 2, 2, 0, 0, 8, 0, 8, 8, 0, 0, 0, 8], [1, 1, 1, 1, 0, 1, 0, 0, 2, 2, 2, 0, 2, 0, 0, 8, 8, 0, 0, 0, 8, 0], [1, 1, 0, 1, 1, 1, 0, 2, 2, 2, 0, 2, 0, 0, 8, 0, 8, 8, 0, 0, 8, 8]], "output": [[4, 8, 3], [1, 2, 8]]}, {"input": [[2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 7, 0, 0, 7, 0, 0], [2, 2, 0, 0, 2, 0, 2, 0, 7, 0, 7, 0, 7, 7, 7, 7, 0], [2, 2, 2, 2, 0, 2, 2, 0, 0, 7, 7, 0, 0, 7, 7, 0, 7], [2, 0, 2, 2, 0, 2, 2, 0, 0, 0, 7, 7, 7, 7, 7, 7, 0], [2, 2, 2, 0, 2, 2, 2, 0, 0, 7, 0, 7, 7, 7, 0, 0, 0], [2, 0, 2, 0, 2, 2, 2, 0, 7, 7, 0, 7, 7, 0, 0, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 0, 0, 0, 8, 0, 8, 8, 8, 8, 8, 8], [4, 0, 4, 4, 0, 4, 0, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8], [4, 0, 0, 4, 0, 4, 4, 0, 0, 8, 0, 8, 8, 0, 8, 0, 8], [4, 4, 0, 0, 0, 0, 4, 0, 8, 8, 0, 8, 8, 8, 8, 8, 8], [4, 4, 4, 4, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 0], [4, 4, 4, 4, 0, 4, 4, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 0, 0, 8, 8, 8, 0, 0, 8, 8, 8, 0], [0, 4, 4, 4, 0, 4, 4, 0, 8, 8, 0, 8, 8, 8, 8, 0, 8], [0, 0, 0, 0, 4, 4, 4, 0, 0, 8, 0, 0, 8, 0, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 6, 6, 0, 6, 6, 0, 6, 6, 6], [0, 1, 1, 1, 1, 1, 0, 0, 6, 6, 6, 0, 6, 6, 6, 6, 0], [1, 1, 1, 1, 1, 0, 1, 0, 6, 6, 6, 6, 0, 6, 6, 6, 6], [1, 0, 0, 0, 1, 1, 1, 0, 6, 6, 6, 0, 6, 6, 6, 6, 6], [1, 0, 1, 1, 1, 0, 0, 0, 6, 6, 6, 6, 6, 0, 0, 6, 6], [1, 1, 1, 1, 1, 1, 1, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[2, 7], [4, 8], [1, 6]]}], "test": [{"input": [[3, 3, 3, 0, 3, 3, 3, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [3, 3, 3, 3, 3, 3, 3, 0, 2, 2, 0, 2, 2, 2, 2, 0, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 3, 0, 0, 3, 3, 0, 0, 2, 2, 0, 0, 2, 2, 2, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [3, 0, 3, 3, 3, 3, 3, 0, 2, 0, 2, 2, 2, 2, 2, 0, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8, 0], [1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8, 8, 0], [1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8], [1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8], [0, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8], [1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8, 8, 0], [1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 8, 8, 8, 8, 8, 0, 0, 0, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 8], [1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 0], [1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8, 8, 8], [1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8], [0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 0, 7, 7, 0, 7, 0, 3, 3, 0, 0, 3, 3, 3, 0, 2, 0, 2, 2, 2, 2, 0, 2, 2, 0, 2, 2], [7, 7, 7, 0, 7, 7, 7, 0, 0, 3, 3, 0, 3, 0, 0, 0, 2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0], [7, 7, 7, 7, 7, 7, 7, 0, 3, 3, 3, 3, 3, 3, 3, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[3, 2, 4], [1, 1, 8], [7, 3, 2]]}]}