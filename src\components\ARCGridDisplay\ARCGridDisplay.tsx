import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { ARCGrid } from '../../types';

interface ARCGridDisplayProps {
  grid: ARCGrid;
  title?: string;
  showDiff?: boolean;
  diffGrid?: boolean[][];
  maxSize?: number;
  showValues?: boolean;
}

const GridContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const GridWrapper = styled(Box, {
  shouldForwardProp: (prop) => !['cellSize', 'width'].includes(prop as string),
})<{ cellSize: number; width: number }>(() => ({
  display: 'flex',
  flexDirection: 'column',
  gap: '1px',
  border: '2px solid #333',
  borderRadius: '4px',
  overflow: 'hidden',
  backgroundColor: '#333',
}));

const GridRow = styled(Box)(() => ({
  display: 'flex',
  gap: '1px',
}));

const GridCell = styled(Box, {
  shouldForwardProp: (prop) => !['cellSize', 'symbolValue', 'isError', 'showValues'].includes(prop as string),
})<{ 
  cellSize: number; 
  symbolValue: number; 
  isError?: boolean;
  showValues?: boolean;
}>(({ cellSize, symbolValue, isError, showValues }) => ({
  width: cellSize,
  height: cellSize,
  backgroundColor: `var(--symbol-${symbolValue}-bg)`,
  color: `var(--symbol-${symbolValue}-text)`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: showValues ? `${cellSize * 0.4}px` : '0',
  fontWeight: 'bold',
  border: isError ? '2px solid #ff0000' : 'none',
  position: 'relative',
  '&::after': isError ? {
    content: '"❌"',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    fontSize: `${cellSize * 0.3}px`,
    zIndex: 1,
  } : {},
}));

const ARCGridDisplay: React.FC<ARCGridDisplayProps> = ({
  grid,
  title,
  showDiff = false,
  diffGrid,
  maxSize = 300,
  showValues = false,
}) => {
  const cellSize = Math.min(maxSize / Math.max(grid.width, grid.height), 30);

  return (
    <GridContainer elevation={2}>
      {title && (
        <Typography variant="h6" component="h3" gutterBottom>
          {title}
        </Typography>
      )}
      <GridWrapper cellSize={cellSize} width={grid.width}>
        {grid.grid.map((row, rowIndex) => (
          <GridRow key={rowIndex}>
            {row.map((value, colIndex) => {
              const isError = showDiff && diffGrid?.[rowIndex]?.[colIndex] === false;
              
              return (
                <GridCell
                  key={`${rowIndex}-${colIndex}`}
                  cellSize={cellSize}
                  symbolValue={value}
                  isError={isError}
                  showValues={showValues}
                >
                  {showValues && value !== 0 ? value : ''}
                </GridCell>
              );
            })}
          </GridRow>
        ))}
      </GridWrapper>
      <Typography variant="caption" color="text.secondary">
        {grid.width} × {grid.height}
      </Typography>
    </GridContainer>
  );
};

export default ARCGridDisplay;