{"train": [{"input": [[9, 9, 6], [3, 8, 8], [8, 3, 3]], "output": [[0, 0, 0, 0, 0, 0, 9, 9, 6], [0, 0, 0, 0, 0, 0, 3, 8, 8], [0, 0, 0, 0, 0, 0, 8, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[8, 5, 5], [8, 8, 8], [5, 9, 9]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 5, 5, 8, 5, 5], [0, 0, 0, 8, 8, 8, 8, 8, 8], [0, 0, 0, 5, 9, 9, 5, 9, 9]]}, {"input": [[7, 1, 7], [1, 7, 7], [7, 1, 7]], "output": [[0, 0, 0, 7, 1, 7, 0, 0, 0], [0, 0, 0, 1, 7, 7, 0, 0, 0], [0, 0, 0, 7, 1, 7, 0, 0, 0], [7, 1, 7, 0, 0, 0, 0, 0, 0], [1, 7, 7, 0, 0, 0, 0, 0, 0], [7, 1, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 7, 1, 7, 0, 0, 0], [0, 0, 0, 1, 7, 7, 0, 0, 0], [0, 0, 0, 7, 1, 7, 0, 0, 0]]}, {"input": [[3, 2, 7], [2, 2, 7], [5, 5, 7]], "output": [[3, 2, 7, 0, 0, 0, 0, 0, 0], [2, 2, 7, 0, 0, 0, 0, 0, 0], [5, 5, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[1, 6, 6], [5, 1, 6], [5, 5, 5]], "output": [[1, 6, 6, 0, 0, 0, 0, 0, 0], [5, 1, 6, 0, 0, 0, 0, 0, 0], [5, 5, 5, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 6, 6, 0, 0, 0], [0, 0, 0, 5, 1, 6, 0, 0, 0], [0, 0, 0, 5, 5, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[4, 4, 2], [2, 2, 2], [2, 4, 2]], "output": [[4, 4, 2, 4, 4, 2, 0, 0, 0], [2, 2, 2, 2, 2, 2, 0, 0, 0], [2, 4, 2, 2, 4, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 4, 2, 0, 0, 0], [0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 2, 4, 2, 0, 0, 0]]}], "test": [{"input": [[9, 7, 9], [9, 9, 7], [7, 9, 7]], "output": [[0, 0, 0, 9, 7, 9, 0, 0, 0], [0, 0, 0, 9, 9, 7, 0, 0, 0], [0, 0, 0, 7, 9, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 9, 7, 9], [0, 0, 0, 0, 0, 0, 9, 9, 7], [0, 0, 0, 0, 0, 0, 7, 9, 7], [9, 7, 9, 0, 0, 0, 9, 7, 9], [9, 9, 7, 0, 0, 0, 9, 9, 7], [7, 9, 7, 0, 0, 0, 7, 9, 7]]}]}