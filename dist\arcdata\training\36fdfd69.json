{"train": [{"input": [[1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1, 1, 2, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0, 0], [1, 1, 1, 2, 1, 2, 2, 2, 2, 0, 1, 1, 1, 0, 0, 1, 1, 0], [1, 0, 2, 1, 2, 2, 2, 2, 2, 0, 1, 0, 0, 0, 1, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0], [1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0], [1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 1, 0], [0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2, 2, 1, 1], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0], [0, 1, 1, 0, 1, 1, 2, 1, 2, 1, 2, 1, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1], [0, 0, 0, 0, 0, 1, 1, 2, 1, 2, 2, 0, 0, 1, 0, 1, 1, 1], [0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0], [0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1]], "output": [[1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1], [1, 1, 2, 4, 4, 4, 4, 4, 4, 0, 0, 1, 0, 1, 1, 1, 0, 0], [1, 1, 4, 2, 4, 2, 2, 2, 2, 0, 1, 1, 1, 0, 0, 1, 1, 0], [1, 0, 2, 4, 2, 2, 2, 2, 2, 0, 1, 0, 0, 0, 1, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0], [1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0], [1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 4, 2, 1, 0], [0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2, 2, 1, 1], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0], [0, 1, 1, 0, 1, 1, 2, 4, 2, 4, 2, 1, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 0, 0, 1, 1, 0, 0, 1], [0, 0, 0, 0, 0, 1, 4, 2, 4, 2, 2, 0, 0, 1, 0, 1, 1, 1], [0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0], [0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1]]}, {"input": [[8, 0, 0, 0, 0, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 8, 0, 0], [0, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8], [0, 0, 8, 0, 8, 0, 0, 0, 0, 8, 0, 8, 8, 2, 8, 0], [0, 0, 2, 8, 2, 2, 2, 8, 0, 0, 0, 2, 8, 2, 8, 0], [8, 0, 2, 8, 2, 8, 8, 8, 0, 0, 0, 8, 0, 0, 8, 8], [8, 0, 0, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0, 0, 0], [8, 0, 8, 0, 8, 0, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8], [8, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 8, 8, 2, 8, 8, 8, 0, 8, 0, 0, 0, 8, 8, 8], [8, 0, 2, 8, 8, 2, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8], [0, 8, 0, 0, 0, 8, 8, 0, 0, 2, 8, 8, 0, 8, 8, 8], [8, 0, 0, 8, 8, 8, 8, 0, 0, 2, 8, 2, 0, 0, 0, 8], [0, 8, 8, 0, 8, 8, 8, 0, 0, 0, 8, 0, 8, 8, 8, 8], [8, 8, 8, 0, 8, 0, 8, 0, 0, 0, 8, 8, 8, 8, 8, 8]], "output": [[8, 0, 0, 0, 0, 8, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 8, 0, 0], [0, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8], [0, 0, 8, 0, 8, 0, 0, 0, 0, 8, 0, 4, 4, 2, 8, 0], [0, 0, 2, 4, 2, 2, 2, 8, 0, 0, 0, 2, 4, 2, 8, 0], [8, 0, 2, 4, 2, 4, 4, 8, 0, 0, 0, 8, 0, 0, 8, 8], [8, 0, 0, 8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0, 0, 0], [8, 0, 8, 0, 8, 0, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8], [8, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 4, 4, 2, 4, 8, 8, 0, 8, 0, 0, 0, 8, 8, 8], [8, 0, 2, 4, 4, 2, 8, 8, 0, 8, 0, 0, 8, 8, 0, 8], [0, 8, 0, 0, 0, 8, 8, 0, 0, 2, 4, 4, 0, 8, 8, 8], [8, 0, 0, 8, 8, 8, 8, 0, 0, 2, 4, 2, 0, 0, 0, 8], [0, 8, 8, 0, 8, 8, 8, 0, 0, 0, 8, 0, 8, 8, 8, 8], [8, 8, 8, 0, 8, 0, 8, 0, 0, 0, 8, 8, 8, 8, 8, 8]]}, {"input": [[3, 3, 0, 0, 0, 0, 0, 3, 0, 3, 3, 0, 0, 0], [0, 0, 3, 0, 0, 3, 3, 0, 3, 0, 0, 0, 3, 0], [0, 0, 3, 3, 0, 0, 0, 3, 3, 3, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0, 3, 3], [0, 0, 0, 2, 2, 2, 2, 3, 0, 0, 0, 3, 0, 3], [0, 3, 3, 2, 2, 3, 3, 2, 0, 0, 0, 3, 3, 0], [0, 3, 0, 2, 2, 2, 3, 2, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 3, 3, 0, 3, 0, 0, 0, 0, 3], [0, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3], [3, 3, 3, 2, 0, 3, 3, 0, 0, 0, 3, 0, 3, 0], [0, 3, 2, 3, 0, 0, 0, 3, 3, 0, 0, 0, 3, 0], [0, 3, 3, 0, 3, 3, 0, 0, 3, 3, 0, 3, 0, 3], [0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 3, 3, 0, 3], [0, 3, 3, 0, 3, 0, 3, 0, 3, 0, 0, 0, 0, 0], [3, 0, 0, 3, 0, 0, 0, 0, 0, 3, 3, 0, 3, 3]], "output": [[3, 3, 0, 0, 0, 0, 0, 3, 0, 3, 3, 0, 0, 0], [0, 0, 3, 0, 0, 3, 3, 0, 3, 0, 0, 0, 3, 0], [0, 0, 3, 3, 0, 0, 0, 3, 3, 3, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0, 3, 3], [0, 0, 0, 2, 2, 2, 2, 4, 0, 0, 0, 3, 0, 3], [0, 3, 3, 2, 2, 4, 4, 2, 0, 0, 0, 3, 3, 0], [0, 3, 0, 2, 2, 2, 4, 2, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 3, 3, 0, 3, 0, 0, 0, 0, 3], [0, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 0, 3, 3], [3, 3, 4, 2, 0, 3, 3, 0, 0, 0, 3, 0, 3, 0], [0, 3, 2, 4, 0, 0, 0, 3, 3, 0, 0, 0, 3, 0], [0, 3, 3, 0, 3, 3, 0, 0, 3, 3, 0, 3, 0, 3], [0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 3, 3, 0, 3], [0, 3, 3, 0, 3, 0, 3, 0, 3, 0, 0, 0, 0, 0], [3, 0, 0, 3, 0, 0, 0, 0, 0, 3, 3, 0, 3, 3]]}], "test": [{"input": [[0, 0, 0, 9, 9, 9, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 9, 0], [9, 2, 9, 2, 2, 9, 0, 0, 0, 9, 0, 0, 9, 0, 0, 0, 0, 0], [0, 2, 2, 9, 9, 2, 0, 0, 9, 9, 9, 0, 0, 9, 0, 0, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 9, 9, 9, 9, 0, 9, 0], [0, 9, 9, 0, 0, 0, 9, 0, 9, 9, 0, 9, 0, 0, 9, 9, 9, 9], [9, 9, 9, 9, 0, 9, 2, 9, 2, 2, 9, 0, 0, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 9, 2, 2, 2, 2, 9, 0, 9, 9, 0, 0, 0, 0], [9, 0, 9, 9, 0, 9, 0, 0, 9, 0, 9, 9, 0, 9, 9, 9, 0, 9], [0, 0, 0, 9, 0, 0, 0, 9, 9, 9, 9, 9, 0, 9, 0, 0, 0, 0], [9, 9, 0, 9, 0, 9, 0, 9, 9, 0, 0, 9, 9, 0, 0, 0, 0, 9], [0, 9, 9, 0, 9, 0, 9, 2, 9, 0, 0, 9, 0, 0, 9, 9, 9, 9], [0, 9, 9, 0, 0, 9, 2, 9, 9, 9, 0, 0, 0, 9, 9, 9, 0, 9], [9, 0, 9, 9, 0, 9, 9, 9, 0, 0, 9, 0, 0, 0, 9, 9, 9, 0], [9, 9, 9, 9, 9, 9, 0, 0, 0, 0, 9, 2, 2, 9, 2, 2, 9, 0], [0, 9, 9, 9, 9, 9, 9, 0, 9, 0, 0, 2, 9, 2, 9, 9, 2, 9], [0, 9, 0, 9, 0, 0, 9, 9, 0, 9, 0, 2, 2, 9, 2, 2, 9, 0], [9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 9, 9, 0]], "output": [[0, 0, 0, 9, 9, 9, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 9, 0], [9, 2, 4, 2, 2, 4, 0, 0, 0, 9, 0, 0, 9, 0, 0, 0, 0, 0], [0, 2, 2, 4, 4, 2, 0, 0, 9, 9, 9, 0, 0, 9, 0, 0, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 9, 9, 9, 9, 0, 9, 0], [0, 9, 9, 0, 0, 0, 9, 0, 9, 9, 0, 9, 0, 0, 9, 9, 9, 9], [9, 9, 9, 9, 0, 9, 2, 4, 2, 2, 9, 0, 0, 9, 0, 0, 0, 0], [0, 0, 0, 0, 0, 9, 2, 2, 2, 2, 9, 0, 9, 9, 0, 0, 0, 0], [9, 0, 9, 9, 0, 9, 0, 0, 9, 0, 9, 9, 0, 9, 9, 9, 0, 9], [0, 0, 0, 9, 0, 0, 0, 9, 9, 9, 9, 9, 0, 9, 0, 0, 0, 0], [9, 9, 0, 9, 0, 9, 0, 9, 9, 0, 0, 9, 9, 0, 0, 0, 0, 9], [0, 9, 9, 0, 9, 0, 4, 2, 9, 0, 0, 9, 0, 0, 9, 9, 9, 9], [0, 9, 9, 0, 0, 9, 2, 4, 9, 9, 0, 0, 0, 9, 9, 9, 0, 9], [9, 0, 9, 9, 0, 9, 9, 9, 0, 0, 9, 0, 0, 0, 9, 9, 9, 0], [9, 9, 9, 9, 9, 9, 0, 0, 0, 0, 9, 2, 2, 4, 2, 2, 4, 0], [0, 9, 9, 9, 9, 9, 9, 0, 9, 0, 0, 2, 4, 2, 4, 4, 2, 9], [0, 9, 0, 9, 0, 0, 9, 9, 0, 9, 0, 2, 2, 4, 2, 2, 4, 0], [9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 9, 9, 0]]}]}