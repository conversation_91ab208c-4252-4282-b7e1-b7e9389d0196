import { useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { LevelStatus } from '../types/AnalysisPanelTypes';

export const useAnalysisState = () => {
  const { currentPuzzle, analysis, showValues } = useSelector((state: RootState) => state.puzzle);
  const [lastAnalysisTime, setLastAnalysisTime] = useState<string | null>(null);

  const getLevelStatus = useCallback((level: string): LevelStatus => {
    if (!analysis) return 'pending';

    switch (level) {
      case 'level0':
        return analysis.level_0 ? 'complete' : 'pending';
      case 'level1':
        return analysis.level_1 ? 'complete' : 'pending';
      case 'level2':
        return analysis.level_2 ? 'complete' : 'pending';
      case 'level3':
        return analysis.level_3 ? 'complete' : 'pending';
      default:
        return 'pending';
    }
  }, [analysis]);

  return {
    currentPuzzle,
    analysis,
    showValues,
    lastAnalysisTime,
    setLastAnalysisTime,
    getLevelStatus
  };
};