{"test": [{"input": [[6, 6, 0], [6, 6, 0], [0, 0, 6]], "output": [[6, 6, 0, 0, 0, 6], [6, 6, 0, 6, 6, 0], [0, 0, 6, 6, 6, 0], [6, 0, 0, 0, 6, 6], [0, 6, 6, 0, 6, 6], [0, 6, 6, 6, 0, 0]]}], "train": [{"input": [[9, 9, 9], [0, 0, 0], [9, 9, 0]], "output": [[9, 9, 9, 9, 0, 0], [0, 0, 0, 9, 0, 9], [9, 9, 0, 9, 0, 9], [0, 9, 9, 9, 0, 9], [0, 0, 0, 9, 0, 9], [9, 9, 9, 0, 0, 9]]}, {"input": [[3, 0, 3], [0, 3, 3], [3, 3, 3]], "output": [[3, 0, 3, 3, 3, 3], [0, 3, 3, 0, 3, 3], [3, 3, 3, 3, 0, 3], [3, 3, 3, 3, 0, 3], [3, 3, 0, 3, 3, 0], [3, 0, 3, 3, 3, 3]]}, {"input": [[3, 3, 3], [0, 0, 3], [3, 0, 0]], "output": [[3, 3, 3, 3, 3, 0], [0, 0, 3, 3, 0, 0], [3, 0, 0, 3, 0, 3], [0, 0, 3, 3, 0, 3], [3, 0, 0, 0, 0, 3], [3, 3, 3, 0, 3, 3]]}, {"input": [[8, 0, 8], [8, 0, 0], [8, 0, 0]], "output": [[8, 0, 8, 8, 0, 0], [8, 0, 0, 0, 0, 0], [8, 0, 0, 8, 8, 8], [0, 0, 8, 8, 8, 8], [0, 0, 8, 0, 0, 0], [8, 0, 8, 0, 0, 8]]}, {"input": [[0, 7, 7], [0, 0, 0], [7, 7, 0]], "output": [[0, 7, 7, 7, 0, 0], [0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 7], [0, 7, 7, 7, 0, 0], [0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 7]]}]}