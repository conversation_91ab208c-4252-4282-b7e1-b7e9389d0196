{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 3, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 8, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 3, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 1, 2, 0, 0, 1, 4, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 4, 0, 0, 0, 0], [0, 7, 6, 7, 0, 0, 0, 0, 1, 4, 1, 0, 0, 0, 0], [0, 6, 7, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 6, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 2, 1, 4, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 6, 7, 1, 4, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 6, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 6, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 1, 0, 0, 0, 5, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 5, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 6, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 9, 0, 0, 4, 6, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 5, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 9, 4, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 9, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 9, 0, 0, 0, 0, 0, 3, 7, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 6, 8, 6, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 6, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 6, 8, 6, 0, 0, 0, 8, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9, 3, 7, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 6, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 8, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 6, 8, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 6, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 8, 6, 0, 2, 5, 2, 5, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 5, 2, 5, 2, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 2, 5, 2, 5, 2, 0, 0, 0], [0, 0, 2, 1, 0, 0, 0, 5, 2, 5, 2, 5, 0, 0, 0], [0, 0, 1, 2, 0, 0, 0, 2, 5, 2, 5, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 3, 0, 0, 0, 0, 8, 4, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 4, 8, 0, 0, 0, 0, 0], [0, 0, 1, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 6, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 2, 5, 2, 5, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 2, 5, 2, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5, 2, 5, 2], [0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 5, 2, 5, 2, 5], [0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 2, 5, 2, 5, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 8, 4, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 8, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 4, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1]]}]}