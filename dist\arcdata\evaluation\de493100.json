{"test": [{"input": [[2, 2, 1, 6, 9, 9, 9, 9, 4, 4, 5, 1, 4, 4, 5, 5, 5, 5, 4, 4, 1, 5, 4, 4, 9, 9, 9, 9, 6, 1], [1, 1, 6, 3, 9, 9, 1, 9, 5, 3, 1, 5, 1, 4, 3, 5, 5, 3, 4, 1, 5, 1, 3, 5, 9, 1, 9, 9, 3, 6], [2, 6, 1, 2, 9, 1, 9, 9, 5, 4, 3, 4, 5, 3, 4, 1, 1, 4, 3, 5, 4, 3, 4, 5, 9, 9, 1, 9, 2, 1], [6, 3, 1, 2, 9, 9, 9, 9, 4, 5, 5, 4, 5, 5, 4, 4, 4, 4, 5, 5, 4, 5, 5, 4, 9, 9, 9, 9, 2, 1], [8, 8, 8, 8, 2, 2, 3, 6, 4, 1, 5, 5, 8, 4, 4, 4, 4, 4, 4, 8, 5, 5, 1, 4, 6, 3, 2, 2, 8, 8], [8, 8, 5, 8, 1, 1, 6, 1, 4, 4, 3, 5, 9, 3, 4, 4, 4, 4, 3, 9, 5, 3, 4, 4, 1, 6, 1, 1, 8, 5], [8, 5, 8, 8, 3, 6, 1, 2, 5, 3, 4, 4, 4, 9, 3, 4, 4, 3, 9, 4, 4, 4, 3, 5, 2, 1, 6, 3, 8, 8], [8, 8, 8, 8, 6, 2, 1, 2, 5, 5, 1, 4, 9, 4, 9, 8, 8, 9, 4, 9, 4, 1, 5, 5, 2, 1, 2, 6, 8, 8], [4, 5, 5, 4, 4, 4, 5, 5, 1, 9, 1, 9, 8, 6, 8, 6, 6, 8, 6, 8, 9, 1, 9, 1, 5, 5, 4, 4, 4, 5], [4, 3, 4, 5, 1, 4, 3, 5, 8, 3, 9, 9, 6, 8, 8, 8, 8, 8, 8, 6, 9, 9, 3, 8, 5, 3, 4, 1, 5, 4], [5, 1, 3, 5, 5, 3, 4, 1, 9, 9, 3, 9, 8, 8, 8, 6, 6, 8, 8, 8, 9, 3, 9, 9, 1, 4, 3, 5, 5, 3], [1, 5, 4, 4, 5, 5, 4, 4, 9, 3, 8, 1, 6, 8, 6, 8, 8, 6, 8, 6, 1, 8, 3, 9, 4, 4, 5, 5, 4, 4], [4, 1, 5, 5, 8, 9, 4, 9, 9, 9, 8, 8, 1, 9, 9, 9, 9, 9, 9, 1, 8, 8, 9, 9, 9, 4, 9, 8, 5, 5], [4, 4, 3, 5, 4, 3, 9, 4, 9, 9, 2, 8, 8, 3, 9, 1, 1, 9, 3, 8, 8, 2, 9, 9, 4, 9, 3, 4, 5, 3], [5, 3, 4, 4, 4, 4, 3, 9, 8, 2, 9, 9, 3, 9, 3, 9, 9, 3, 9, 3, 9, 9, 2, 8, 9, 3, 4, 4, 4, 4], [5, 5, 1, 4, 4, 4, 4, 8, 8, 8, 9, 9, 9, 9, 8, 1, 1, 8, 9, 9, 9, 9, 8, 8, 8, 4, 4, 4, 4, 1], [5, 5, 1, 4, 4, 4, 4, 8, 8, 8, 9, 9, 9, 9, 8, 1, 1, 8, 9, 7, 7, 9, 8, 8, 8, 4, 4, 4, 4, 1], [5, 3, 4, 4, 4, 4, 3, 9, 8, 2, 9, 9, 3, 9, 3, 9, 9, 3, 9, 7, 7, 9, 2, 8, 9, 3, 4, 4, 4, 4], [4, 4, 3, 5, 4, 3, 9, 4, 9, 9, 2, 8, 8, 3, 9, 1, 1, 9, 3, 7, 7, 2, 9, 9, 4, 9, 3, 4, 5, 3], [4, 1, 5, 5, 8, 9, 4, 9, 9, 9, 8, 8, 1, 9, 9, 9, 9, 9, 9, 7, 7, 8, 9, 9, 9, 4, 9, 8, 5, 5], [1, 5, 4, 4, 5, 5, 4, 4, 9, 3, 8, 1, 6, 8, 6, 8, 8, 6, 8, 7, 7, 8, 3, 9, 4, 4, 5, 5, 4, 4], [5, 1, 3, 5, 5, 3, 4, 1, 9, 9, 3, 9, 8, 8, 8, 6, 6, 8, 8, 7, 7, 3, 9, 9, 1, 4, 3, 5, 5, 3], [4, 3, 4, 5, 1, 4, 3, 5, 8, 3, 9, 9, 6, 8, 8, 8, 8, 8, 8, 7, 7, 9, 3, 8, 5, 3, 4, 1, 5, 4], [4, 5, 5, 4, 4, 4, 5, 5, 1, 9, 1, 9, 8, 6, 8, 6, 6, 8, 6, 7, 7, 1, 9, 1, 5, 5, 4, 4, 4, 5], [8, 8, 8, 8, 6, 2, 1, 2, 5, 5, 1, 4, 9, 4, 9, 8, 8, 9, 4, 7, 7, 1, 5, 5, 2, 1, 2, 6, 8, 8], [8, 5, 8, 8, 3, 6, 1, 2, 5, 3, 4, 4, 4, 9, 3, 4, 4, 3, 9, 7, 7, 4, 3, 5, 2, 1, 6, 3, 8, 8], [8, 8, 5, 8, 1, 1, 6, 1, 4, 4, 3, 5, 9, 3, 4, 4, 4, 4, 3, 9, 5, 3, 4, 4, 1, 6, 1, 1, 8, 5], [8, 8, 8, 8, 2, 2, 3, 6, 4, 1, 5, 5, 8, 4, 4, 4, 4, 4, 4, 8, 5, 5, 1, 4, 6, 3, 2, 2, 8, 8], [6, 3, 1, 2, 9, 9, 9, 9, 4, 5, 5, 4, 5, 5, 4, 4, 4, 4, 5, 5, 4, 5, 5, 4, 9, 9, 9, 9, 2, 1], [2, 6, 1, 2, 9, 1, 9, 9, 5, 4, 3, 4, 5, 3, 4, 1, 1, 4, 3, 5, 4, 3, 4, 5, 9, 9, 1, 9, 2, 1]], "output": [[9, 9], [3, 9], [8, 8], [1, 8], [6, 1], [8, 9], [6, 9], [8, 9], [9, 4], [4, 4]]}], "train": [{"input": [[2, 9, 2, 9, 6, 4, 6, 2, 1, 2, 6, 6, 1, 1, 9, 4, 4, 9, 1, 1, 6, 6, 2, 1, 2, 6, 4, 6, 9, 2], [5, 5, 9, 5, 4, 6, 6, 6, 3, 6, 6, 6, 1, 1, 9, 9, 9, 9, 1, 1, 6, 6, 6, 3, 6, 6, 6, 4, 5, 9], [9, 9, 5, 9, 6, 6, 6, 4, 3, 2, 6, 2, 9, 9, 8, 4, 4, 8, 9, 9, 2, 6, 2, 3, 4, 6, 6, 6, 9, 5], [9, 9, 5, 2, 2, 6, 4, 6, 2, 3, 3, 1, 4, 9, 9, 8, 8, 9, 9, 4, 1, 3, 3, 2, 6, 4, 6, 2, 2, 5], [9, 8, 1, 8, 2, 9, 5, 9, 1, 1, 9, 4, 6, 6, 6, 6, 6, 6, 6, 6, 4, 9, 1, 1, 9, 5, 9, 2, 8, 1], [8, 9, 1, 1, 5, 5, 9, 2, 1, 1, 9, 9, 9, 9, 6, 6, 6, 6, 9, 9, 9, 9, 1, 1, 2, 9, 5, 5, 1, 1], [1, 1, 9, 8, 9, 9, 5, 9, 9, 9, 8, 9, 9, 1, 9, 6, 6, 9, 1, 9, 9, 8, 9, 9, 9, 5, 9, 9, 8, 9], [8, 1, 8, 9, 9, 9, 5, 2, 4, 9, 4, 8, 1, 9, 9, 6, 6, 9, 9, 1, 8, 4, 9, 4, 2, 5, 9, 9, 9, 8], [1, 3, 3, 2, 1, 1, 9, 4, 9, 9, 3, 4, 8, 8, 3, 8, 8, 3, 8, 8, 4, 3, 9, 9, 4, 9, 1, 1, 2, 3], [2, 6, 2, 3, 1, 1, 9, 9, 4, 4, 4, 8, 8, 8, 6, 3, 3, 6, 8, 8, 8, 4, 4, 4, 9, 9, 1, 1, 3, 2], [6, 6, 6, 3, 9, 9, 8, 4, 8, 9, 4, 9, 3, 6, 8, 8, 8, 8, 6, 3, 9, 4, 9, 8, 4, 8, 9, 9, 3, 6], [6, 6, 2, 1, 4, 9, 9, 8, 9, 3, 4, 9, 8, 3, 8, 8, 8, 8, 3, 8, 9, 4, 3, 9, 8, 9, 9, 4, 1, 2], [1, 1, 9, 4, 6, 9, 9, 1, 9, 9, 4, 1, 9, 9, 8, 4, 4, 8, 9, 9, 1, 4, 9, 9, 1, 9, 9, 6, 4, 9], [1, 1, 9, 9, 6, 9, 1, 9, 9, 9, 1, 4, 4, 4, 4, 3, 3, 4, 4, 4, 4, 1, 9, 9, 9, 1, 9, 6, 9, 9], [9, 9, 8, 9, 6, 6, 9, 9, 4, 1, 9, 9, 3, 9, 4, 9, 9, 4, 9, 3, 9, 9, 1, 4, 9, 9, 6, 6, 9, 8], [4, 9, 4, 8, 6, 6, 6, 6, 1, 4, 9, 9, 9, 8, 4, 9, 9, 4, 8, 9, 9, 9, 4, 1, 6, 6, 6, 6, 8, 4], [4, 9, 4, 8, 6, 6, 6, 6, 1, 4, 9, 9, 9, 8, 4, 9, 9, 4, 8, 9, 9, 9, 4, 1, 6, 6, 6, 6, 8, 4], [9, 9, 8, 9, 6, 6, 9, 9, 4, 1, 9, 9, 3, 9, 4, 9, 9, 4, 9, 3, 9, 9, 1, 4, 9, 9, 6, 6, 9, 8], [1, 1, 9, 9, 6, 9, 1, 9, 9, 9, 1, 4, 4, 4, 4, 3, 3, 4, 4, 4, 4, 1, 9, 9, 9, 1, 9, 6, 9, 9], [1, 1, 9, 4, 6, 9, 9, 1, 9, 9, 4, 1, 9, 9, 8, 4, 4, 8, 9, 9, 1, 4, 9, 9, 1, 9, 9, 6, 4, 9], [6, 6, 2, 1, 4, 9, 9, 8, 9, 3, 4, 9, 8, 3, 8, 8, 8, 8, 3, 8, 9, 7, 7, 7, 7, 7, 7, 7, 7, 2], [6, 6, 6, 3, 9, 9, 8, 4, 8, 9, 4, 9, 3, 6, 8, 8, 8, 8, 6, 3, 9, 7, 7, 7, 7, 7, 7, 7, 7, 6], [2, 6, 2, 3, 1, 1, 9, 9, 4, 4, 4, 8, 8, 8, 6, 3, 3, 6, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 2], [1, 3, 3, 2, 1, 1, 9, 4, 9, 9, 3, 4, 8, 8, 3, 8, 8, 3, 8, 8, 4, 7, 7, 7, 7, 7, 7, 7, 7, 3], [8, 1, 8, 9, 9, 9, 5, 2, 4, 9, 4, 8, 1, 9, 9, 6, 6, 9, 9, 1, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8], [1, 1, 9, 8, 9, 9, 5, 9, 9, 9, 8, 9, 9, 1, 9, 6, 6, 9, 1, 9, 9, 7, 7, 7, 7, 7, 7, 7, 7, 9], [8, 9, 1, 1, 5, 5, 9, 2, 1, 1, 9, 9, 9, 9, 6, 6, 6, 6, 9, 9, 9, 9, 1, 1, 2, 9, 5, 5, 1, 1], [9, 8, 1, 8, 2, 9, 5, 9, 1, 1, 9, 4, 6, 6, 6, 6, 6, 6, 6, 6, 4, 9, 1, 1, 9, 5, 9, 2, 8, 1], [9, 9, 5, 2, 2, 6, 4, 6, 2, 3, 3, 1, 4, 9, 9, 8, 8, 9, 9, 4, 1, 3, 3, 2, 6, 4, 6, 2, 2, 5], [9, 9, 5, 9, 6, 6, 6, 4, 3, 2, 6, 2, 9, 9, 8, 4, 4, 8, 9, 9, 2, 6, 2, 3, 4, 6, 6, 6, 9, 5]], "output": [[4, 3, 9, 8, 9, 9, 4, 1], [4, 9, 8, 4, 8, 9, 9, 3], [4, 4, 4, 9, 9, 1, 1, 3], [3, 9, 9, 4, 9, 1, 1, 2], [4, 9, 4, 2, 5, 9, 9, 9], [8, 9, 9, 9, 5, 9, 9, 8]]}, {"input": [[3, 3, 2, 2, 8, 9, 9, 8, 8, 5, 5, 5, 1, 3, 3, 1, 1, 3, 3, 1, 5, 7, 7, 7, 7, 7, 9, 8, 2, 2], [1, 1, 2, 1, 9, 8, 8, 9, 5, 2, 5, 5, 3, 1, 1, 9, 9, 1, 1, 3, 5, 7, 7, 7, 7, 7, 8, 9, 1, 2], [2, 2, 1, 3, 9, 8, 8, 9, 8, 5, 2, 5, 3, 1, 9, 2, 2, 9, 1, 3, 5, 7, 7, 7, 7, 7, 8, 9, 3, 1], [2, 9, 1, 3, 8, 9, 9, 8, 5, 8, 5, 8, 1, 9, 1, 9, 9, 1, 9, 1, 8, 7, 7, 7, 7, 7, 9, 8, 3, 1], [8, 3, 8, 1, 3, 3, 1, 2, 1, 3, 3, 1, 9, 4, 9, 8, 8, 9, 4, 9, 1, 7, 7, 7, 7, 7, 3, 3, 1, 8], [3, 8, 3, 8, 1, 1, 2, 2, 3, 1, 1, 9, 2, 8, 8, 9, 9, 8, 8, 2, 9, 7, 7, 7, 7, 7, 1, 1, 8, 3], [8, 3, 8, 3, 9, 2, 1, 3, 3, 1, 9, 1, 9, 9, 8, 4, 4, 8, 9, 9, 1, 7, 7, 7, 7, 7, 2, 9, 3, 8], [1, 8, 3, 8, 2, 2, 1, 3, 1, 9, 2, 9, 9, 9, 2, 9, 9, 2, 9, 9, 9, 2, 9, 1, 3, 1, 2, 2, 8, 3], [8, 5, 8, 5, 1, 3, 3, 1, 3, 2, 6, 2, 9, 9, 6, 9, 9, 6, 9, 9, 2, 6, 2, 3, 1, 3, 3, 1, 5, 8], [5, 2, 5, 8, 3, 1, 1, 9, 1, 1, 2, 2, 9, 9, 4, 6, 6, 4, 9, 9, 2, 2, 1, 1, 9, 1, 1, 3, 8, 5], [5, 5, 2, 5, 3, 1, 9, 2, 6, 3, 1, 2, 6, 4, 9, 9, 9, 9, 4, 6, 2, 1, 3, 6, 2, 9, 1, 3, 5, 2], [5, 5, 5, 8, 1, 9, 1, 9, 3, 6, 1, 3, 9, 6, 9, 9, 9, 9, 6, 9, 3, 1, 6, 3, 9, 1, 9, 1, 8, 5], [1, 3, 3, 1, 9, 2, 9, 9, 8, 4, 3, 2, 3, 2, 2, 2, 2, 2, 2, 3, 2, 3, 4, 8, 9, 9, 2, 9, 1, 3], [3, 1, 1, 9, 4, 8, 9, 9, 4, 8, 2, 3, 1, 1, 2, 6, 6, 2, 1, 1, 3, 2, 8, 4, 9, 9, 8, 4, 9, 1], [3, 1, 9, 1, 9, 8, 8, 2, 3, 2, 8, 4, 6, 3, 1, 2, 2, 1, 3, 6, 4, 8, 2, 3, 2, 8, 8, 9, 1, 9], [1, 9, 2, 9, 8, 9, 4, 9, 2, 3, 4, 8, 3, 6, 1, 3, 3, 1, 6, 3, 8, 4, 3, 2, 9, 4, 9, 8, 9, 2], [1, 9, 2, 9, 8, 9, 4, 9, 2, 3, 4, 8, 3, 6, 1, 3, 3, 1, 6, 3, 8, 4, 3, 2, 9, 4, 9, 8, 9, 2], [3, 1, 9, 1, 9, 8, 8, 2, 3, 2, 8, 4, 6, 3, 1, 2, 2, 1, 3, 6, 4, 8, 2, 3, 2, 8, 8, 9, 1, 9], [3, 1, 1, 9, 4, 8, 9, 9, 4, 8, 2, 3, 1, 1, 2, 6, 6, 2, 1, 1, 3, 2, 8, 4, 9, 9, 8, 4, 9, 1], [1, 3, 3, 1, 9, 2, 9, 9, 8, 4, 3, 2, 3, 2, 2, 2, 2, 2, 2, 3, 2, 3, 4, 8, 9, 9, 2, 9, 1, 3], [5, 5, 5, 8, 1, 9, 1, 9, 3, 6, 1, 3, 9, 6, 9, 9, 9, 9, 6, 9, 3, 1, 6, 3, 9, 1, 9, 1, 8, 5], [5, 5, 2, 5, 3, 1, 9, 2, 6, 3, 1, 2, 6, 4, 9, 9, 9, 9, 4, 6, 2, 1, 3, 6, 2, 9, 1, 3, 5, 2], [5, 2, 5, 8, 3, 1, 1, 9, 1, 1, 2, 2, 9, 9, 4, 6, 6, 4, 9, 9, 2, 2, 1, 1, 9, 1, 1, 3, 8, 5], [8, 5, 8, 5, 1, 3, 3, 1, 3, 2, 6, 2, 9, 9, 6, 9, 9, 6, 9, 9, 2, 6, 2, 3, 1, 3, 3, 1, 5, 8], [1, 8, 3, 8, 2, 2, 1, 3, 1, 9, 2, 9, 9, 9, 2, 9, 9, 2, 9, 9, 9, 2, 9, 1, 3, 1, 2, 2, 8, 3], [8, 3, 8, 3, 9, 2, 1, 3, 3, 1, 9, 1, 9, 9, 8, 4, 4, 8, 9, 9, 1, 9, 1, 3, 3, 1, 2, 9, 3, 8], [3, 8, 3, 8, 1, 1, 2, 2, 3, 1, 1, 9, 2, 8, 8, 9, 9, 8, 8, 2, 9, 1, 1, 3, 2, 2, 1, 1, 8, 3], [8, 3, 8, 1, 3, 3, 1, 2, 1, 3, 3, 1, 9, 4, 9, 8, 8, 9, 4, 9, 1, 3, 3, 1, 2, 1, 3, 3, 1, 8], [2, 9, 1, 3, 8, 9, 9, 8, 5, 8, 5, 8, 1, 9, 1, 9, 9, 1, 9, 1, 8, 5, 8, 5, 8, 9, 9, 8, 3, 1], [2, 2, 1, 3, 9, 8, 8, 9, 8, 5, 2, 5, 3, 1, 9, 2, 2, 9, 1, 3, 5, 2, 5, 8, 9, 8, 8, 9, 3, 1]], "output": [[5, 5, 8, 8, 9], [5, 2, 5, 9, 8], [2, 5, 8, 9, 8], [5, 8, 5, 8, 9], [3, 3, 1, 2, 1], [1, 1, 3, 2, 2], [9, 1, 3, 3, 1]]}, {"input": [[4, 3, 3, 8, 8, 8, 4, 4, 2, 3, 8, 2, 6, 2, 2, 6, 6, 2, 2, 6, 2, 8, 3, 2, 4, 4, 8, 8, 8, 3], [4, 3, 8, 8, 8, 8, 4, 4, 3, 8, 2, 8, 9, 6, 6, 6, 6, 6, 6, 9, 8, 2, 8, 3, 4, 4, 8, 8, 8, 8], [8, 8, 3, 3, 4, 4, 8, 8, 3, 8, 8, 3, 2, 6, 9, 6, 6, 9, 6, 2, 3, 8, 8, 3, 8, 8, 4, 4, 3, 3], [8, 3, 4, 4, 4, 4, 8, 8, 8, 3, 3, 2, 6, 6, 2, 9, 9, 2, 6, 6, 2, 3, 3, 8, 8, 8, 4, 4, 4, 4], [5, 9, 4, 4, 4, 3, 8, 8, 6, 9, 2, 6, 6, 2, 9, 8, 8, 9, 2, 6, 6, 2, 9, 6, 8, 8, 3, 4, 4, 4], [9, 5, 5, 4, 4, 3, 8, 3, 2, 6, 6, 6, 8, 6, 8, 9, 9, 8, 6, 8, 6, 6, 6, 2, 3, 8, 3, 4, 4, 5], [4, 5, 5, 9, 3, 8, 3, 3, 2, 6, 9, 2, 8, 6, 6, 2, 2, 6, 6, 8, 2, 9, 6, 2, 3, 3, 8, 3, 9, 5], [4, 4, 9, 5, 8, 8, 4, 4, 6, 6, 6, 9, 6, 8, 8, 6, 6, 8, 8, 6, 9, 6, 6, 6, 4, 4, 8, 8, 5, 9], [2, 3, 3, 8, 6, 2, 2, 6, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 9, 9, 6, 2, 2, 6, 8, 3], [3, 8, 8, 3, 9, 6, 6, 6, 9, 5, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 5, 9, 6, 6, 6, 9, 3, 8], [8, 2, 8, 3, 2, 6, 9, 6, 4, 8, 5, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 5, 8, 4, 6, 9, 6, 2, 3, 8], [2, 8, 3, 2, 6, 6, 2, 9, 8, 4, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 4, 8, 9, 2, 6, 6, 2, 3], [6, 9, 2, 6, 6, 8, 8, 6, 9, 9, 9, 6, 9, 9, 9, 8, 8, 9, 9, 9, 6, 9, 9, 9, 6, 8, 8, 6, 6, 2], [2, 6, 6, 6, 2, 6, 6, 8, 9, 9, 9, 9, 9, 5, 8, 8, 8, 8, 5, 9, 9, 9, 9, 9, 8, 6, 6, 2, 6, 6], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 4, 8, 5, 9, 9, 5, 8, 4, 9, 9, 9, 9, 8, 6, 8, 9, 2, 9], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 8, 4, 9, 9, 9, 9, 4, 8, 9, 9, 9, 6, 6, 2, 9, 8, 9, 6], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 8, 4, 9, 9, 9, 9, 4, 8, 9, 9, 9, 6, 6, 2, 9, 8, 9, 6], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 4, 8, 5, 9, 9, 5, 8, 4, 9, 9, 9, 9, 8, 6, 8, 9, 2, 9], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 9, 5, 8, 8, 8, 8, 5, 9, 9, 9, 9, 9, 8, 6, 6, 2, 6, 6], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 6, 9, 9, 9, 8, 8, 9, 9, 9, 6, 9, 9, 9, 6, 8, 8, 6, 6, 2], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 4, 8, 9, 2, 6, 6, 2, 3], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 5, 8, 4, 6, 9, 6, 2, 3, 8], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 5, 9, 6, 6, 6, 9, 3, 8], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 9, 9, 6, 2, 2, 6, 8, 3], [4, 4, 9, 5, 8, 8, 4, 4, 6, 6, 6, 9, 6, 8, 8, 6, 6, 8, 8, 6, 9, 6, 6, 6, 4, 4, 8, 8, 5, 9], [4, 5, 5, 9, 3, 8, 3, 3, 2, 6, 9, 2, 8, 6, 6, 2, 2, 6, 6, 8, 2, 9, 6, 2, 3, 3, 8, 3, 9, 5], [9, 5, 5, 4, 4, 3, 8, 3, 2, 6, 6, 6, 8, 6, 8, 9, 9, 8, 6, 8, 6, 6, 6, 2, 3, 8, 3, 4, 4, 5], [5, 9, 4, 4, 4, 3, 8, 8, 6, 9, 2, 6, 6, 2, 9, 8, 8, 9, 2, 6, 6, 2, 9, 6, 8, 8, 3, 4, 4, 4], [8, 3, 4, 4, 4, 4, 8, 8, 8, 3, 3, 2, 6, 6, 2, 9, 9, 2, 6, 6, 2, 3, 3, 8, 8, 8, 4, 4, 4, 4], [8, 8, 3, 3, 4, 4, 8, 8, 3, 8, 8, 3, 2, 6, 9, 6, 6, 9, 6, 2, 3, 8, 8, 3, 8, 8, 4, 4, 3, 3]], "output": [[2, 6, 9, 2, 9, 8, 6, 8, 9, 9], [6, 6, 6, 9, 8, 9, 2, 6, 6, 9], [6, 6, 6, 9, 8, 9, 2, 6, 6, 9], [2, 6, 9, 2, 9, 8, 6, 8, 9, 9], [2, 6, 6, 6, 2, 6, 6, 8, 9, 9], [6, 9, 2, 6, 6, 8, 8, 6, 9, 9], [2, 8, 3, 2, 6, 6, 2, 9, 8, 4], [8, 2, 8, 3, 2, 6, 9, 6, 4, 8], [3, 8, 8, 3, 9, 6, 6, 6, 9, 5], [2, 3, 3, 8, 6, 2, 2, 6, 9, 9]]}, {"input": [[3, 1, 8, 8, 6, 8, 8, 6, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 6, 8, 8, 6, 8, 8], [2, 3, 8, 3, 8, 6, 1, 8, 8, 9, 9, 8, 2, 9, 8, 8, 8, 8, 9, 2, 8, 9, 9, 8, 8, 1, 6, 8, 3, 8], [1, 8, 3, 1, 8, 1, 6, 8, 4, 8, 9, 8, 9, 8, 9, 8, 8, 9, 8, 9, 8, 9, 8, 4, 8, 6, 1, 8, 1, 3], [8, 2, 2, 3, 6, 8, 8, 6, 8, 4, 8, 8, 9, 8, 9, 9, 9, 9, 8, 9, 8, 8, 4, 8, 6, 8, 8, 6, 3, 2], [3, 8, 8, 3, 3, 1, 3, 8, 9, 2, 9, 9, 9, 8, 9, 8, 8, 9, 8, 9, 9, 9, 2, 9, 8, 3, 1, 3, 3, 8], [8, 3, 1, 8, 2, 3, 8, 8, 9, 9, 8, 8, 8, 4, 8, 9, 9, 8, 4, 8, 8, 8, 9, 9, 8, 8, 3, 2, 8, 1], [8, 1, 3, 8, 2, 8, 3, 1, 9, 8, 9, 9, 9, 5, 4, 8, 8, 4, 5, 9, 9, 9, 8, 9, 1, 3, 8, 2, 8, 3], [3, 8, 8, 3, 8, 1, 2, 3, 9, 8, 8, 9, 5, 9, 8, 9, 9, 8, 9, 5, 9, 8, 8, 9, 3, 2, 1, 8, 3, 8], [8, 8, 4, 8, 9, 9, 9, 9, 2, 9, 8, 8, 5, 2, 5, 1, 1, 5, 2, 5, 8, 8, 9, 2, 9, 9, 9, 9, 8, 4], [8, 9, 8, 4, 2, 9, 8, 8, 8, 2, 8, 3, 2, 5, 4, 5, 5, 4, 5, 2, 3, 8, 2, 8, 8, 8, 9, 2, 4, 8], [8, 9, 9, 8, 9, 8, 9, 8, 8, 8, 2, 9, 5, 4, 5, 2, 2, 5, 4, 5, 9, 2, 8, 8, 8, 9, 8, 9, 8, 9], [9, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 2, 1, 5, 2, 5, 5, 2, 5, 1, 2, 8, 8, 8, 9, 9, 8, 9, 8, 8], [9, 2, 9, 9, 9, 8, 9, 5, 8, 8, 6, 8, 2, 9, 3, 8, 8, 3, 9, 2, 8, 6, 8, 8, 5, 9, 8, 9, 9, 9], [9, 9, 8, 8, 8, 4, 5, 9, 8, 8, 4, 6, 8, 2, 8, 8, 8, 8, 2, 8, 6, 4, 8, 8, 9, 5, 4, 8, 8, 8], [9, 8, 9, 9, 9, 8, 4, 8, 6, 4, 8, 8, 8, 8, 2, 9, 9, 2, 8, 8, 8, 8, 4, 6, 8, 4, 8, 9, 9, 9], [9, 8, 8, 9, 8, 9, 8, 9, 8, 6, 8, 8, 8, 8, 8, 2, 2, 8, 8, 8, 8, 8, 6, 8, 9, 8, 9, 8, 9, 8], [9, 8, 8, 9, 8, 9, 8, 9, 8, 6, 8, 8, 8, 8, 8, 2, 2, 8, 8, 8, 8, 8, 6, 8, 9, 8, 9, 8, 9, 8], [9, 8, 9, 9, 9, 8, 4, 8, 6, 4, 8, 8, 8, 8, 2, 9, 9, 2, 8, 8, 8, 8, 4, 6, 8, 4, 8, 9, 9, 9], [9, 9, 8, 8, 8, 4, 5, 9, 8, 8, 4, 6, 8, 2, 8, 8, 8, 8, 2, 8, 6, 4, 8, 8, 9, 5, 4, 8, 8, 8], [9, 2, 9, 9, 9, 8, 9, 5, 8, 8, 6, 8, 2, 9, 3, 8, 8, 3, 9, 2, 8, 6, 8, 8, 5, 9, 8, 9, 9, 9], [9, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 2, 1, 5, 2, 5, 5, 2, 5, 1, 2, 8, 8, 8, 9, 9, 8, 9, 8, 8], [8, 9, 9, 8, 9, 8, 9, 8, 8, 8, 2, 9, 5, 4, 5, 2, 2, 5, 4, 5, 9, 2, 8, 8, 8, 9, 8, 9, 8, 9], [8, 9, 8, 4, 2, 9, 8, 8, 8, 2, 8, 3, 2, 5, 4, 5, 5, 4, 5, 2, 3, 8, 2, 8, 8, 8, 9, 2, 4, 8], [8, 8, 4, 8, 9, 9, 9, 9, 2, 9, 8, 8, 5, 2, 5, 1, 1, 5, 2, 5, 8, 8, 9, 2, 9, 9, 9, 9, 8, 4], [3, 8, 8, 3, 8, 7, 7, 7, 7, 8, 8, 9, 5, 9, 8, 9, 9, 8, 9, 5, 9, 8, 8, 9, 3, 2, 1, 8, 3, 8], [8, 1, 3, 8, 2, 7, 7, 7, 7, 8, 9, 9, 9, 5, 4, 8, 8, 4, 5, 9, 9, 9, 8, 9, 1, 3, 8, 2, 8, 3], [8, 3, 1, 8, 2, 7, 7, 7, 7, 9, 8, 8, 8, 4, 8, 9, 9, 8, 4, 8, 8, 8, 9, 9, 8, 8, 3, 2, 8, 1], [3, 8, 8, 3, 3, 7, 7, 7, 7, 2, 9, 9, 9, 8, 9, 8, 8, 9, 8, 9, 9, 9, 2, 9, 8, 3, 1, 3, 3, 8], [8, 2, 2, 3, 6, 8, 8, 6, 8, 4, 8, 8, 9, 8, 9, 9, 9, 9, 8, 9, 8, 8, 4, 8, 6, 8, 8, 6, 3, 2], [1, 8, 3, 1, 8, 1, 6, 8, 4, 8, 9, 8, 9, 8, 9, 8, 8, 9, 8, 9, 8, 9, 8, 4, 8, 6, 1, 8, 1, 3]], "output": [[1, 2, 3, 9], [8, 3, 1, 9], [3, 8, 8, 9], [1, 3, 8, 9]]}]}