{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 0, 5, 0, 0, 5, 0, 2, 0, 0], [0, 2, 0, 0, 0, 5, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 5, 0, 0, 5, 2, 2, 0, 0], [0, 2, 2, 0, 0, 5, 0, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 0, 5, 0, 0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 5, 0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 5, 0, 2, 2, 2, 2, 0, 0], [0, 2, 2, 0, 5, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 2, 0, 0, 5, 0, 0, 0, 0, 0, 2, 0], [0, 2, 0, 0, 0, 0, 5, 0, 0, 0, 2, 0], [0, 2, 0, 0, 5, 0, 0, 5, 0, 0, 2, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 2, 5, 0, 0, 0, 2, 2, 2, 0], [0, 2, 2, 2, 0, 0, 5, 0, 2, 2, 2, 0], [0, 2, 2, 2, 5, 0, 0, 5, 2, 2, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 2, 0, 5, 0, 0, 0, 0, 0, 2, 0], [0, 0, 2, 0, 0, 0, 0, 5, 0, 0, 2, 0], [0, 0, 2, 0, 0, 0, 5, 0, 0, 0, 2, 0], [0, 0, 2, 0, 0, 5, 0, 0, 0, 0, 2, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 5, 0, 0, 0, 2, 2, 2, 0], [0, 0, 2, 2, 0, 0, 0, 5, 2, 2, 2, 0], [0, 0, 2, 2, 0, 0, 5, 0, 2, 2, 2, 0], [0, 0, 2, 2, 0, 5, 0, 0, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}