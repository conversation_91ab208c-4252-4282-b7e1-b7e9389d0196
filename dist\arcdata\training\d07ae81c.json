{"train": [{"input": [[8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 2, 4, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 1, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 2, 2, 2, 2, 8, 8, 1, 8, 8], [1, 8, 8, 2, 2, 2, 2, 8, 1, 8, 8, 8], [2, 4, 2, 2, 2, 2, 2, 4, 2, 2, 2, 2], [2, 2, 4, 2, 2, 2, 4, 2, 2, 2, 2, 2], [2, 2, 2, 4, 2, 4, 2, 2, 2, 2, 2, 2], [8, 8, 8, 2, 4, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 4, 2, 4, 2, 8, 8, 8, 8, 8], [1, 8, 1, 2, 2, 2, 4, 8, 8, 8, 8, 8], [8, 1, 8, 2, 2, 2, 2, 1, 8, 8, 8, 8], [1, 8, 1, 2, 2, 2, 2, 8, 1, 8, 8, 8], [8, 8, 8, 4, 2, 2, 2, 8, 8, 1, 8, 8], [8, 8, 8, 2, 4, 2, 2, 8, 8, 8, 1, 8], [2, 2, 2, 2, 2, 4, 2, 2, 2, 2, 2, 4], [2, 2, 2, 2, 2, 2, 4, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 4, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 4, 2, 2, 2], [8, 8, 8, 2, 2, 2, 2, 8, 8, 1, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 1, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 1]]}, {"input": [[3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 2, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 8, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3]], "output": [[3, 3, 3, 2, 1, 1, 1, 1, 1, 8, 3, 3, 3, 3], [3, 3, 3, 1, 2, 1, 1, 1, 2, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 2, 1, 2, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 1, 2, 1, 1, 3, 3, 3, 3, 3], [3, 3, 3, 1, 1, 2, 1, 2, 1, 3, 3, 3, 3, 3], [1, 1, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 1, 1], [1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 2], [1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 1], [3, 8, 3, 1, 1, 1, 1, 1, 1, 3, 3, 8, 3, 3], [8, 3, 3, 1, 1, 1, 1, 1, 1, 3, 8, 3, 8, 3], [3, 3, 3, 1, 1, 1, 1, 1, 1, 8, 3, 3, 3, 8], [3, 3, 3, 1, 1, 1, 1, 1, 2, 3, 3, 3, 3, 3]]}, {"input": [[1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 8, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 3, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 3, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 6, 6]], "output": [[1, 1, 3, 6, 6, 6, 1, 1, 1, 1, 6, 6, 3, 6, 6], [1, 1, 6, 3, 6, 6, 1, 1, 1, 1, 6, 3, 6, 6, 6], [1, 1, 6, 6, 3, 6, 1, 1, 1, 1, 3, 6, 6, 6, 6], [1, 1, 6, 6, 6, 3, 1, 1, 1, 8, 6, 6, 6, 6, 6], [8, 1, 6, 6, 6, 6, 8, 1, 8, 1, 6, 6, 6, 6, 6], [1, 8, 6, 6, 6, 6, 1, 8, 1, 1, 6, 6, 6, 6, 6], [1, 1, 3, 6, 6, 6, 8, 1, 8, 1, 6, 6, 6, 6, 6], [1, 1, 6, 3, 6, 3, 1, 1, 1, 8, 6, 6, 6, 6, 6], [1, 1, 6, 6, 3, 6, 1, 1, 1, 1, 3, 6, 6, 6, 3], [1, 1, 6, 3, 6, 3, 1, 1, 1, 1, 6, 3, 6, 3, 6], [1, 1, 3, 6, 6, 6, 8, 1, 1, 1, 6, 6, 3, 6, 6], [1, 8, 6, 6, 6, 6, 1, 8, 1, 1, 6, 3, 6, 3, 6], [8, 1, 6, 6, 6, 6, 1, 1, 8, 1, 3, 6, 6, 6, 3], [1, 1, 6, 6, 6, 6, 1, 1, 1, 8, 6, 6, 6, 6, 6], [1, 1, 6, 6, 6, 6, 1, 1, 8, 1, 3, 6, 6, 6, 6]]}], "test": [{"input": [[8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 4, 3, 3, 3, 3, 3, 3, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 3, 3, 3, 3, 3, 4, 8, 4, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 1, 3, 3, 3, 1, 8, 8, 8, 4, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 1, 3, 1, 3, 8, 8, 8, 8, 4, 8, 8, 8, 8, 8], [3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 1, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3], [3, 3, 3, 3, 1, 3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3], [8, 8, 8, 1, 3, 3, 3, 3, 3, 4, 8, 8, 8, 8, 8, 8, 8, 4, 8], [8, 8, 4, 3, 3, 3, 3, 3, 3, 8, 4, 8, 8, 8, 8, 8, 8, 8, 4], [8, 4, 8, 3, 3, 3, 3, 3, 3, 8, 8, 4, 8, 8, 8, 8, 8, 4, 8], [4, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 4, 8, 8, 8, 4, 8, 8], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 3, 1, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 3, 1, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 1, 3, 3], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 4, 8, 8, 8, 8, 8, 4, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 4, 8, 8, 8, 8, 8, 8, 8, 4]]}]}