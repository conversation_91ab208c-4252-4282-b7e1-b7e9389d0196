{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0], [2, 0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0], [2, 0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0], [1, 0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 0], [1, 0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 0], [2, 0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0], [2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0], [2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0], [1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0], [1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0], [2, 0, 0, 0, 2, 2, 2, 0, 2, 2, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0], [3, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5], [4, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5], [4, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5], [3, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5], [4, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], [3, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], [3, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], [3, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], [4, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0], [4, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3], [4, 4, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4], [3, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3], [4, 0, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4], [3, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3], [3, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3], [3, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3], [4, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0], [4, 0, 0, 0, 4, 4, 4, 0, 0, 0, 0, 0]]}], "test": [{"input": [[1, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [8, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [1, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [1, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5], [7, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5], [7, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5], [7, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [7, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0], [8, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0], [8, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0], [8, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0], [8, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0]], "output": [[1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0], [8, 0, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0], [1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0], [1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1], [7, 0, 7, 7, 7, 7, 7, 7, 0, 7, 7, 7], [7, 0, 7, 7, 7, 7, 7, 7, 0, 7, 7, 7], [7, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7, 7], [7, 0, 0, 0, 0, 0, 7, 7, 7, 0, 0, 0], [8, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0], [8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 0, 0], [8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 0, 0], [8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 0, 0]]}]}