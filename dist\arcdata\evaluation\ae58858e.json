{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 2, 2, 0], [0, 2, 2, 0, 0, 2, 2, 0], [0, 0, 0, 0, 0, 0, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 2, 2, 0, 0, 0, 0], [2, 0, 2, 2, 0, 0, 2, 2], [2, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 0, 0, 0, 6, 6, 0], [0, 6, 6, 0, 0, 6, 6, 0], [0, 0, 0, 0, 0, 0, 6, 6], [0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 6, 6, 0, 0, 0, 0], [2, 0, 6, 6, 0, 0, 2, 2], [2, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 2, 2, 0, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 2, 2, 0, 0, 0, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2], [0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 2, 0], [0, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 6, 0, 0, 0, 6, 0, 0, 0, 0, 0], [0, 6, 6, 0, 0, 0, 6, 6, 0, 0, 0, 0], [0, 6, 6, 6, 0, 0, 6, 6, 0, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 2, 2, 0, 0, 0, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 6], [0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 6, 0], [0, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0]]}, {"input": [[2, 2, 0, 0, 0, 2], [2, 2, 0, 0, 0, 2], [0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0], [0, 0, 0, 2, 0, 2], [0, 2, 2, 2, 0, 0]], "output": [[6, 6, 0, 0, 0, 2], [6, 6, 0, 0, 0, 2], [0, 0, 0, 2, 0, 0], [0, 2, 0, 0, 0, 0], [0, 0, 0, 6, 0, 2], [0, 6, 6, 6, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 2, 2, 0], [0, 0, 0, 0, 0, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 6, 6, 0], [0, 0, 0, 0, 0, 6, 6, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 2, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 2, 2, 2, 0], [2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 2, 2, 0], [0, 2, 0, 0, 0, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 2, 2, 0, 0, 0, 2]], "output": [[0, 0, 0, 0, 2, 2, 2, 0], [6, 6, 0, 0, 0, 0, 0, 0], [0, 6, 6, 0, 0, 6, 6, 0], [0, 6, 0, 0, 0, 6, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 2, 2, 0, 0, 0, 2]]}]}