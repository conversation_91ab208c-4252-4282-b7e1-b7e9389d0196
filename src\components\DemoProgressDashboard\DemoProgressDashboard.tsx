import React, { useEffect, useState } from 'react';
import {
  Paper,
  Typography,
  Box,
  LinearProgress,
  Stack,
  Chip,
  But<PERSON>,
  Card,
  CardContent,
  Alert,
} from '@mui/material';
import {
  Stop as StopIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { stopDemo } from '../../store/slices/demoSlice';
import { demoRunner } from '../../services/demoRunner';
import { DemoSession } from '../../types';
import DemoResultsTable from '../DemoResultsTable/DemoResultsTable';

const DemoProgressDashboard: React.FC = () => {
  const dispatch = useDispatch();
  const { mode } = useSelector((state: RootState) => state.demo);
  const [currentSession, setCurrentSession] = useState<DemoSession | null>(null);
  const [currentPuzzle, setCurrentPuzzle] = useState(0);
  const [totalPuzzles, setTotalPuzzles] = useState(0);

  useEffect(() => {
    // Configurer le callback de progression
    demoRunner.setProgressCallback((current, total, session) => {
      setCurrentPuzzle(current);
      setTotalPuzzles(total);
      setCurrentSession(session);
    });

    // Récupérer la session actuelle si elle existe
    const session = demoRunner.getCurrentSession();
    if (session) {
      setCurrentSession(session);
    }
  }, []);

  const handleStopDemo = () => {
    demoRunner.stopDemo();
    dispatch(stopDemo());
  };

  const handlePauseDemo = () => {
    if (demoRunner.isPausedDemo()) {
      demoRunner.resumeDemo();
    } else {
      demoRunner.pauseDemo();
    }
  };

  const isRunningDemo = demoRunner.isRunningDemo();
  const isPausedDemo = demoRunner.isPausedDemo();

  if (!mode.isActive && !isRunningDemo) {
    return null;
  }

  const progress = totalPuzzles > 0 ? (currentPuzzle / totalPuzzles) * 100 : 0;
  const successRate = currentSession && currentSession.puzzlesProcessed > 0
    ? (currentSession.successCount / currentSession.puzzlesProcessed) * 100
    : 0;

  const formatDuration = (startTime: Date) => {
    const now = new Date();
    const diff = now.getTime() - startTime.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Stack spacing={2}>
        {/* Header */}
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Demo en cours</Typography>
          <Stack direction="row" spacing={1}>
            <Button
              size="small"
              variant="outlined"
              startIcon={isPausedDemo ? <PlayIcon /> : <PauseIcon />}
              disabled={!isRunningDemo}
              onClick={handlePauseDemo}
            >
              {isPausedDemo ? 'Reprendre' : 'Pause'}
            </Button>
            <Button
              size="small"
              variant="contained"
              color="error"
              startIcon={<StopIcon />}
              onClick={handleStopDemo}
              disabled={!isRunningDemo}
            >
              Arrêter
            </Button>
          </Stack>
        </Stack>

        {/* Progress */}
        <Box>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 1 }}>
            <Typography variant="body2">
              Puzzle {currentPuzzle} / {totalPuzzles}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {progress.toFixed(1)}%
            </Typography>
          </Stack>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Stats */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Card variant="outlined" sx={{ textAlign: 'center' }}>
              <CardContent sx={{ py: 1 }}>
                <Typography variant="h6" color="success.main">
                  {currentSession?.successCount || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Succès
                </Typography>
              </CardContent>
            </Card>
          </Box>
          <Box sx={{ flex: 1 }}>
            <Card variant="outlined" sx={{ textAlign: 'center' }}>
              <CardContent sx={{ py: 1 }}>
                <Typography variant="h6" color="error.main">
                  {currentSession?.failureCount || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Échecs
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Box>

        {/* Additional Info */}
        <Stack direction="row" spacing={1} flexWrap="wrap">
          <Chip 
            label={`Taux: ${successRate.toFixed(1)}%`}
            color={successRate >= 70 ? 'success' : successRate >= 40 ? 'warning' : 'error'}
            size="small"
          />
          {currentSession && (
            <Chip
              label={`Temps: ${formatDuration(currentSession.startTime)}`}
              variant="outlined"
              size="small"
            />
          )}
          {currentSession && (
            <Chip
              label={`Coût: $${currentSession.totalCost.toFixed(3)}`}
              variant="outlined"
              size="small"
            />
          )}
          <Chip
            label={`${mode.selectedProvider}/${mode.selectedModel}`}
            variant="outlined"
            size="small"
          />
        </Stack>

        {/* Current Status */}
        <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Status: {isRunningDemo ? (isPausedDemo ? 'En pause' : 'En cours...') : 'Arrêté'}
          </Typography>
          {currentSession && currentSession.averageResponseTime > 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
              Temps moyen: {currentSession.averageResponseTime.toFixed(0)}ms
            </Typography>
          )}
        </Box>

        {/* Afficher les erreurs s'il y en a */}
        {!isRunningDemo && currentSession && currentSession.results.length > 0 && (
          <Alert severity="info">
            Demo terminée ! {currentSession.successCount} succès sur {currentSession.puzzlesProcessed} puzzles traités.
          </Alert>
        )}

        {/* Tableau des résultats */}
        {currentSession && currentSession.results.length > 0 && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Résultats détaillés ({currentSession.results.length} puzzles)
            </Typography>
            <DemoResultsTable results={currentSession.results} />
          </Box>
        )}
      </Stack>
    </Paper>
  );
};

export default DemoProgressDashboard;