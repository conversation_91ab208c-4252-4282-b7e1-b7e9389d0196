{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 2, 2, 0], [2, 2, 2, 0, 0, 0, 0, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 2, 2, 0], [0, 0, 0, 2, 2, 0, 0, 2, 2, 0], [0, 0, 0, 2, 2, 0, 0, 2, 2, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 9, 9, 9, 9, 2, 2, 0], [2, 2, 2, 9, 9, 9, 9, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 2, 2, 0], [0, 0, 0, 2, 2, 9, 9, 2, 2, 0], [0, 0, 0, 2, 2, 9, 9, 2, 2, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0]]}, {"input": [[2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 2, 2, 2], [2, 2, 0, 0, 0, 0, 0, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 2, 2, 2], [0, 0, 0, 2, 2, 0, 0, 2, 2, 2], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2]], "output": [[2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 9, 9, 9, 9, 9, 2, 2, 2], [2, 2, 9, 9, 9, 9, 9, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 2, 2, 2], [0, 0, 0, 2, 2, 9, 9, 2, 2, 2], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 9, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 2], [2, 2, 2, 2, 0, 2, 2, 2, 0, 2], [2, 2, 2, 2, 0, 2, 2, 2, 0, 2], [2, 2, 2, 2, 0, 0, 0, 0, 0, 2]], "output": [[0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [2, 2, 2, 2, 9, 9, 2, 2, 2, 2], [2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 9, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 9, 2], [2, 2, 2, 2, 9, 2, 2, 2, 9, 2], [2, 2, 2, 2, 9, 2, 2, 2, 9, 2], [2, 2, 2, 2, 0, 0, 0, 0, 0, 2]]}], "test": [{"input": [[2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 2, 2, 2, 0, 0, 2, 2, 2, 2], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 2, 2, 0, 2, 2, 2, 2, 0], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 9, 9, 9, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2], [0, 2, 2, 2, 9, 9, 2, 2, 2, 2], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 9, 2, 2, 2, 2, 0], [0, 2, 2, 2, 9, 2, 2, 2, 2, 0], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}