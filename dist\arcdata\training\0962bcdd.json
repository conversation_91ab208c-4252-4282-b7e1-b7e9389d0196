{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 2, 7, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 7, 2, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 7, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 2, 7, 2, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 2, 7, 7, 0, 0, 0, 0, 0, 0, 0], [0, 2, 7, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 7, 0, 2, 0, 2, 0, 7, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 2, 7, 2, 0, 0], [0, 0, 0, 0, 0, 0, 7, 7, 2, 7, 7, 0], [0, 0, 0, 0, 0, 0, 0, 2, 7, 2, 0, 0], [0, 0, 0, 0, 0, 0, 2, 0, 7, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 6, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 6, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 0, 8, 0, 6, 0, 0, 0, 0, 0, 0], [0, 0, 6, 8, 6, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 6, 8, 8, 0, 0, 0, 0, 0, 0], [0, 0, 6, 8, 6, 0, 0, 0, 0, 0, 0, 0], [0, 6, 0, 8, 0, 6, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 6, 0, 8, 0, 6, 0], [0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 0, 0], [0, 0, 0, 0, 0, 0, 8, 8, 6, 8, 8, 0], [0, 0, 0, 0, 0, 0, 0, 6, 8, 6, 0, 0], [0, 0, 0, 0, 0, 0, 6, 0, 8, 0, 6, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 4, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 4, 0, 3, 0, 4, 0], [0, 0, 0, 0, 0, 0, 0, 4, 3, 4, 0, 0], [0, 0, 0, 0, 0, 0, 3, 3, 4, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 4, 3, 4, 0, 0], [0, 0, 0, 0, 0, 0, 4, 0, 3, 0, 4, 0], [4, 0, 3, 0, 4, 0, 0, 0, 0, 0, 0, 0], [0, 4, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 4, 3, 3, 0, 0, 0, 0, 0, 0, 0], [0, 4, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 3, 0, 4, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}