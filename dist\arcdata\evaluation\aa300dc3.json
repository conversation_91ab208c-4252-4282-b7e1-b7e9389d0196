{"train": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 0, 0, 0, 5, 5, 0, 0, 5, 5], [5, 5, 0, 0, 0, 5, 0, 0, 0, 5], [5, 5, 5, 0, 0, 0, 0, 0, 5, 5], [5, 5, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 5, 0, 0, 0, 5, 0, 0, 0, 5], [5, 0, 0, 5, 0, 5, 0, 0, 0, 5], [5, 5, 0, 5, 5, 5, 0, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 8, 0, 0, 5, 5, 0, 0, 5, 5], [5, 5, 8, 0, 0, 5, 0, 0, 0, 5], [5, 5, 5, 8, 0, 0, 0, 0, 5, 5], [5, 5, 0, 0, 8, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 8, 0, 0, 5, 5], [5, 5, 0, 0, 0, 5, 8, 0, 0, 5], [5, 0, 0, 5, 0, 5, 0, 8, 0, 5], [5, 5, 0, 5, 5, 5, 0, 5, 8, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 0, 5], [5, 5, 0, 0, 5, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 0, 0, 0, 0, 0, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 5, 5, 5], [5, 5, 0, 5, 0, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 8, 5], [5, 5, 0, 0, 5, 0, 0, 8, 0, 5], [5, 0, 0, 0, 0, 0, 8, 0, 0, 5], [5, 5, 0, 0, 0, 8, 0, 0, 0, 5], [5, 5, 5, 0, 8, 0, 0, 0, 5, 5], [5, 0, 0, 8, 0, 0, 0, 0, 0, 5], [5, 0, 8, 0, 0, 0, 0, 5, 5, 5], [5, 5, 0, 5, 0, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 5, 0, 0, 0, 0, 5, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 5, 5, 0, 0, 0, 0, 5], [5, 5, 5, 5, 5, 0, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 8, 0, 0, 0, 0, 5, 5, 5, 5], [5, 5, 8, 0, 0, 0, 5, 0, 0, 5], [5, 0, 0, 8, 0, 0, 0, 0, 0, 5], [5, 5, 0, 0, 8, 0, 0, 0, 0, 5], [5, 5, 0, 0, 0, 8, 0, 0, 5, 5], [5, 0, 0, 0, 0, 0, 8, 0, 0, 5], [5, 0, 0, 5, 5, 0, 0, 8, 0, 5], [5, 5, 5, 5, 5, 0, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 0, 0, 0, 5, 5, 5, 5], [5, 5, 5, 0, 0, 0, 0, 5, 5, 5], [5, 5, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 0, 5, 5, 0, 0, 0, 5], [5, 5, 0, 0, 5, 5, 0, 0, 5, 5], [5, 5, 5, 0, 5, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 8, 0, 0, 5, 5, 5, 5], [5, 5, 5, 0, 8, 0, 0, 5, 5, 5], [5, 5, 0, 0, 0, 8, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 8, 0, 5, 5], [5, 0, 0, 0, 0, 0, 0, 8, 0, 5], [5, 5, 5, 0, 5, 5, 0, 0, 8, 5], [5, 5, 0, 0, 5, 5, 0, 0, 5, 5], [5, 5, 5, 0, 5, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}], "test": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 5, 5], [5, 5, 0, 0, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 0, 0, 0, 0, 5, 5, 5], [5, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 0, 5, 5, 0, 0, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 0, 0, 0, 0, 8, 5, 5], [5, 5, 0, 0, 0, 0, 8, 0, 5, 5], [5, 5, 5, 0, 0, 8, 0, 0, 0, 5], [5, 0, 0, 0, 8, 0, 0, 5, 5, 5], [5, 0, 0, 8, 0, 0, 5, 5, 5, 5], [5, 0, 8, 0, 0, 0, 0, 0, 0, 5], [5, 8, 0, 5, 5, 0, 0, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}]}