{"train": [{"input": [[8, 1, 8, 0, 8, 1, 8, 0, 1, 0, 1, 1], [1, 0, 1, 1, 8, 6, 0, 1, 1, 6, 6, 8], [0, 1, 8, 8, 0, 8, 0, 1, 0, 6, 1, 0], [0, 8, 0, 8, 0, 0, 0, 0, 6, 8, 8, 6], [1, 8, 0, 8, 0, 0, 0, 6, 8, 8, 0, 0], [4, 6, 6, 8, 6, 0, 8, 0, 1, 1, 0, 8], [4, 4, 6, 8, 0, 1, 8, 1, 1, 1, 8, 6], [6, 4, 4, 0, 8, 0, 6, 0, 1, 0, 1, 0], [8, 8, 1, 1, 8, 8, 8, 0, 0, 0, 8, 0], [0, 6, 8, 8, 0, 0, 0, 1, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 6], [1, 0, 1, 8, 8, 0, 6, 0, 8, 8, 1, 8], [1, 0, 0, 8, 1, 6, 6, 0, 1, 0, 8, 8], [8, 1, 8, 8, 1, 1, 0, 1, 8, 0, 8, 8], [0, 1, 1, 0, 0, 6, 1, 8, 0, 0, 8, 1], [1, 8, 8, 8, 0, 8, 8, 6, 1, 8, 6, 0], [8, 0, 6, 1, 8, 1, 6, 6, 8, 0, 1, 1], [8, 0, 8, 0, 8, 0, 8, 0, 8, 0, 8, 1], [8, 1, 0, 0, 1, 1, 0, 8, 8, 0, 0, 8]], "output": [[8, 1, 8, 0, 8, 1, 8, 0, 1, 0, 1, 1], [1, 0, 1, 1, 8, 6, 0, 1, 1, 6, 6, 4], [0, 1, 8, 8, 0, 8, 0, 1, 0, 6, 4, 4], [0, 8, 0, 8, 0, 0, 0, 0, 6, 4, 4, 6], [1, 8, 0, 8, 0, 0, 0, 6, 8, 8, 0, 0], [4, 6, 6, 8, 6, 0, 8, 0, 1, 1, 0, 8], [4, 4, 6, 8, 0, 1, 8, 1, 1, 1, 8, 6], [6, 4, 4, 0, 8, 0, 6, 0, 1, 0, 1, 0], [8, 8, 1, 1, 8, 8, 8, 0, 0, 0, 8, 0], [0, 6, 8, 8, 0, 0, 0, 1, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 6], [1, 0, 1, 8, 8, 0, 6, 0, 8, 8, 1, 8], [1, 0, 0, 8, 1, 6, 6, 0, 1, 0, 8, 8], [8, 1, 8, 8, 1, 1, 0, 1, 8, 0, 8, 8], [0, 1, 1, 0, 0, 6, 4, 4, 0, 0, 8, 1], [1, 8, 8, 8, 0, 4, 4, 6, 1, 8, 6, 0], [8, 0, 6, 1, 8, 4, 6, 6, 8, 0, 1, 1], [8, 0, 8, 0, 8, 0, 8, 0, 8, 0, 8, 1], [8, 1, 0, 0, 1, 1, 0, 8, 8, 0, 0, 8]]}, {"input": [[1, 0, 6, 0, 0, 0, 0, 8, 0, 1, 8, 8, 8, 1, 1, 1], [0, 1, 8, 6, 1, 6, 0, 0, 0, 0, 6, 1, 1, 8, 8, 1], [0, 1, 8, 1, 0, 1, 0, 0, 6, 6, 1, 0, 0, 8, 1, 1], [0, 0, 1, 1, 1, 6, 1, 1, 0, 6, 6, 0, 1, 1, 8, 0], [0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 6, 6, 1], [0, 0, 0, 0, 0, 6, 0, 1, 8, 8, 0, 8, 1, 1, 0, 1], [1, 1, 1, 8, 1, 0, 0, 8, 1, 1, 0, 8, 8, 1, 1, 1], [8, 8, 1, 1, 0, 6, 1, 8, 1, 8, 6, 8, 8, 1, 8, 6], [1, 1, 6, 1, 8, 1, 0, 1, 1, 1, 6, 0, 6, 8, 1, 8], [0, 0, 8, 8, 6, 4, 6, 0, 1, 8, 0, 1, 0, 0, 1, 8], [0, 1, 8, 0, 4, 6, 6, 1, 0, 8, 1, 1, 1, 1, 1, 0], [1, 1, 1, 6, 4, 6, 4, 8, 0, 0, 0, 1, 0, 8, 6, 8], [6, 0, 1, 1, 1, 1, 1, 0, 8, 1, 0, 1, 1, 8, 0, 0], [0, 1, 1, 1, 1, 8, 1, 1, 1, 8, 8, 1, 8, 8, 8, 0], [0, 1, 0, 0, 6, 0, 0, 0, 8, 1, 6, 8, 8, 6, 0, 0], [1, 1, 1, 1, 8, 8, 8, 1, 1, 1, 0, 8, 1, 1, 8, 1], [8, 1, 1, 0, 0, 6, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1], [6, 0, 1, 0, 1, 8, 8, 8, 8, 6, 1, 1, 8, 1, 8, 8]], "output": [[1, 0, 6, 0, 0, 0, 0, 8, 0, 1, 8, 8, 8, 1, 1, 1], [0, 1, 8, 6, 1, 6, 0, 0, 4, 4, 6, 1, 1, 8, 8, 1], [0, 1, 8, 1, 0, 1, 0, 0, 6, 6, 4, 0, 0, 8, 1, 1], [0, 0, 1, 1, 1, 6, 1, 1, 4, 6, 6, 0, 1, 1, 8, 0], [0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 6, 6, 1], [0, 0, 0, 0, 0, 6, 0, 1, 8, 8, 0, 8, 1, 1, 0, 1], [1, 1, 1, 8, 1, 0, 0, 8, 1, 1, 0, 8, 8, 1, 1, 1], [8, 8, 1, 1, 0, 6, 1, 8, 1, 8, 6, 8, 8, 1, 8, 6], [1, 1, 6, 1, 8, 1, 0, 1, 1, 1, 6, 0, 6, 8, 1, 8], [0, 0, 8, 8, 6, 4, 6, 0, 1, 8, 0, 1, 0, 0, 1, 8], [0, 1, 8, 0, 4, 6, 6, 1, 0, 8, 1, 1, 1, 1, 1, 0], [1, 1, 1, 6, 4, 6, 4, 8, 0, 0, 0, 1, 0, 8, 6, 8], [6, 0, 1, 1, 1, 1, 1, 0, 8, 1, 0, 1, 1, 8, 0, 0], [0, 1, 1, 1, 1, 8, 1, 1, 1, 8, 8, 1, 8, 8, 8, 0], [0, 1, 0, 0, 6, 0, 0, 0, 8, 1, 6, 8, 8, 6, 0, 0], [1, 1, 1, 1, 8, 8, 8, 1, 1, 1, 0, 8, 1, 1, 8, 1], [8, 1, 1, 0, 0, 6, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1], [6, 0, 1, 0, 1, 8, 8, 8, 8, 6, 1, 1, 8, 1, 8, 8]]}, {"input": [[8, 1, 8, 8, 6, 0, 0, 1, 0, 0, 1, 0, 8, 0, 1, 0, 0], [6, 8, 1, 0, 0, 8, 0, 4, 6, 6, 1, 0, 0, 0, 8, 0, 1], [0, 8, 1, 8, 0, 1, 0, 6, 4, 4, 0, 0, 8, 0, 0, 0, 8], [1, 1, 0, 1, 1, 0, 8, 4, 4, 4, 1, 8, 8, 1, 0, 1, 8], [1, 6, 6, 0, 0, 8, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 8, 6, 0, 0, 8, 0, 0, 1, 1, 0, 6, 0, 0, 0, 1, 8], [0, 8, 0, 8, 0, 0, 8, 8, 8, 1, 8, 0, 8, 0, 0, 0, 6], [0, 1, 0, 1, 6, 0, 0, 1, 1, 0, 0, 8, 1, 1, 6, 8, 6], [0, 0, 1, 0, 1, 8, 0, 8, 8, 0, 1, 1, 8, 1, 1, 8, 0], [0, 8, 0, 8, 1, 0, 6, 8, 8, 0, 0, 0, 0, 6, 8, 1, 1], [0, 0, 0, 0, 6, 0, 1, 1, 8, 1, 1, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 6, 8, 1, 8, 1, 0, 0, 0, 1, 8, 1, 1, 6], [8, 8, 0, 0, 1, 0, 1, 8, 0, 1, 8, 0, 1, 0, 0, 0, 1], [0, 8, 8, 1, 8, 6, 8, 1, 6, 1, 0, 6, 0, 0, 8, 0, 6], [1, 0, 8, 8, 1, 0, 8, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0], [0, 0, 0, 8, 0, 0, 1, 0, 8, 8, 0, 0, 0, 8, 0, 6, 6], [1, 0, 0, 0, 0, 0, 1, 0, 8, 0, 1, 1, 6, 0, 6, 0, 1]], "output": [[8, 1, 8, 8, 6, 0, 0, 1, 0, 0, 1, 0, 8, 0, 1, 0, 0], [6, 8, 1, 0, 0, 8, 0, 4, 6, 6, 1, 0, 0, 0, 8, 0, 1], [0, 8, 1, 8, 0, 1, 0, 6, 4, 4, 0, 0, 8, 0, 0, 0, 8], [1, 1, 0, 1, 1, 0, 8, 4, 4, 4, 1, 8, 8, 1, 0, 1, 8], [1, 6, 6, 0, 0, 8, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 8], [0, 8, 6, 0, 0, 8, 0, 0, 1, 1, 0, 6, 0, 0, 0, 1, 8], [0, 8, 0, 8, 0, 0, 8, 8, 8, 1, 8, 0, 8, 0, 0, 0, 6], [0, 1, 0, 1, 6, 0, 0, 1, 1, 0, 0, 8, 1, 1, 6, 8, 6], [0, 0, 1, 0, 1, 8, 0, 8, 8, 0, 1, 1, 8, 1, 1, 8, 0], [0, 8, 0, 8, 1, 0, 6, 8, 8, 0, 0, 0, 0, 6, 8, 1, 1], [0, 0, 0, 0, 6, 0, 1, 1, 8, 1, 1, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 6, 8, 1, 8, 1, 0, 0, 0, 1, 8, 1, 1, 6], [8, 8, 0, 0, 1, 0, 1, 8, 0, 1, 8, 0, 1, 0, 0, 0, 1], [0, 8, 8, 1, 8, 6, 8, 1, 6, 1, 0, 6, 0, 0, 8, 0, 6], [1, 0, 8, 8, 1, 0, 8, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0], [0, 0, 0, 8, 0, 0, 1, 0, 8, 8, 0, 0, 0, 8, 4, 6, 6], [1, 0, 0, 0, 0, 0, 1, 0, 8, 0, 1, 1, 6, 0, 6, 4, 4]]}], "test": [{"input": [[0, 6, 8, 0, 0, 6, 1, 6, 6, 1, 1, 1, 0, 0, 1], [1, 0, 8, 1, 6, 8, 8, 1, 1, 0, 1, 0, 8, 0, 1], [0, 0, 6, 0, 1, 8, 0, 1, 1, 0, 0, 0, 1, 0, 1], [1, 1, 1, 8, 6, 6, 6, 8, 0, 0, 1, 8, 0, 8, 6], [1, 0, 8, 0, 8, 6, 0, 6, 8, 1, 1, 1, 1, 1, 8], [0, 0, 6, 0, 1, 0, 0, 8, 8, 1, 1, 8, 1, 6, 0], [0, 1, 8, 1, 0, 6, 8, 8, 8, 6, 0, 1, 6, 6, 0], [1, 0, 0, 0, 0, 0, 1, 8, 0, 0, 0, 8, 1, 0, 8], [0, 1, 0, 8, 1, 1, 1, 8, 0, 0, 8, 1, 1, 8, 6], [0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 8, 1], [8, 0, 8, 8, 8, 4, 4, 4, 6, 1, 1, 8, 6, 8, 0], [1, 0, 8, 1, 1, 6, 4, 4, 8, 1, 8, 1, 0, 1, 1], [0, 6, 1, 0, 0, 6, 6, 4, 1, 1, 0, 0, 8, 8, 8], [8, 1, 1, 0, 0, 8, 8, 0, 8, 8, 0, 0, 1, 1, 1], [1, 1, 8, 8, 0, 1, 8, 8, 8, 8, 0, 0, 1, 6, 8], [0, 8, 1, 8, 0, 1, 8, 0, 6, 1, 6, 0, 6, 6, 0]], "output": [[0, 6, 8, 0, 0, 6, 1, 6, 6, 1, 1, 1, 0, 0, 1], [1, 0, 8, 1, 6, 8, 8, 1, 1, 0, 1, 0, 8, 0, 1], [0, 0, 6, 0, 1, 8, 0, 1, 1, 0, 0, 0, 1, 0, 1], [1, 1, 1, 4, 6, 6, 6, 8, 0, 0, 1, 8, 0, 8, 6], [1, 0, 8, 4, 4, 6, 0, 6, 8, 1, 1, 4, 4, 4, 8], [0, 0, 6, 4, 4, 4, 0, 8, 8, 1, 1, 4, 4, 6, 0], [0, 1, 8, 1, 0, 6, 8, 8, 8, 6, 0, 4, 6, 6, 0], [1, 0, 0, 0, 0, 0, 1, 8, 0, 0, 0, 8, 1, 0, 8], [0, 1, 0, 8, 1, 1, 1, 8, 0, 0, 8, 1, 1, 8, 6], [0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 8, 1], [8, 0, 8, 8, 8, 4, 4, 4, 6, 1, 1, 8, 6, 8, 0], [1, 0, 8, 1, 1, 6, 4, 4, 8, 1, 8, 1, 0, 1, 1], [0, 6, 1, 0, 0, 6, 6, 4, 1, 1, 0, 0, 8, 8, 8], [8, 1, 1, 0, 0, 8, 8, 0, 8, 8, 0, 4, 4, 4, 1], [1, 1, 8, 8, 0, 1, 8, 8, 8, 8, 0, 4, 4, 6, 8], [0, 8, 1, 8, 0, 1, 8, 0, 6, 1, 6, 4, 6, 6, 0]]}]}