{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 2, 2, 2, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 4, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 3, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 4, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 2, 2, 2, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 2, 2, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 2, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 2, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 3, 4, 2, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 2, 4, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 2, 4, 3, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 2, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 2, 2, 2, 2, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 2, 2, 2, 2, 2, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 2, 2, 2, 2, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 2, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 3, 4, 2, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 2, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 2, 2, 2, 2, 2, 4, 2, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}