import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Stack,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Tooltip,

} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  DataObject as DataObjectIcon,
  Analytics as AnalyticsIcon,
  Compare as CompareIcon,
  Psychology as PsychologyIcon,
  GridOn as GridOnIcon,
  Palette as PaletteIcon,
  AutoAwesome as SymmetryIcon,
  Pattern as PatternIcon,
  Transform as TransformIcon,
  ViewModule as BlockIcon,
  Dashboard as MosaicIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { useAnalyzePuzzleMutation } from '../../store';
import { setAnalysis } from '../../store/slices/puzzleSlice';
import { dataStorage } from '../../services/dataStorage';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analysis-tabpanel-${index}`}
      aria-labelledby={`analysis-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2, pb: 4 }}>{children}</Box>}
    </div>
  );
};

const AnalysisPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { currentPuzzle, analysis, showValues } = useSelector((state: RootState) => state.puzzle);
  const [analyzePuzzle, { isLoading }] = useAnalyzePuzzleMutation();
  const [currentTab, setCurrentTab] = useState(0);
  const [expandedSections, setExpandedSections] = useState<string[]>(['geometric_detection']);
  const [lastAnalysisTime, setLastAnalysisTime] = useState<string | null>(null);


  const handleAnalyze = async () => {
    if (!currentPuzzle) return;

    try {
      const result = await analyzePuzzle(currentPuzzle).unwrap();
      if (result) {
        dispatch(setAnalysis(result));
        setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
    }
  };

  // Auto-analyse lors du changement de puzzle
  useEffect(() => {
    const autoAnalyze = async () => {
      if (!currentPuzzle) return;

      // Vérifier si une analyse existe déjà en cache
      const cachedAnalysis = dataStorage.getAnalysis(currentPuzzle.id);

      if (cachedAnalysis) {
        // Utiliser l'analyse en cache
        dispatch(setAnalysis(cachedAnalysis.analysis));
        setLastAnalysisTime(new Date(cachedAnalysis.timestamp).toLocaleString('fr-FR'));
      } else {
        // Lancer une nouvelle analyse
        try {
          const result = await analyzePuzzle(currentPuzzle).unwrap();
          if (result) {
            dispatch(setAnalysis(result));
            setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
          }
        } catch (error) {
          console.error('Erreur lors de l\'auto-analyse:', error);
        }
      }
    };

    autoAnalyze();
  }, [currentPuzzle?.id, analyzePuzzle, dispatch]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleAccordionChange = (section: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSections(prev =>
      isExpanded
        ? [...prev, section]
        : prev.filter(s => s !== section)
    );
  };

  // Fonction pour obtenir le statut d'un niveau d'analyse
  const getLevelStatus = (level: string) => {
    if (!analysis) return 'pending';

    switch (level) {
      case 'level0':
        return analysis.level_0 ? 'complete' : 'pending';
      case 'level1':
        return analysis.level_1 ? 'complete' : 'pending';
      case 'level2':
        return analysis.level_2 ? 'complete' : 'pending';
      case 'level3':
        return analysis.level_3 ? 'complete' : 'pending';
      default:
        return 'pending';
    }
  };



  if (!currentPuzzle) {
    return (
      <Paper sx={{ p: 2, height: '100%' }}>
        <Typography variant="h6" gutterBottom>
          Analyse ARC AGI
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Sélectionnez un puzzle pour voir l'analyse en 4 niveaux
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header avec contrôles */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Analyse ARC AGI</Typography>
            <Stack direction="column" alignItems="flex-end" spacing={0.5}>
              <Button
                size="small"
                startIcon={isLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                onClick={handleAnalyze}
                disabled={isLoading}
                variant="contained"
              >
                {isLoading ? 'Analyse...' : 'Analyser'}
              </Button>
              {lastAnalysisTime && (
                <Typography variant="caption" color="text.secondary">
                  Dernière analyse: {lastAnalysisTime}
                </Typography>
              )}
            </Stack>
          </Stack>

          {/* Indicateurs de progression par niveau */}
          {analysis && (
            <Stack direction="row" spacing={1} justifyContent="center">
              {[
                { key: 'level0', label: 'Niveau 0', icon: <DataObjectIcon /> },
                { key: 'level1', label: 'Niveau 1', icon: <AnalyticsIcon /> },
                { key: 'level2', label: 'Niveau 2', icon: <CompareIcon /> },
                { key: 'level3', label: 'Niveau 3', icon: <PsychologyIcon /> }
              ].map((level, index) => (
                <Tooltip key={level.key} title={level.label}>
                  <Chip
                    icon={level.icon}
                    label={`N${index}`}
                    size="small"
                    color={getLevelStatus(level.key) === 'complete' ? 'success' : 'default'}
                    variant={getLevelStatus(level.key) === 'complete' ? 'filled' : 'outlined'}
                  />
                </Tooltip>
              ))}
            </Stack>
          )}
        </Stack>
      </Box>

      {/* Onglets pour les différents niveaux */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ minHeight: 48 }}
        >
          <Tab
            icon={<DataObjectIcon />}
            label="Niveau 0"
            iconPosition="start"
            sx={{ minHeight: 48, fontSize: '0.875rem' }}
          />
          <Tab
            icon={<AnalyticsIcon />}
            label="Niveau 1"
            iconPosition="start"
            sx={{ minHeight: 48, fontSize: '0.875rem' }}
          />
          <Tab
            icon={<CompareIcon />}
            label="Niveau 2"
            iconPosition="start"
            sx={{ minHeight: 48, fontSize: '0.875rem' }}
          />
          <Tab
            icon={<PsychologyIcon />}
            label="Niveau 3"
            iconPosition="start"
            sx={{ minHeight: 48, fontSize: '0.875rem' }}
          />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <Box sx={{
        flex: '1 1 0', // Permet au conteneur de grandir et rétrécir
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: 0, // Important pour le flex
        '&::-webkit-scrollbar': {
          width: '12px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#f1f1f1',
          borderRadius: '6px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#888',
          borderRadius: '6px',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          background: '#555',
        },
      }}>
        {!analysis ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom color="text.secondary">
              Analyse ARC AGI en 4 Niveaux
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Cliquez sur "Analyser" pour obtenir une analyse complète selon l'architecture pure en niveaux
            </Typography>
            <Stack spacing={1} sx={{ maxWidth: 400, mx: 'auto' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <DataObjectIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 0</strong> : Données brutes (grilles, dimensions, fréquences)
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AnalyticsIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 1</strong> : Analyses dérivées (objets, patterns, séparations)
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CompareIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 2</strong> : Comparaisons input/output
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PsychologyIcon color="disabled" />
                <Typography variant="body2" color="text.secondary">
                  <strong>Niveau 3</strong> : Synthèse par entraînement
                </Typography>
              </Box>
            </Stack>
          </Box>
        ) : (
          <>
            {/* Niveau 0 : Données Brutes */}
            <TabPanel value={currentTab} index={0}>
              <Stack spacing={2}>
                <Typography variant="h6" gutterBottom>
                  Niveau 0 : Données Brutes
                </Typography>

                {!analysis.level_0 && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Aucune donnée du Niveau 0 disponible. Lancez une analyse pour voir les données brutes.
                    </Typography>
                  </Alert>
                )}

                {/* Exemples d'Entraînement */}
                {analysis.level_0?.training_examples && (
                  <Stack spacing={3}>
                    {analysis.level_0.training_examples.map((example, index) => (
                      <Box key={example.example_id}>
                        {/* Titre de l'exemple */}
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <GridOnIcon sx={{ mr: 1, color: 'primary.main' }} />
                          Exemple d'Entraînement {index + 1}
                        </Typography>

                        {/* Contenu côte à côte */}
                        <Grid container spacing={3}>
                          {/* Input */}
                          <Grid item xs={12} md={6}>
                            <Card variant="outlined" sx={{ height: '100%' }}>
                              <CardContent>
                                <Typography variant="h6" gutterBottom color="primary">
                                  Input
                                </Typography>
                                <Stack spacing={2}>
                                  <Box>
                                    <Typography variant="subtitle2" gutterBottom>Dimensions</Typography>
                                    <Stack direction="row" spacing={1}>
                                      <Chip label={`${example.input_grid.width}×${example.input_grid.height}`} size="small" />
                                      <Chip label={`${example.input_grid.width * example.input_grid.height} pixels`} size="small" color="info" />
                                    </Stack>
                                  </Box>
                                  <Box>
                                    <Typography variant="subtitle2" gutterBottom>Fréquences des Couleurs</Typography>
                                    <Stack direction="row" spacing={1} flexWrap="wrap">
                                      {Object.entries(example.input_grid.value_frequencies).map(([color, count]) => (
                                        <Chip
                                          key={color}
                                          label={showValues ? `${color}: ${count}` : `${count}`}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${color}-bg)`,
                                            color: `var(--symbol-${color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      ))}
                                    </Stack>
                                  </Box>
                                </Stack>
                              </CardContent>
                            </Card>
                          </Grid>

                          {/* Output */}
                          <Grid item xs={12} md={6}>
                            <Card variant="outlined" sx={{ height: '100%' }}>
                              <CardContent>
                                <Typography variant="h6" gutterBottom color="secondary">
                                  Output
                                </Typography>
                                <Stack spacing={2}>
                                  <Box>
                                    <Typography variant="subtitle2" gutterBottom>Dimensions</Typography>
                                    <Stack direction="row" spacing={1}>
                                      <Chip label={`${example.output_grid.width}×${example.output_grid.height}`} size="small" />
                                      <Chip label={`${example.output_grid.width * example.output_grid.height} pixels`} size="small" color="info" />
                                    </Stack>
                                  </Box>
                                  <Box>
                                    <Typography variant="subtitle2" gutterBottom>Fréquences des Couleurs</Typography>
                                    <Stack direction="row" spacing={1} flexWrap="wrap">
                                      {Object.entries(example.output_grid.value_frequencies).map(([color, count]) => (
                                        <Chip
                                          key={color}
                                          label={showValues ? `${color}: ${count}` : `${count}`}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${color}-bg)`,
                                            color: `var(--symbol-${color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      ))}
                                    </Stack>
                                  </Box>
                                </Stack>
                              </CardContent>
                            </Card>
                          </Grid>
                        </Grid>
                      </Box>
                    ))}
                  </Stack>
                )}
              </Stack>
            </TabPanel>

            {/* Niveau 1 : Analyses Dérivées */}
            <TabPanel value={currentTab} index={1}>
              <Stack spacing={2}>
                <Typography variant="h6" gutterBottom>
                  Niveau 1 : Analyses Dérivées par Domaine
                </Typography>

                {!analysis.level_1 && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Aucune donnée du Niveau 1 disponible. Lancez une analyse pour voir les analyses dérivées par domaine.
                    </Typography>
                  </Alert>
                )}

                {/* Analyse Détaillée - Tous les Exemples */}
                <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 4 }}>
                  Analyses Complètes par Exemple d'Entraînement
                </Typography>

                {/* Analyses de tous les exemples d'entraînement */}
                {analysis.level_1?.training_examples_analysis && (
                  <Stack spacing={2}>
                    {analysis.level_1.training_examples_analysis.map((exampleAnalysis, index) => (
                      <Accordion key={exampleAnalysis.example_id} expanded={expandedSections.includes(`example_${index}`)} onChange={handleAccordionChange(`example_${index}`)}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1">
                            <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                            Exemple d'Entraînement {index + 1}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Stack spacing={2}>
                            {/* Vue d'ensemble Input/Output */}
                            <Grid container spacing={2}>
                              <Grid item xs={12} md={6}>
                                <Card variant="outlined">
                                  <CardContent>
                                    <Typography variant="h6" gutterBottom color="primary">Input</Typography>
                                    <Stack spacing={1}>
                                      <Chip label={`${exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                                      <Chip label={`${exampleAnalysis.input_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                                      <Chip label={`Fond: ${showValues ? exampleAnalysis.input_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                                    </Stack>
                                  </CardContent>
                                </Card>
                              </Grid>
                              <Grid item xs={12} md={6}>
                                <Card variant="outlined">
                                  <CardContent>
                                    <Typography variant="h6" gutterBottom color="secondary">Output</Typography>
                                    <Stack spacing={1}>
                                      <Chip label={`${exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                                      <Chip label={`${exampleAnalysis.output_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                                      <Chip label={`Fond: ${showValues ? exampleAnalysis.output_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                                    </Stack>
                                  </CardContent>
                                </Card>
                              </Grid>
                            </Grid>

                            {/* Analyses détaillées par domaine */}
                            <Stack spacing={1}>
                              {/* Détection Géométrique */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Détection Géométrique
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                                    <Stack spacing={1}>
                                      <Chip label={`Lignes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                                      <Chip label={`Colonnes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                                      {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length > 0 && (
                                        <Typography variant="caption">
                                          Lignes: {exampleAnalysis.input_analysis.geometric_detection.uniform_rows.map(r => `${r.index}(${r.color})`).join(', ')}
                                        </Typography>
                                      )}
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                                    <Stack spacing={1}>
                                      <Chip label={`Lignes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                                      <Chip label={`Colonnes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                                      {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length > 0 && (
                                        <Typography variant="caption">
                                          Lignes: {exampleAnalysis.output_analysis.geometric_detection.uniform_rows.map(r => `${r.index}(${r.color})`).join(', ')}
                                        </Typography>
                                      )}
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Couleurs */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <PaletteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Analyse des Couleurs
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                                    <Stack spacing={1} direction="row" flexWrap="wrap">
                                      {exampleAnalysis.input_analysis.colors.present_colors.map(color => (
                                        <Chip
                                          key={color}
                                          label={showValues ? `${color}: ${exampleAnalysis.input_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.input_analysis.colors.color_distribution[color]}`}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${color}-bg)`,
                                            color: `var(--symbol-${color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      ))}
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                                    <Stack spacing={1} direction="row" flexWrap="wrap">
                                      {exampleAnalysis.output_analysis.colors.present_colors.map(color => (
                                        <Chip
                                          key={color}
                                          label={showValues ? `${color}: ${exampleAnalysis.output_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.output_analysis.colors.color_distribution[color]}`}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${color}-bg)`,
                                            color: `var(--symbol-${color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      ))}
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Objets sur Table */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <DataObjectIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Objets sur Table
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input ({exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets)</Typography>
                                    <Stack spacing={1}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                                        <Stack direction="row" spacing={1} flexWrap="wrap">
                                          <Chip
                                            label={showValues ? `Fond: ${exampleAnalysis.input_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Confiance: ${(exampleAnalysis.input_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Densité: ${(exampleAnalysis.input_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                            size="small"
                                            color="warning"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                                        <Stack spacing={1}>
                                          {exampleAnalysis.input_analysis.objects.detected_objects.map((obj, objIndex) => (
                                            <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                              <Stack direction="row" alignItems="center" spacing={1}>
                                                <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                                <Typography variant="caption">
                                                  {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                                  {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                                </Typography>
                                              </Stack>
                                            </Card>
                                          ))}
                                        </Stack>
                                      </Box>
                                      {exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                                        <Box>
                                          <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                                          <Chip
                                            label={`${exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                            size="small"
                                            color="error"
                                          />
                                        </Box>
                                      )}
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output ({exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets)</Typography>
                                    <Stack spacing={1}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                                        <Stack direction="row" spacing={1} flexWrap="wrap">
                                          <Chip
                                            label={showValues ? `Fond: ${exampleAnalysis.output_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Confiance: ${(exampleAnalysis.output_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Densité: ${(exampleAnalysis.output_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                            size="small"
                                            color="warning"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                                        <Stack spacing={1}>
                                          {exampleAnalysis.output_analysis.objects.detected_objects.map((obj, objIndex) => (
                                            <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                              <Stack direction="row" alignItems="center" spacing={1}>
                                                <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                                <Typography variant="caption">
                                                  {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                                  {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                                </Typography>
                                              </Stack>
                                            </Card>
                                          ))}
                                        </Stack>
                                      </Box>
                                      {exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                                        <Box>
                                          <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                                          <Chip
                                            label={`${exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                            size="small"
                                            color="error"
                                          />
                                        </Box>
                                      )}
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Classification Structurelle */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Classification Structurelle
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input - Structure</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Bordures:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Complétude: ${exampleAnalysis.input_analysis.structural_classification.borders.border_completeness}`}
                                            size="small"
                                            color={exampleAnalysis.input_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                                          />
                                          <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                            {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                              <Chip
                                                key={border}
                                                label={border.replace('_border', '').toUpperCase()}
                                                size="small"
                                                color={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                                variant={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                              />
                                            ))}
                                          </Stack>
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Total: ${exampleAnalysis.input_analysis.structural_classification.separators.length}`}
                                            size="small"
                                            color="secondary"
                                          />
                                          {exampleAnalysis.input_analysis.structural_classification.separators.slice(0, 3).map((sep, sepIndex) => (
                                            <Chip
                                              key={sepIndex}
                                              label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                              size="small"
                                              variant="outlined"
                                            />
                                          ))}
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                            size="small"
                                            color={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                                          />
                                          {exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size && (
                                            <Chip
                                              label={`${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                              size="small"
                                              color="info"
                                            />
                                          )}
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output - Structure</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Bordures:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Complétude: ${exampleAnalysis.output_analysis.structural_classification.borders.border_completeness}`}
                                            size="small"
                                            color={exampleAnalysis.output_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                                          />
                                          <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                            {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                              <Chip
                                                key={border}
                                                label={border.replace('_border', '').toUpperCase()}
                                                size="small"
                                                color={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                                variant={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                              />
                                            ))}
                                          </Stack>
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Total: ${exampleAnalysis.output_analysis.structural_classification.separators.length}`}
                                            size="small"
                                            color="secondary"
                                          />
                                          {exampleAnalysis.output_analysis.structural_classification.separators.slice(0, 3).map((sep, sepIndex) => (
                                            <Chip
                                              key={sepIndex}
                                              label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                              size="small"
                                              variant="outlined"
                                            />
                                          ))}
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                            size="small"
                                            color={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                                          />
                                          {exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size && (
                                            <Chip
                                              label={`${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                              size="small"
                                              color="info"
                                            />
                                          )}
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Symétries */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <SymmetryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Analyse des Symétries
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input - Symétries ({exampleAnalysis.input_analysis.symmetries.symmetry_count})</Typography>
                                    <Stack direction="row" spacing={1} flexWrap="wrap">
                                      <Chip
                                        label="Horizontale"
                                        size="small"
                                        color={exampleAnalysis.input_analysis.symmetries.horizontal ? 'success' : 'default'}
                                        variant={exampleAnalysis.input_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Verticale"
                                        size="small"
                                        color={exampleAnalysis.input_analysis.symmetries.vertical ? 'success' : 'default'}
                                        variant={exampleAnalysis.input_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Diagonale principale"
                                        size="small"
                                        color={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                                        variant={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Diagonale anti"
                                        size="small"
                                        color={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                                        variant={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                                      />
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output - Symétries ({exampleAnalysis.output_analysis.symmetries.symmetry_count})</Typography>
                                    <Stack direction="row" spacing={1} flexWrap="wrap">
                                      <Chip
                                        label="Horizontale"
                                        size="small"
                                        color={exampleAnalysis.output_analysis.symmetries.horizontal ? 'success' : 'default'}
                                        variant={exampleAnalysis.output_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Verticale"
                                        size="small"
                                        color={exampleAnalysis.output_analysis.symmetries.vertical ? 'success' : 'default'}
                                        variant={exampleAnalysis.output_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Diagonale principale"
                                        size="small"
                                        color={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                                        variant={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                                      />
                                      <Chip
                                        label="Diagonale anti"
                                        size="small"
                                        color={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                                        variant={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                                      />
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Motifs Répétitifs */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Motifs Répétitifs
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input - Motifs</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Patterns: ${exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Densité: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Couverture: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                            size="small"
                                            color="secondary"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Complexité:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Complexité: ${exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                            size="small"
                                            color={
                                              exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                                exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                            }
                                          />
                                          <Chip
                                            label={`Régularité: ${(exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output - Motifs</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Patterns: ${exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Densité: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Couverture: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                            size="small"
                                            color="secondary"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Complexité:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Complexité: ${exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                            size="small"
                                            color={
                                              exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                                exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                            }
                                          />
                                          <Chip
                                            label={`Régularité: ${(exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>

                              {/* Propriétés Spatiales */}
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Propriétés Spatiales
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="primary">Input - Propriétés Spatiales</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Densité objets: ${(exampleAnalysis.input_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Ratio fond: ${(exampleAnalysis.input_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Ratio rempli: ${(exampleAnalysis.input_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                            size="small"
                                            color="secondary"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={showValues ? `Centre: (${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                            size="small"
                                            color="warning"
                                          />
                                          <Chip
                                            label={`Distribution: ${exampleAnalysis.input_analysis.spatial_properties.spatial_distribution}`}
                                            size="small"
                                            color={
                                              exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                                exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                            }
                                          />
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                  <Grid item xs={12} md={6}>
                                    <Typography variant="subtitle2" gutterBottom color="secondary">Output - Propriétés Spatiales</Typography>
                                    <Stack spacing={2}>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={`Densité objets: ${(exampleAnalysis.output_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                            size="small"
                                            color="primary"
                                          />
                                          <Chip
                                            label={`Ratio fond: ${(exampleAnalysis.output_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                            size="small"
                                            color="info"
                                          />
                                          <Chip
                                            label={`Ratio rempli: ${(exampleAnalysis.output_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                            size="small"
                                            color="secondary"
                                          />
                                        </Stack>
                                      </Box>
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                                        <Stack spacing={1}>
                                          <Chip
                                            label={showValues ? `Centre: (${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                            size="small"
                                            color="warning"
                                          />
                                          <Chip
                                            label={`Distribution: ${exampleAnalysis.output_analysis.spatial_properties.spatial_distribution}`}
                                            size="small"
                                            color={
                                              exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                                exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                            }
                                          />
                                        </Stack>
                                      </Box>
                                    </Stack>
                                  </Grid>
                                </Grid>
                              </Box>


                            </Stack>
                          </Stack>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Stack>
                )}

                {/* Analyse Détaillée - Tous les Exemples */}
                <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 4 }}>
                  Analyses Détaillées
                </Typography>

                {/* Détection Géométrique (1A) - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('geometric_detection')} onChange={handleAccordionChange('geometric_detection')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Détection Géométrique
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Lignes Uniformes</Typography>
                                  <Stack spacing={1}>
                                    <Chip
                                      label={`Lignes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length}`}
                                      size="small"
                                      color="primary"
                                    />
                                    <Chip
                                      label={`Colonnes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length}`}
                                      size="small"
                                      color="primary"
                                    />
                                    {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length > 0 && (
                                      <Typography variant="caption" color="text.secondary">
                                        Lignes: {exampleAnalysis.input_analysis.geometric_detection.uniform_rows.map(r => `${r.index}(couleur ${r.color})`).join(', ')}
                                      </Typography>
                                    )}
                                    {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length > 0 && (
                                      <Typography variant="caption" color="text.secondary">
                                        Colonnes: {exampleAnalysis.input_analysis.geometric_detection.uniform_columns.map(c => `${c.index}(couleur ${c.color})`).join(', ')}
                                      </Typography>
                                    )}
                                  </Stack>
                                  {Object.keys(exampleAnalysis.input_analysis.geometric_detection.color_frequency_in_lines).length > 0 && (
                                    <Box sx={{ mt: 2 }}>
                                      <Typography variant="caption" gutterBottom>Distribution par Couleur:</Typography>
                                      <Stack direction="row" spacing={1} flexWrap="wrap">
                                        {Object.entries(exampleAnalysis.input_analysis.geometric_detection.color_frequency_in_lines).map(([color, count]) => (
                                          <Chip
                                            key={color}
                                            label={showValues ? `${color}: ${count}` : `${count}`}
                                            size="small"
                                            sx={{
                                              backgroundColor: `var(--symbol-${color}-bg)`,
                                              color: `var(--symbol-${color}-text)`,
                                              fontWeight: 'bold'
                                            }}
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                  )}
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Lignes Uniformes</Typography>
                                  <Stack spacing={1}>
                                    <Chip
                                      label={`Lignes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length}`}
                                      size="small"
                                      color="primary"
                                    />
                                    <Chip
                                      label={`Colonnes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length}`}
                                      size="small"
                                      color="primary"
                                    />
                                    {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length > 0 && (
                                      <Typography variant="caption" color="text.secondary">
                                        Lignes: {exampleAnalysis.output_analysis.geometric_detection.uniform_rows.map(r => `${r.index}(couleur ${r.color})`).join(', ')}
                                      </Typography>
                                    )}
                                    {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length > 0 && (
                                      <Typography variant="caption" color="text.secondary">
                                        Colonnes: {exampleAnalysis.output_analysis.geometric_detection.uniform_columns.map(c => `${c.index}(couleur ${c.color})`).join(', ')}
                                      </Typography>
                                    )}
                                  </Stack>
                                  {Object.keys(exampleAnalysis.output_analysis.geometric_detection.color_frequency_in_lines).length > 0 && (
                                    <Box sx={{ mt: 2 }}>
                                      <Typography variant="caption" gutterBottom>Distribution par Couleur:</Typography>
                                      <Stack direction="row" spacing={1} flexWrap="wrap">
                                        {Object.entries(exampleAnalysis.output_analysis.geometric_detection.color_frequency_in_lines).map(([color, count]) => (
                                          <Chip
                                            key={color}
                                            label={showValues ? `${color}: ${count}` : `${count}`}
                                            size="small"
                                            sx={{
                                              backgroundColor: `var(--symbol-${color}-bg)`,
                                              color: `var(--symbol-${color}-text)`,
                                              fontWeight: 'bold'
                                            }}
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                  )}
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de détection géométrique disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Classification Structurelle (1B) - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('structural_classification')} onChange={handleAccordionChange('structural_classification')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Classification Structurelle
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Structure</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Bordures:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Complétude: ${exampleAnalysis.input_analysis.structural_classification.borders.border_completeness}`}
                                          size="small"
                                          color={exampleAnalysis.input_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                                        />
                                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                          {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                            <Chip
                                              key={border}
                                              label={border.replace('_border', '').toUpperCase()}
                                              size="small"
                                              color={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                              variant={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                            />
                                          ))}
                                        </Stack>
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Total: ${exampleAnalysis.input_analysis.structural_classification.separators.length}`}
                                          size="small"
                                          color="secondary"
                                        />
                                        {exampleAnalysis.input_analysis.structural_classification.separators.slice(0, 3).map((sep, sepIndex) => (
                                          <Chip
                                            key={sepIndex}
                                            label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                            size="small"
                                            variant="outlined"
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                          size="small"
                                          color={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                                        />
                                        {exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size && (
                                          <Chip
                                            label={`${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                            size="small"
                                            color="info"
                                          />
                                        )}
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Structure</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Bordures:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Complétude: ${exampleAnalysis.output_analysis.structural_classification.borders.border_completeness}`}
                                          size="small"
                                          color={exampleAnalysis.output_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                                        />
                                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                          {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                            <Chip
                                              key={border}
                                              label={border.replace('_border', '').toUpperCase()}
                                              size="small"
                                              color={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                              variant={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                            />
                                          ))}
                                        </Stack>
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Total: ${exampleAnalysis.output_analysis.structural_classification.separators.length}`}
                                          size="small"
                                          color="secondary"
                                        />
                                        {exampleAnalysis.output_analysis.structural_classification.separators.slice(0, 3).map((sep, sepIndex) => (
                                          <Chip
                                            key={sepIndex}
                                            label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                            size="small"
                                            variant="outlined"
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                          size="small"
                                          color={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                                        />
                                        {exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size && (
                                          <Chip
                                            label={`${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                            size="small"
                                            color="info"
                                          />
                                        )}
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de classification structurelle disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Objets sur Table - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('objects')} onChange={handleAccordionChange('objects')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <DataObjectIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Objets sur Table
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Objets ({exampleAnalysis.input_analysis.objects.object_statistics.total_objects})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Fond: ${exampleAnalysis.input_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Confiance: ${(exampleAnalysis.input_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Densité: ${(exampleAnalysis.input_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                                      <Stack spacing={1}>
                                        {exampleAnalysis.input_analysis.objects.detected_objects.map((obj, objIndex) => (
                                          <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                            <Stack direction="row" alignItems="center" spacing={1}>
                                              <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                              <Typography variant="caption">
                                                {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                                {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                              </Typography>
                                            </Stack>
                                          </Card>
                                        ))}
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                                        <Chip
                                          label={`${exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                          size="small"
                                          color="error"
                                        />
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Objets ({exampleAnalysis.output_analysis.objects.object_statistics.total_objects})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Fond: ${exampleAnalysis.output_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Confiance: ${(exampleAnalysis.output_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Densité: ${(exampleAnalysis.output_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                                      <Stack spacing={1}>
                                        {exampleAnalysis.output_analysis.objects.detected_objects.map((obj, objIndex) => (
                                          <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                            <Stack direction="row" alignItems="center" spacing={1}>
                                              <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                              <Typography variant="caption">
                                                {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                                {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                              </Typography>
                                            </Stack>
                                          </Card>
                                        ))}
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                                        <Chip
                                          label={`${exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                          size="small"
                                          color="error"
                                        />
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée d'objets disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Analyse des Couleurs - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('colors')} onChange={handleAccordionChange('colors')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PaletteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse des Couleurs
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Couleurs ({exampleAnalysis.input_analysis.colors.present_colors.length})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Couleurs Présentes:</Typography>
                                      <Stack direction="row" spacing={1} flexWrap="wrap">
                                        {exampleAnalysis.input_analysis.colors.present_colors.map(color => (
                                          <Chip
                                            key={color}
                                            label={showValues ? `${color}: ${exampleAnalysis.input_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.input_analysis.colors.color_distribution[color]}`}
                                            size="small"
                                            sx={{
                                              backgroundColor: `var(--symbol-${color}-bg)`,
                                              color: `var(--symbol-${color}-text)`,
                                              fontWeight: 'bold'
                                            }}
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Dominante: ${exampleAnalysis.input_analysis.colors.dominant_color}` : 'Couleur dominante'}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${exampleAnalysis.input_analysis.colors.dominant_color}-bg)`,
                                            color: `var(--symbol-${exampleAnalysis.input_analysis.colors.dominant_color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                        <Chip
                                          label={showValues ? `Fond: ${exampleAnalysis.input_analysis.colors.background_color}` : 'Couleur de fond'}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${exampleAnalysis.input_analysis.colors.background_color}-bg)`,
                                            color: `var(--symbol-${exampleAnalysis.input_analysis.colors.background_color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Couleurs ({exampleAnalysis.output_analysis.colors.present_colors.length})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Couleurs Présentes:</Typography>
                                      <Stack direction="row" spacing={1} flexWrap="wrap">
                                        {exampleAnalysis.output_analysis.colors.present_colors.map(color => (
                                          <Chip
                                            key={color}
                                            label={showValues ? `${color}: ${exampleAnalysis.output_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.output_analysis.colors.color_distribution[color]}`}
                                            size="small"
                                            sx={{
                                              backgroundColor: `var(--symbol-${color}-bg)`,
                                              color: `var(--symbol-${color}-text)`,
                                              fontWeight: 'bold'
                                            }}
                                          />
                                        ))}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Dominante: ${exampleAnalysis.output_analysis.colors.dominant_color}` : 'Couleur dominante'}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${exampleAnalysis.output_analysis.colors.dominant_color}-bg)`,
                                            color: `var(--symbol-${exampleAnalysis.output_analysis.colors.dominant_color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                        <Chip
                                          label={showValues ? `Fond: ${exampleAnalysis.output_analysis.colors.background_color}` : 'Couleur de fond'}
                                          size="small"
                                          sx={{
                                            backgroundColor: `var(--symbol-${exampleAnalysis.output_analysis.colors.background_color}-bg)`,
                                            color: `var(--symbol-${exampleAnalysis.output_analysis.colors.background_color}-text)`,
                                            fontWeight: 'bold'
                                          }}
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de couleurs disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Symétries - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('symmetries')} onChange={handleAccordionChange('symmetries')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <SymmetryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse des Symétries
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Symétries ({exampleAnalysis.input_analysis.symmetries.symmetry_count})</Typography>
                                  <Stack direction="row" spacing={1} flexWrap="wrap">
                                    <Chip
                                      label="Horizontale"
                                      size="small"
                                      color={exampleAnalysis.input_analysis.symmetries.horizontal ? 'success' : 'default'}
                                      variant={exampleAnalysis.input_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Verticale"
                                      size="small"
                                      color={exampleAnalysis.input_analysis.symmetries.vertical ? 'success' : 'default'}
                                      variant={exampleAnalysis.input_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Diagonale principale"
                                      size="small"
                                      color={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                                      variant={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Diagonale anti"
                                      size="small"
                                      color={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                                      variant={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                                    />
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Symétries ({exampleAnalysis.output_analysis.symmetries.symmetry_count})</Typography>
                                  <Stack direction="row" spacing={1} flexWrap="wrap">
                                    <Chip
                                      label="Horizontale"
                                      size="small"
                                      color={exampleAnalysis.output_analysis.symmetries.horizontal ? 'success' : 'default'}
                                      variant={exampleAnalysis.output_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Verticale"
                                      size="small"
                                      color={exampleAnalysis.output_analysis.symmetries.vertical ? 'success' : 'default'}
                                      variant={exampleAnalysis.output_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Diagonale principale"
                                      size="small"
                                      color={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                                      variant={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                                    />
                                    <Chip
                                      label="Diagonale anti"
                                      size="small"
                                      color={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                                      variant={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                                    />
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de symétries disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Motifs Répétitifs - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('patterns')} onChange={handleAccordionChange('patterns')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Motifs Répétitifs
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Motifs</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Patterns: ${exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Densité: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Couverture: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Complexité:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Complexité: ${exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                          size="small"
                                          color={
                                            exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                              exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                          }
                                        />
                                        <Chip
                                          label={`Régularité: ${(exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Motifs</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Patterns: ${exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Densité: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Couverture: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Complexité:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Complexité: ${exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                          size="small"
                                          color={
                                            exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                              exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                          }
                                        />
                                        <Chip
                                          label={`Régularité: ${(exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de motifs disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Propriétés Spatiales - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('spatial_properties')} onChange={handleAccordionChange('spatial_properties')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Propriétés Spatiales
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Propriétés Spatiales</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Densité objets: ${(exampleAnalysis.input_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Ratio fond: ${(exampleAnalysis.input_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Ratio rempli: ${(exampleAnalysis.input_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Centre: (${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                          size="small"
                                          color="warning"
                                        />
                                        <Chip
                                          label={`Distribution: ${exampleAnalysis.input_analysis.spatial_properties.spatial_distribution}`}
                                          size="small"
                                          color={
                                            exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                              exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                          }
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Propriétés Spatiales</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Densité objets: ${(exampleAnalysis.output_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Ratio fond: ${(exampleAnalysis.output_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Ratio rempli: ${(exampleAnalysis.output_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={showValues ? `Centre: (${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                          size="small"
                                          color="warning"
                                        />
                                        <Chip
                                          label={`Distribution: ${exampleAnalysis.output_analysis.spatial_properties.spatial_distribution}`}
                                          size="small"
                                          color={
                                            exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                              exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                          }
                                        />
                                      </Stack>
                                    </Box>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de propriétés spatiales disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Patterns Détectés - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('detected_patterns')} onChange={handleAccordionChange('detected_patterns')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Patterns Détectés
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Patterns Détectés</Typography>
                                  <Stack spacing={1}>
                                    {exampleAnalysis.input_analysis.repeating_patterns.detected_patterns.length > 0 ? (
                                      exampleAnalysis.input_analysis.repeating_patterns.detected_patterns.slice(0, 5).map((pattern, patternIndex) => (
                                        <Card key={patternIndex} variant="outlined" sx={{ p: 1 }}>
                                          <Stack direction="row" alignItems="center" spacing={1}>
                                            <Typography variant="caption">
                                              {pattern.pattern_size[0]}×{pattern.pattern_size[1]} - {pattern.occurrence_count} occurrences
                                              {showValues && ` - ${pattern.pattern_type} - ${(pattern.coverage_ratio * 100).toFixed(1)}%`}
                                            </Typography>
                                          </Stack>
                                        </Card>
                                      ))
                                    ) : (
                                      <Typography variant="caption" color="text.secondary">
                                        Aucun pattern répétitif détecté
                                      </Typography>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Patterns Détectés</Typography>
                                  <Stack spacing={1}>
                                    {exampleAnalysis.output_analysis.repeating_patterns.detected_patterns.length > 0 ? (
                                      exampleAnalysis.output_analysis.repeating_patterns.detected_patterns.slice(0, 5).map((pattern, patternIndex) => (
                                        <Card key={patternIndex} variant="outlined" sx={{ p: 1 }}>
                                          <Stack direction="row" alignItems="center" spacing={1}>
                                            <Typography variant="caption">
                                              {pattern.pattern_size[0]}×{pattern.pattern_size[1]} - {pattern.occurrence_count} occurrences
                                              {showValues && ` - ${pattern.pattern_type} - ${(pattern.coverage_ratio * 100).toFixed(1)}%`}
                                            </Typography>
                                          </Stack>
                                        </Card>
                                      ))
                                    ) : (
                                      <Typography variant="caption" color="text.secondary">
                                        Aucun pattern répétitif détecté
                                      </Typography>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée de patterns détectés disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Analyse de Blocs (1C) - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('block_analysis')} onChange={handleAccordionChange('block_analysis')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <BlockIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse de Blocs
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Blocs ({exampleAnalysis.input_analysis.block_analysis?.detected_blocks.length || 0})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Uniformité des Blocs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.input_analysis.block_analysis?.block_uniformity.all_same_size ? 'Tailles uniformes' : 'Tailles variables'}
                                          size="small"
                                          color={exampleAnalysis.input_analysis.block_analysis?.block_uniformity.all_same_size ? 'success' : 'warning'}
                                        />
                                        {exampleAnalysis.input_analysis.block_analysis?.block_uniformity.uniform_dimensions && (
                                          <Chip
                                            label={`${exampleAnalysis.input_analysis.block_analysis.block_uniformity.uniform_dimensions[0]}×${exampleAnalysis.input_analysis.block_analysis.block_uniformity.uniform_dimensions[1]}`}
                                            size="small"
                                            color="info"
                                          />
                                        )}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Contenu des Blocs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Uniformes: ${exampleAnalysis.input_analysis.block_analysis?.block_content_patterns.uniform_blocks.length || 0}`}
                                          size="small"
                                          color="success"
                                        />
                                        <Chip
                                          label={`Avec motifs: ${exampleAnalysis.input_analysis.block_analysis?.block_content_patterns.patterned_blocks.length || 0}`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Divers: ${exampleAnalysis.input_analysis.block_analysis?.block_content_patterns.diverse_blocks.length || 0}`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.input_analysis.block_analysis?.block_content_patterns.background_consistency.same_background_across_blocks && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Cohérence du Fond:</Typography>
                                        <Chip
                                          label={showValues ? `Fond commun: ${exampleAnalysis.input_analysis.block_analysis.block_content_patterns.background_consistency.common_background_color}` : 'Fond cohérent'}
                                          size="small"
                                          color="success"
                                        />
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Blocs ({exampleAnalysis.output_analysis.block_analysis?.detected_blocks.length || 0})</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Uniformité des Blocs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.output_analysis.block_analysis?.block_uniformity.all_same_size ? 'Tailles uniformes' : 'Tailles variables'}
                                          size="small"
                                          color={exampleAnalysis.output_analysis.block_analysis?.block_uniformity.all_same_size ? 'success' : 'warning'}
                                        />
                                        {exampleAnalysis.output_analysis.block_analysis?.block_uniformity.uniform_dimensions && (
                                          <Chip
                                            label={`${exampleAnalysis.output_analysis.block_analysis.block_uniformity.uniform_dimensions[0]}×${exampleAnalysis.output_analysis.block_analysis.block_uniformity.uniform_dimensions[1]}`}
                                            size="small"
                                            color="info"
                                          />
                                        )}
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Contenu des Blocs:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Uniformes: ${exampleAnalysis.output_analysis.block_analysis?.block_content_patterns.uniform_blocks.length || 0}`}
                                          size="small"
                                          color="success"
                                        />
                                        <Chip
                                          label={`Avec motifs: ${exampleAnalysis.output_analysis.block_analysis?.block_content_patterns.patterned_blocks.length || 0}`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`Divers: ${exampleAnalysis.output_analysis.block_analysis?.block_content_patterns.diverse_blocks.length || 0}`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.output_analysis.block_analysis?.block_content_patterns.background_consistency.same_background_across_blocks && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Cohérence du Fond:</Typography>
                                        <Chip
                                          label={showValues ? `Fond commun: ${exampleAnalysis.output_analysis.block_analysis.block_content_patterns.background_consistency.common_background_color}` : 'Fond cohérent'}
                                          size="small"
                                          color="success"
                                        />
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée d'analyse de blocs disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Analyse de Mosaïques - Tous les exemples */}
                <Accordion expanded={expandedSections.includes('mosaics_analysis')} onChange={handleAccordionChange('mosaics_analysis')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <MosaicIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse de Mosaïques
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={3}>
                      {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis, index) => (
                        <Box key={exampleAnalysis.example_id}>
                          <Typography variant="h6" gutterBottom>
                            Exemple {index + 1}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="primary">Input - Détection de Mosaïque</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Détection:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.input_analysis.mosaics?.mosaic_detection.is_potential_mosaic ? 'Mosaïque potentielle' : 'Pas de mosaïque'}
                                          size="small"
                                          color={exampleAnalysis.input_analysis.mosaics?.mosaic_detection.is_potential_mosaic ? 'success' : 'default'}
                                        />
                                        <Chip
                                          label={`Confiance: ${((exampleAnalysis.input_analysis.mosaics?.mosaic_detection.confidence_score || 0) * 100).toFixed(1)}%`}
                                          size="small"
                                          color={
                                            (exampleAnalysis.input_analysis.mosaics?.mosaic_detection.confidence_score || 0) > 0.7 ? 'success' :
                                              (exampleAnalysis.input_analysis.mosaics?.mosaic_detection.confidence_score || 0) > 0.4 ? 'warning' : 'error'
                                          }
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse de Taille:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Catégorie: ${exampleAnalysis.input_analysis.mosaics?.mosaic_detection.size_analysis.size_category}`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`${exampleAnalysis.input_analysis.mosaics?.mosaic_detection.size_analysis.total_pixels} pixels`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Zones Uniformes:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Zones: ${exampleAnalysis.input_analysis.mosaics?.mosaic_detection.uniform_zones_detection.total_uniform_zones || 0}`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Couverture: ${((exampleAnalysis.input_analysis.mosaics?.mosaic_detection.uniform_zones_detection.uniform_coverage_ratio || 0) * 100).toFixed(1)}%`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.input_analysis.mosaics?.mosaic_detection.detection_reasons && exampleAnalysis.input_analysis.mosaics.mosaic_detection.detection_reasons.length > 0 && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Raisons:</Typography>
                                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                          {exampleAnalysis.input_analysis.mosaics.mosaic_detection.detection_reasons.map((reason, reasonIndex) => (
                                            <Chip
                                              key={reasonIndex}
                                              label={reason}
                                              size="small"
                                              variant="outlined"
                                            />
                                          ))}
                                        </Stack>
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Card variant="outlined">
                                <CardContent>
                                  <Typography variant="subtitle2" gutterBottom color="secondary">Output - Détection de Mosaïque</Typography>
                                  <Stack spacing={2}>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Détection:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={exampleAnalysis.output_analysis.mosaics?.mosaic_detection.is_potential_mosaic ? 'Mosaïque potentielle' : 'Pas de mosaïque'}
                                          size="small"
                                          color={exampleAnalysis.output_analysis.mosaics?.mosaic_detection.is_potential_mosaic ? 'success' : 'default'}
                                        />
                                        <Chip
                                          label={`Confiance: ${((exampleAnalysis.output_analysis.mosaics?.mosaic_detection.confidence_score || 0) * 100).toFixed(1)}%`}
                                          size="small"
                                          color={
                                            (exampleAnalysis.output_analysis.mosaics?.mosaic_detection.confidence_score || 0) > 0.7 ? 'success' :
                                              (exampleAnalysis.output_analysis.mosaics?.mosaic_detection.confidence_score || 0) > 0.4 ? 'warning' : 'error'
                                          }
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Analyse de Taille:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Catégorie: ${exampleAnalysis.output_analysis.mosaics?.mosaic_detection.size_analysis.size_category}`}
                                          size="small"
                                          color="info"
                                        />
                                        <Chip
                                          label={`${exampleAnalysis.output_analysis.mosaics?.mosaic_detection.size_analysis.total_pixels} pixels`}
                                          size="small"
                                          color="secondary"
                                        />
                                      </Stack>
                                    </Box>
                                    <Box>
                                      <Typography variant="caption" gutterBottom>Zones Uniformes:</Typography>
                                      <Stack spacing={1}>
                                        <Chip
                                          label={`Zones: ${exampleAnalysis.output_analysis.mosaics?.mosaic_detection.uniform_zones_detection.total_uniform_zones || 0}`}
                                          size="small"
                                          color="primary"
                                        />
                                        <Chip
                                          label={`Couverture: ${((exampleAnalysis.output_analysis.mosaics?.mosaic_detection.uniform_zones_detection.uniform_coverage_ratio || 0) * 100).toFixed(1)}%`}
                                          size="small"
                                          color="warning"
                                        />
                                      </Stack>
                                    </Box>
                                    {exampleAnalysis.output_analysis.mosaics?.mosaic_detection.detection_reasons && exampleAnalysis.output_analysis.mosaics.mosaic_detection.detection_reasons.length > 0 && (
                                      <Box>
                                        <Typography variant="caption" gutterBottom>Raisons:</Typography>
                                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                          {exampleAnalysis.output_analysis.mosaics.mosaic_detection.detection_reasons.map((reason, reasonIndex) => (
                                            <Chip
                                              key={reasonIndex}
                                              label={reason}
                                              size="small"
                                              variant="outlined"
                                            />
                                          ))}
                                        </Stack>
                                      </Box>
                                    )}
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          </Grid>
                        </Box>
                      )) || (
                          <Typography variant="body2" color="text.secondary">
                            Aucune donnée d'analyse de mosaïques disponible pour les exemples d'entraînement.
                          </Typography>
                        )}
                    </Stack>
                  </AccordionDetails>
                </Accordion>
              </Stack>
            </TabPanel>



            {/* Niveau 2 : Comparaisons Input/Output */}
            <TabPanel value={currentTab} index={2}>
              <Stack spacing={2}>
                <Typography variant="h6" gutterBottom>
                  Niveau 2 : Comparaisons Input/Output
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Comparaisons factuelles entre input et output pour détecter les transformations appliquées.
                  </Typography>
                </Alert>

                {!analysis.level_2 && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Aucune donnée du Niveau 2 disponible. Lancez une analyse pour voir les comparaisons.
                    </Typography>
                  </Alert>
                )}

                {analysis.level_2?.training_examples_comparisons && (
                  <Stack spacing={2}>
                    {analysis.level_2.training_examples_comparisons.map((comparison, index) => (
                      <Accordion key={comparison.example_id} expanded={expandedSections.includes(`comparison_${index}`)} onChange={handleAccordionChange(`comparison_${index}`)}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1">
                            <CompareIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                            Comparaison Exemple {index + 1}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Stack spacing={2}>
                            {/* Compatibilité Dimensionnelle */}
                            <Box>
                              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                Compatibilité Dimensionnelle
                              </Typography>
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                <Chip
                                  label={comparison.transformation_analysis.dimension_compatibility.same_dimensions ? 'Mêmes dimensions' : 'Dimensions différentes'}
                                  size="small"
                                  color={comparison.transformation_analysis.dimension_compatibility.same_dimensions ? 'success' : 'warning'}
                                />
                                {!comparison.transformation_analysis.dimension_compatibility.same_dimensions && (
                                  <>
                                    <Chip
                                      label={`Δ largeur: ${comparison.transformation_analysis.dimension_compatibility.width_change > 0 ? '+' : ''}${comparison.transformation_analysis.dimension_compatibility.width_change}`}
                                      size="small"
                                      color={comparison.transformation_analysis.dimension_compatibility.width_change === 0 ? 'success' : 'info'}
                                    />
                                    <Chip
                                      label={`Δ hauteur: ${comparison.transformation_analysis.dimension_compatibility.height_change > 0 ? '+' : ''}${comparison.transformation_analysis.dimension_compatibility.height_change}`}
                                      size="small"
                                      color={comparison.transformation_analysis.dimension_compatibility.height_change === 0 ? 'success' : 'info'}
                                    />
                                  </>
                                )}
                              </Stack>
                            </Box>

                            {/* Grille de Différences */}
                            <Box>
                              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                Grille de Différences
                              </Typography>
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                <Chip
                                  label={comparison.transformation_analysis.diff_grid.exists ? 'Grille diff disponible' : 'Pas de grille diff'}
                                  size="small"
                                  color={comparison.transformation_analysis.diff_grid.exists ? 'success' : 'default'}
                                />
                                {comparison.transformation_analysis.diff_grid.exists && comparison.transformation_analysis.diff_grid.change_ratio !== null && (
                                  <Chip
                                    label={`Changements: ${(comparison.transformation_analysis.diff_grid.change_ratio * 100).toFixed(1)}%`}
                                    size="small"
                                    color={
                                      comparison.transformation_analysis.diff_grid.change_ratio > 0.5 ? 'error' :
                                        comparison.transformation_analysis.diff_grid.change_ratio > 0.2 ? 'warning' : 'success'
                                    }
                                  />
                                )}
                                {comparison.transformation_analysis.diff_grid.change_positions && (
                                  <Chip
                                    label={`Positions modifiées: ${comparison.transformation_analysis.diff_grid.change_positions.length}`}
                                    size="small"
                                    color="info"
                                  />
                                )}
                              </Stack>
                            </Box>

                            {/* Analyse de la Grille Diff */}
                            {comparison.transformation_analysis.diff_grid.diff_analysis && (
                              <Box>
                                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                  Analyse de la Grille Diff
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  La grille diff a été analysée avec la même structure que les grilles input/output.
                                </Typography>
                                <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
                                  <Chip
                                    label={`${comparison.transformation_analysis.diff_grid.diff_analysis.colors.present_colors.length} couleurs détectées`}
                                    size="small"
                                    color="info"
                                  />
                                  <Chip
                                    label={`${comparison.transformation_analysis.diff_grid.diff_analysis.objects.detected_objects.length} objets détectés`}
                                    size="small"
                                    color="primary"
                                  />
                                </Stack>
                              </Box>
                            )}
                          </Stack>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Stack>
                )}
              </Stack>
            </TabPanel>

            {/* Niveau 3 : Synthèse par Entraînement */}
            <TabPanel value={currentTab} index={3}>
              <Stack spacing={2}>
                <Typography variant="h6" gutterBottom>
                  Niveau 3 : Synthèse par Entraînement
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Synthèse des patterns récurrents à travers tous les exemples d'entraînement pour formuler des règles globales.
                  </Typography>
                </Alert>

                {!analysis.level_3 && (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Le Niveau 3 (synthèse par entraînement) nécessite plusieurs exemples d'entraînement pour fonctionner.
                    </Typography>
                  </Alert>
                )}

                {/* Patterns Récurrents */}
                <Accordion expanded={expandedSections.includes('recurring_patterns')} onChange={handleAccordionChange('recurring_patterns')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Patterns Récurrents
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>Patterns Identifiés</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Patterns globaux: ${analysis.level_3?.recurring_patterns?.global_patterns?.length || 0}`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Cohérence: ${((analysis.level_3?.recurring_patterns?.consistency_score || 0) * 100).toFixed(1)}%`}
                                size="small"
                                color={
                                  (analysis.level_3?.recurring_patterns?.consistency_score || 0) > 0.8 ? 'success' :
                                    (analysis.level_3?.recurring_patterns?.consistency_score || 0) > 0.6 ? 'warning' : 'error'
                                }
                              />
                            </Stack>
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                {/* Règles Globales */}
                <Accordion expanded={expandedSections.includes('global_rules')} onChange={handleAccordionChange('global_rules')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PsychologyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Règles Globales
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>Hypothèses de Règles</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Règles candidates: ${analysis.level_3?.global_rules?.rule_hypotheses?.length || 0}`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance max: ${((analysis.level_3?.global_rules?.best_rule_confidence || 0) * 100).toFixed(1)}%`}
                                size="small"
                                color={
                                  (analysis.level_3?.global_rules?.best_rule_confidence || 0) > 0.9 ? 'success' :
                                    (analysis.level_3?.global_rules?.best_rule_confidence || 0) > 0.7 ? 'warning' : 'error'
                                }
                              />
                            </Stack>
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                {/* Prédictions */}
                <Accordion expanded={expandedSections.includes('predictions')} onChange={handleAccordionChange('predictions')}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <PsychologyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Prédictions pour le Test
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>Application sur l'Exemple Test</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Prédictions: ${analysis.level_3?.predictions?.test_predictions?.length || 0}`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance: ${((analysis.level_3?.predictions?.prediction_confidence || 0) * 100).toFixed(1)}%`}
                                size="small"
                                color={
                                  (analysis.level_3?.predictions?.prediction_confidence || 0) > 0.8 ? 'success' :
                                    (analysis.level_3?.predictions?.prediction_confidence || 0) > 0.6 ? 'warning' : 'error'
                                }
                              />
                            </Stack>
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Stack>
            </TabPanel>
          </>
        )}
      </Box>
    </Paper>
  );
};

export default AnalysisPanel;