import React from 'react';
import { Box } from '@mui/material';
import { TabPanelProps } from '../../types/AnalysisPanelTypes';

export const TabPanel: React.FC<TabPanelProps> = ({ 
  children, 
  value, 
  index, 
  className,
  ...other 
}) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analysis-tabpanel-${index}`}
      aria-labelledby={`analysis-tab-${index}`}
      className={className}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2, pb: 4 }}>
          {children}
        </Box>
      )}
    </div>
  );
};