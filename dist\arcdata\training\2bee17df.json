{"train": [{"input": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 0, 0, 0, 2, 2, 0, 2, 2, 2, 2, 2], [8, 0, 0, 0, 0, 2, 0, 0, 2, 2, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [8, 8, 0, 0, 0, 0, 8, 8, 0, 0, 0, 8], [8, 8, 8, 0, 0, 8, 8, 8, 0, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 0, 0, 3, 2, 2, 0, 2, 2, 2, 2, 2], [8, 0, 0, 3, 0, 2, 0, 0, 2, 2, 0, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8], [8, 8, 0, 3, 0, 0, 8, 8, 0, 0, 0, 8], [8, 8, 8, 3, 0, 8, 8, 8, 0, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8], [2, 2, 0, 0, 0, 0, 0, 8, 8, 0, 0, 8], [2, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 8], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 8], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [2, 2, 0, 2, 0, 0, 2, 0, 0, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 0, 0, 0, 3, 3, 8, 8, 8, 8, 8, 8], [2, 2, 0, 0, 3, 3, 0, 8, 8, 0, 0, 8], [2, 0, 0, 0, 3, 3, 0, 8, 0, 0, 0, 8], [2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8], [2, 2, 2, 0, 3, 3, 0, 0, 0, 0, 0, 8], [2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 8], [2, 2, 0, 0, 3, 3, 0, 0, 0, 0, 0, 8], [2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8], [2, 0, 0, 0, 3, 3, 0, 0, 0, 0, 2, 2], [2, 2, 0, 2, 3, 3, 2, 0, 0, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 2], [8, 8, 8, 0, 8, 8, 0, 8, 0, 2], [8, 8, 0, 0, 8, 0, 0, 0, 0, 2], [8, 8, 0, 0, 0, 0, 0, 0, 2, 2], [8, 0, 0, 0, 0, 0, 0, 0, 2, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 2, 2, 0, 2], [8, 2, 0, 0, 0, 2, 2, 2, 2, 2], [8, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 2], [8, 8, 8, 3, 8, 8, 0, 8, 0, 2], [8, 8, 0, 3, 8, 0, 0, 0, 0, 2], [8, 8, 0, 3, 0, 0, 0, 0, 2, 2], [8, 0, 0, 3, 0, 0, 0, 0, 2, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 0, 0, 3, 0, 0, 2, 2, 0, 2], [8, 2, 0, 3, 0, 2, 2, 2, 2, 2], [8, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 0, 0, 8, 8, 8, 0, 0, 8, 2, 2], [8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2], [8, 8, 0, 2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 2], [8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 0, 8, 8, 8, 3, 3, 8, 2, 2], [8, 8, 8, 0, 3, 0, 0, 0, 0, 3, 3, 0, 0, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 8, 8, 0, 3, 0, 0, 0, 0, 3, 3, 0, 2, 2], [8, 8, 0, 0, 3, 0, 0, 0, 0, 3, 3, 2, 2, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 8, 0, 0, 3, 0, 0, 0, 0, 3, 3, 0, 0, 2], [8, 8, 0, 0, 3, 0, 0, 0, 0, 3, 3, 0, 0, 2], [8, 8, 0, 0, 3, 0, 0, 0, 0, 3, 3, 0, 0, 2], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2], [8, 8, 0, 0, 3, 0, 0, 0, 0, 3, 3, 0, 2, 2], [8, 8, 0, 2, 3, 2, 2, 0, 0, 3, 3, 2, 2, 2], [8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}]}