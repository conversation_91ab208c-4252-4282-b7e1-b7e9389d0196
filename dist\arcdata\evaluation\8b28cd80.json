{"train": [{"input": [[0, 0, 0], [0, 4, 0], [0, 0, 0]], "output": [[4, 0, 4, 4, 4, 4, 4, 4, 4], [4, 0, 4, 0, 0, 0, 0, 0, 4], [4, 0, 4, 0, 4, 4, 4, 0, 4], [4, 0, 4, 0, 4, 0, 4, 0, 4], [4, 0, 4, 0, 4, 0, 4, 0, 4], [4, 0, 4, 0, 0, 0, 4, 0, 4], [4, 0, 4, 4, 4, 4, 4, 0, 4], [4, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[0, 0, 0], [5, 0, 0], [0, 0, 0]], "output": [[5, 5, 5, 5, 5, 0, 5, 0, 5], [0, 0, 0, 0, 5, 0, 5, 0, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5], [5, 0, 5, 0, 5, 0, 5, 0, 5], [5, 0, 5, 0, 5, 0, 5, 0, 5], [0, 0, 5, 0, 5, 0, 5, 0, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5], [0, 0, 0, 0, 5, 0, 5, 0, 5], [5, 5, 5, 5, 5, 0, 5, 0, 5]]}, {"input": [[0, 3, 0], [0, 0, 0], [0, 0, 0]], "output": [[3, 0, 3, 0, 3, 0, 3, 0, 3], [3, 0, 3, 0, 0, 0, 3, 0, 3], [3, 0, 3, 3, 3, 3, 3, 0, 3], [3, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0], [0, 0, 8], [0, 0, 0]], "output": [[8, 0, 8, 0, 8, 0, 8, 8, 8], [8, 0, 8, 0, 8, 0, 8, 0, 0], [8, 0, 8, 0, 8, 0, 8, 0, 8], [8, 0, 8, 0, 8, 0, 8, 0, 8], [8, 0, 8, 0, 8, 0, 8, 0, 8], [8, 0, 8, 0, 8, 0, 8, 0, 0], [8, 0, 8, 0, 8, 0, 8, 8, 8], [8, 0, 8, 0, 8, 0, 0, 0, 0], [8, 0, 8, 0, 8, 8, 8, 8, 8]]}, {"input": [[0, 0, 7], [0, 0, 0], [0, 0, 0]], "output": [[7, 0, 7, 0, 7, 0, 7, 0, 7], [7, 0, 7, 0, 7, 0, 7, 0, 0], [7, 0, 7, 0, 7, 0, 7, 7, 7], [7, 0, 7, 0, 7, 0, 0, 0, 0], [7, 0, 7, 0, 7, 7, 7, 7, 7], [7, 0, 7, 0, 0, 0, 0, 0, 0], [7, 0, 7, 7, 7, 7, 7, 7, 7], [7, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 7, 7, 7, 7]]}], "test": [{"input": [[0, 0, 0], [0, 0, 0], [0, 0, 6]], "output": [[6, 0, 6, 6, 6, 6, 6, 6, 6], [6, 0, 6, 0, 0, 0, 0, 0, 0], [6, 0, 6, 0, 6, 6, 6, 6, 6], [6, 0, 6, 0, 6, 0, 0, 0, 0], [6, 0, 6, 0, 6, 0, 6, 6, 6], [6, 0, 6, 0, 6, 0, 6, 0, 0], [6, 0, 6, 0, 6, 0, 6, 0, 6], [6, 0, 6, 0, 6, 0, 6, 0, 6], [6, 0, 6, 0, 6, 0, 6, 0, 6]]}, {"input": [[0, 0, 0], [0, 0, 0], [3, 0, 0]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 3, 0, 3], [0, 0, 0, 0, 0, 0, 3, 0, 3], [3, 3, 3, 3, 3, 0, 3, 0, 3], [0, 0, 0, 0, 3, 0, 3, 0, 3], [3, 3, 3, 0, 3, 0, 3, 0, 3], [3, 0, 3, 0, 3, 0, 3, 0, 3], [3, 0, 3, 0, 3, 0, 3, 0, 3]]}]}