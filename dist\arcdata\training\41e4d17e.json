{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 6, 8, 1, 8, 8, 8, 8, 8, 8, 8], [6, 6, 6, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6], [8, 8, 8, 1, 8, 6, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 1, 8, 6, 8, 1, 8, 8, 6, 8, 8, 8, 8], [6, 6, 6, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6], [8, 8, 8, 1, 8, 6, 8, 1, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 1, 1, 1, 1, 1, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 1, 8, 6, 8, 1, 8, 8], [6, 6, 6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 1, 6, 6], [8, 8, 8, 8, 8, 6, 8, 8, 1, 8, 6, 8, 1, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 1, 1, 1, 1, 1, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8, 8, 8, 8]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 1, 1, 1, 1, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 1, 8, 6, 8, 1, 8, 8, 8, 8], [6, 6, 6, 6, 6, 6, 1, 6, 6, 6, 1, 6, 6, 6, 6], [8, 8, 8, 8, 8, 6, 1, 8, 6, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 1, 1, 1, 1, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 6, 8, 1, 6, 8, 8, 8, 8, 8, 8], [6, 6, 6, 1, 6, 6, 6, 1, 6, 6, 6, 6, 6, 6, 6], [8, 8, 8, 1, 8, 6, 8, 1, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 1, 1, 1, 1, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8, 8, 8, 8]]}]}