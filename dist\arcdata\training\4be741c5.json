{"train": [{"input": [[4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 8, 8], [4, 4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8], [4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 8, 8, 8], [4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8], [4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8], [4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8], [4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8]], "output": [[4, 2, 8]]}, {"input": [[2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2], [2, 8, 8, 8, 2, 2, 8], [8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 5, 5, 8, 8], [5, 8, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5]], "output": [[2], [8], [5]]}, {"input": [[6, 6, 6, 6, 6, 6, 6, 6, 6], [6, 6, 4, 4, 6, 6, 6, 6, 6], [6, 4, 4, 4, 6, 4, 6, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 2, 2, 4, 4, 4, 2, 2, 4], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 3, 2, 2, 2, 2, 2, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[6], [4], [2], [3]]}], "test": [{"input": [[3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 8, 8], [3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 8, 8, 8], [3, 3, 3, 3, 3, 2, 2, 1, 1, 1, 8, 8, 8, 8], [3, 3, 3, 3, 3, 2, 2, 1, 1, 1, 1, 8, 8, 8], [3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 8, 8], [3, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 8], [3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 8, 8], [3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 8, 8, 8], [3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 8, 8], [3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 8, 8], [3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 8, 8, 8], [3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 8, 8], [3, 3, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 8, 8], [3, 3, 3, 3, 3, 3, 2, 2, 2, 1, 1, 1, 8, 8]], "output": [[3, 2, 1, 8]]}]}