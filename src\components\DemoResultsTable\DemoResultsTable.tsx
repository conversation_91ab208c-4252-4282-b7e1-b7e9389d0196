import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Typography,
  Box,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { DemoResult } from '../../types';

interface DemoResultsTableProps {
  results: DemoResult[];
}

const DemoResultsTable: React.FC<DemoResultsTableProps> = ({ results }) => {
  const [expandedRows, setExpandedRows] = React.useState<Set<string>>(new Set());

  const toggleRow = (puzzleId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(puzzleId)) {
      newExpanded.delete(puzzleId);
    } else {
      newExpanded.add(puzzleId);
    }
    setExpandedRows(newExpanded);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  const formatCost = (cost: number) => {
    if (cost < 0.001) return '<$0.001';
    return `$${cost.toFixed(4)}`;
  };

  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell width={40}></TableCell>
            <TableCell>Puzzle ID</TableCell>
            <TableCell align="center">Statut</TableCell>
            <TableCell align="center">Précision</TableCell>
            <TableCell align="center">Tentatives</TableCell>
            <TableCell align="center">Temps</TableCell>
            <TableCell align="center">Coût</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {results.map((result) => (
            <React.Fragment key={result.puzzleId}>
              <TableRow hover>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => toggleRow(result.puzzleId)}
                  >
                    {expandedRows.has(result.puzzleId) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {result.puzzleId}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Chip
                    icon={result.success ? <SuccessIcon /> : <ErrorIcon />}
                    label={result.success ? 'Succès' : 'Échec'}
                    color={result.success ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {(result.accuracy * 100).toFixed(1)}%
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {result.attempts}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {formatDuration(result.responseTime)}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {formatCost(result.cost)}
                  </Typography>
                </TableCell>
              </TableRow>
              
              {/* Ligne de détails expandable */}
              <TableRow>
                <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
                  <Collapse in={expandedRows.has(result.puzzleId)} timeout="auto" unmountOnExit>
                    <Box sx={{ margin: 1 }}>
                      <Typography variant="h6" gutterBottom component="div">
                        Détails
                      </Typography>
                      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 2 }}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Informations générales
                          </Typography>
                          <Typography variant="body2">
                            • Puzzle ID: {result.puzzleId}
                          </Typography>
                          <Typography variant="body2">
                            • Statut: {result.success ? 'Résolu avec succès' : 'Échec de résolution'}
                          </Typography>
                          <Typography variant="body2">
                            • Nombre de tentatives: {result.attempts}
                          </Typography>
                        </Box>
                        
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Métriques de performance
                          </Typography>
                          <Typography variant="body2">
                            • Précision: {(result.accuracy * 100).toFixed(2)}%
                          </Typography>
                          <Typography variant="body2">
                            • Erreurs: {result.errorCount} cellules
                          </Typography>
                          <Typography variant="body2">
                            • Temps de réponse: {formatDuration(result.responseTime)}
                          </Typography>
                          <Typography variant="body2">
                            • Coût: {formatCost(result.cost)}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      
      {results.length === 0 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Aucun résultat disponible
          </Typography>
        </Box>
      )}
    </TableContainer>
  );
};

export default DemoResultsTable;
