{"train": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 0, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 0, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], [2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 0, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 0, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 0, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4]], "output": [[2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 0, 0, 0, 0, 0, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 5, 5, 5, 5, 5, 4, 4, 4, 4, 4, 4]]}], "test": [{"input": [[8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "output": [[8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4], [2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]]}]}