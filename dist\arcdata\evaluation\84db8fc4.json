{"train": [{"input": [[3, 1, 0, 3, 3, 3, 3, 3, 0, 3], [1, 0, 0, 3, 3, 0, 1, 3, 1, 1], [0, 1, 1, 1, 0, 3, 0, 0, 0, 3], [0, 1, 3, 3, 0, 3, 1, 3, 0, 0], [1, 3, 1, 1, 0, 1, 3, 0, 0, 0], [0, 1, 1, 3, 0, 0, 3, 1, 1, 3], [3, 0, 1, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 3, 3, 1, 0, 0, 1, 3], [3, 3, 1, 0, 0, 1, 1, 0, 0, 1], [0, 1, 3, 0, 1, 1, 1, 1, 1, 3]], "output": [[3, 1, 2, 3, 3, 3, 3, 3, 2, 3], [1, 2, 2, 3, 3, 5, 1, 3, 1, 1], [2, 1, 1, 1, 5, 3, 2, 2, 2, 3], [2, 1, 3, 3, 5, 3, 1, 3, 2, 2], [1, 3, 1, 1, 5, 1, 3, 2, 2, 2], [2, 1, 1, 3, 5, 5, 3, 1, 1, 3], [3, 2, 1, 5, 5, 5, 5, 5, 3, 2], [2, 2, 2, 3, 3, 1, 5, 5, 1, 3], [3, 3, 1, 2, 2, 1, 1, 5, 5, 1], [2, 1, 3, 2, 1, 1, 1, 1, 1, 3]]}, {"input": [[0, 3, 3, 0, 3, 1, 0, 1, 1, 3], [1, 3, 0, 0, 1, 1, 3, 1, 0, 0], [1, 0, 1, 0, 0, 1, 3, 0, 3, 3], [0, 0, 3, 3, 1, 3, 3, 3, 0, 1], [0, 0, 3, 3, 0, 0, 0, 0, 3, 1], [3, 3, 0, 0, 3, 0, 0, 0, 3, 0], [0, 0, 3, 3, 3, 0, 3, 0, 3, 3], [3, 1, 1, 1, 3, 0, 1, 1, 1, 3], [0, 0, 1, 3, 1, 0, 0, 3, 3, 3], [0, 3, 3, 0, 3, 3, 1, 3, 1, 1]], "output": [[2, 3, 3, 2, 3, 1, 2, 1, 1, 3], [1, 3, 2, 2, 1, 1, 3, 1, 2, 2], [1, 2, 1, 2, 2, 1, 3, 5, 3, 3], [2, 2, 3, 3, 1, 3, 3, 3, 5, 1], [2, 2, 3, 3, 5, 5, 5, 5, 3, 1], [3, 3, 5, 5, 3, 5, 5, 5, 3, 2], [2, 2, 3, 3, 3, 5, 3, 5, 3, 3], [3, 1, 1, 1, 3, 5, 1, 1, 1, 3], [2, 2, 1, 3, 1, 5, 5, 3, 3, 3], [2, 3, 3, 2, 3, 3, 1, 3, 1, 1]]}, {"input": [[0, 3, 0, 3, 0, 0, 1, 3, 3, 1], [0, 1, 1, 1, 1, 3, 0, 0, 1, 1], [0, 3, 1, 0, 1, 0, 3, 0, 3, 0], [3, 3, 3, 0, 0, 3, 3, 3, 0, 0], [1, 1, 3, 1, 3, 0, 0, 0, 1, 0], [1, 0, 1, 0, 3, 0, 3, 3, 0, 3], [0, 0, 0, 0, 1, 1, 3, 0, 1, 0], [3, 0, 1, 3, 3, 1, 0, 3, 0, 0], [1, 1, 0, 0, 1, 3, 3, 1, 1, 3], [0, 0, 1, 1, 0, 1, 0, 0, 0, 0]], "output": [[2, 3, 2, 3, 2, 2, 1, 3, 3, 1], [2, 1, 1, 1, 1, 3, 5, 5, 1, 1], [2, 3, 1, 5, 1, 5, 3, 5, 3, 2], [3, 3, 3, 5, 5, 3, 3, 3, 2, 2], [1, 1, 3, 1, 3, 5, 5, 5, 1, 2], [1, 2, 1, 2, 3, 5, 3, 3, 5, 3], [2, 2, 2, 2, 1, 1, 3, 5, 1, 2], [3, 2, 1, 3, 3, 1, 5, 3, 2, 2], [1, 1, 5, 5, 1, 3, 3, 1, 1, 3], [2, 2, 1, 1, 2, 1, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 3, 1, 1, 3], [0, 0, 3, 1, 0, 1, 1, 0, 0, 3], [0, 1, 0, 0, 1, 3, 3, 1, 3, 1], [0, 1, 3, 0, 0, 0, 0, 0, 1, 0], [0, 1, 3, 1, 0, 1, 0, 3, 0, 1], [1, 0, 0, 3, 1, 3, 1, 0, 1, 0], [1, 0, 0, 3, 0, 1, 0, 3, 0, 0], [0, 1, 0, 1, 1, 0, 3, 1, 0, 3], [0, 3, 1, 1, 3, 0, 0, 3, 1, 0], [1, 1, 3, 3, 0, 0, 1, 3, 0, 3]], "output": [[2, 2, 2, 2, 2, 2, 3, 1, 1, 3], [2, 2, 3, 1, 2, 1, 1, 5, 5, 3], [2, 1, 5, 5, 1, 3, 3, 1, 3, 1], [2, 1, 3, 5, 5, 5, 5, 5, 1, 2], [2, 1, 3, 1, 5, 1, 5, 3, 5, 1], [1, 5, 5, 3, 1, 3, 1, 5, 1, 2], [1, 5, 5, 3, 5, 1, 5, 3, 2, 2], [2, 1, 5, 1, 1, 2, 3, 1, 2, 3], [2, 3, 1, 1, 3, 2, 2, 3, 1, 2], [1, 1, 3, 3, 2, 2, 1, 3, 2, 3]]}], "test": [{"input": [[1, 0, 0, 1, 0, 1, 1, 1, 1, 3], [0, 0, 0, 3, 0, 3, 0, 1, 0, 0], [0, 1, 0, 3, 3, 0, 1, 3, 3, 3], [3, 1, 3, 1, 1, 0, 3, 3, 0, 1], [1, 1, 3, 0, 1, 3, 0, 1, 1, 0], [0, 3, 0, 1, 3, 0, 1, 1, 0, 3], [1, 1, 3, 0, 0, 3, 0, 3, 3, 3], [3, 1, 1, 1, 1, 3, 1, 0, 3, 1], [3, 0, 0, 0, 3, 3, 1, 0, 1, 1], [1, 0, 3, 1, 1, 0, 0, 0, 1, 0]], "output": [[1, 2, 2, 1, 2, 1, 1, 1, 1, 3], [2, 2, 2, 3, 2, 3, 5, 1, 2, 2], [2, 1, 2, 3, 3, 5, 1, 3, 3, 3], [3, 1, 3, 1, 1, 5, 3, 3, 5, 1], [1, 1, 3, 5, 1, 3, 5, 1, 1, 2], [2, 3, 5, 1, 3, 5, 1, 1, 5, 3], [1, 1, 3, 5, 5, 3, 5, 3, 3, 3], [3, 1, 1, 1, 1, 3, 1, 2, 3, 1], [3, 2, 2, 2, 3, 3, 1, 2, 1, 1], [1, 2, 3, 1, 1, 2, 2, 2, 1, 2]]}]}