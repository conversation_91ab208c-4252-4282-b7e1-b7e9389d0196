{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 0, 0, 0], [0, 0, 0, 8, 4, 3, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 2, 6, 8, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 8, 8, 8, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 7, 7, 7, 7, 7, 7, 0, 0], [0, 0, 7, 2, 0, 0, 8, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 9, 0, 0, 3, 7, 0, 0], [0, 0, 7, 7, 7, 7, 7, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 9, 0], [0, 0, 7, 7, 7, 7, 7, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 7, 7, 7, 7, 7, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 1, 2, 0, 0, 5, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 6, 0, 0, 3, 1, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 0, 0, 0, 0, 0, 0, 6, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0], [0, 0, 3, 6, 0, 0, 4, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 8, 0, 0, 2, 3, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 8, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 6, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}