import React from 'react';
import { Stack, Typography, Box, Grid, Card, CardContent, Chip } from '@mui/material';
import { DataObject as DataObjectIcon, GridOn as GridOnIcon } from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { Level0PanelProps } from '../types/AnalysisPanelTypes';

export const Level0Panel: React.FC<Level0PanelProps> = ({ analysis, showValues }) => {
  if (!analysis.level_0) {
    return (
      <LevelHeader
        level={0}
        title="Niveau 0 : Données Brutes"
        description="Stockage des données factuelles pures, sans aucune interprétation ni calcul dérivé."
        icon={<DataObjectIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={0}
        title="Niveau 0 : Données Brutes"
        description="Stockage des données factuelles pures, sans aucune interprétation ni calcul dérivé."
        icon={<DataObjectIcon />}
        hasData={true}
      />

      {/* Exemples d'Entraînement */}
      {analysis.level_0.training_examples && (
        <Stack spacing={3}>
          {analysis.level_0.training_examples.map((example: any, index: number) => (
            <Box key={example.example_id}>
              {/* Titre de l'exemple */}
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <GridOnIcon sx={{ mr: 1, color: 'primary.main' }} />
                Exemple d'Entraînement {index + 1}
              </Typography>

              {/* Contenu côte à côte */}
              <Grid container spacing={3}>
                {/* Input */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Input
                      </Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Dimensions</Typography>
                          <Stack direction="row" spacing={1}>
                            <Chip label={`${example.input_grid.width}×${example.input_grid.height}`} size="small" />
                            <Chip label={`${example.input_grid.width * example.input_grid.height} pixels`} size="small" color="info" />
                          </Stack>
                        </Box>
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Fréquences des Couleurs</Typography>
                          <Stack direction="row" spacing={1} flexWrap="wrap">
                            {Object.entries(example.input_grid.value_frequencies).map(([color, count]) => (
                              <Chip
                                key={color}
                                label={showValues ? `${color}: ${count}` : `${count}`}
                                size="small"
                                sx={{
                                  backgroundColor: `var(--symbol-${color}-bg)`,
                                  color: `var(--symbol-${color}-text)`,
                                  fontWeight: 'bold'
                                }}
                              />
                            ))}
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Output */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="secondary">
                        Output
                      </Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Dimensions</Typography>
                          <Stack direction="row" spacing={1}>
                            <Chip label={`${example.output_grid.width}×${example.output_grid.height}`} size="small" />
                            <Chip label={`${example.output_grid.width * example.output_grid.height} pixels`} size="small" color="info" />
                          </Stack>
                        </Box>
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Fréquences des Couleurs</Typography>
                          <Stack direction="row" spacing={1} flexWrap="wrap">
                            {Object.entries(example.output_grid.value_frequencies).map(([color, count]) => (
                              <Chip
                                key={color}
                                label={showValues ? `${color}: ${count}` : `${count}`}
                                size="small"
                                sx={{
                                  backgroundColor: `var(--symbol-${color}-bg)`,
                                  color: `var(--symbol-${color}-text)`,
                                  fontWeight: 'bold'
                                }}
                              />
                            ))}
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          ))}
        </Stack>
      )}
    </Stack>
  );
};