{"train": [{"input": [[0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3, 3], [4, 4, 4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3, 3], [4, 4, 4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3, 3], [4, 4, 4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0]], "output": [[0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0], [0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0], [0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0], [0, 0, 8, 2, 2, 2, 2, 2, 2, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3], [4, 4, 8, 6, 6, 6, 6, 6, 6, 8, 3, 3, 3, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 8, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [4, 4, 4, 8, 6, 6, 6, 6, 8, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0]]}]}