{"train": [{"input": [[0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 2, 2], [2, 4, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 2], [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 2, 0, 2], [2, 0, 0, 0, 4, 0, 2, 0, 0, 0, 2, 2, 2], [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 4, 0]], "output": [[4, 4, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 4], [4, 4, 0, 0, 0, 0, 4], [4, 0, 0, 0, 0, 0, 4], [4, 0, 0, 0, 4, 0, 4], [4, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 3, 0, 0, 1, 0], [0, 3, 0, 0, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 3, 0, 1, 3, 0, 3, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0], [0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 3, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 3, 0, 0, 3, 3, 0, 0, 0, 0]], "output": [[3, 3, 3, 3, 3, 3, 3], [3, 0, 0, 3, 0, 0, 3], [3, 0, 0, 0, 0, 0, 3], [3, 3, 0, 3, 0, 0, 3], [3, 0, 0, 0, 0, 0, 3], [3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 0, 0, 2, 0, 0, 0, 0, 0], [0, 3, 0, 2, 0, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 3, 3, 3, 3, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 2, 0, 3, 0, 2, 3, 0, 0], [0, 3, 0, 2, 0, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0], [0, 3, 0, 0, 0, 0, 2, 3, 0, 0, 3, 0, 0, 3, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 0, 0, 3, 3, 3, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2], [0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 3, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 2, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 0, 2, 0, 2], [2, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 0, 0, 2], [2, 0, 2, 0, 0, 0, 2], [2, 0, 0, 0, 0, 2, 2], [2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0], [1, 1, 1, 0, 1, 0, 8, 0, 8, 0, 0, 1, 8, 0, 0, 0, 0], [1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 8, 0, 8, 0, 0], [1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 8, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 8, 0, 0, 0, 0, 1, 0, 0, 0, 0, 8], [0, 0, 0, 0, 1, 0, 0, 0, 0, 8, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 8, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 1, 8, 0, 8, 0, 1, 0], [0, 0, 0, 8, 8, 0, 0, 8, 0, 0, 1, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8], [8, 0, 8, 0, 8, 0, 0, 8], [8, 0, 0, 0, 0, 0, 0, 8], [8, 0, 0, 0, 0, 0, 0, 8], [8, 0, 0, 0, 0, 0, 0, 8], [8, 0, 8, 0, 0, 0, 0, 8], [8, 0, 0, 0, 0, 8, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8]]}]}