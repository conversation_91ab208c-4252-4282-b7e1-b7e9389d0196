{"train": [{"input": [[5, 5, 5, 5, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 0, 5, 5, 5, 0, 5, 5, 5, 5], [5, 5, 5, 5, 0, 0, 5, 5, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5], [5, 0, 5, 5, 0, 0, 5, 5, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5]], "output": [[5, 5, 5, 5, 5, 3, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 1, 5, 5, 5, 3, 5, 5, 5, 5], [5, 5, 5, 5, 2, 3, 5, 5, 5, 5], [5, 1, 5, 5, 2, 5, 5, 5, 5, 5], [5, 1, 5, 5, 2, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 4, 5], [5, 1, 5, 5, 2, 3, 5, 5, 4, 5], [5, 5, 5, 5, 5, 5, 5, 5, 4, 5]]}, {"input": [[0, 5, 5, 5, 5, 5, 5, 0, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 0, 5, 5], [5, 5, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 0, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 0, 5], [5, 5, 5, 5, 0, 5, 5, 5, 5, 5]], "output": [[1, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 2, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 5, 2, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 2, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 4, 5], [5, 5, 5, 5, 2, 5, 5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 0, 0, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 0, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 1, 2, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5, 4], [5, 5, 5, 5, 5, 5, 5, 5, 3, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 2, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 4], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}], "test": [{"input": [[5, 5, 5, 0, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 0, 5, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 5, 5, 5, 5, 0, 5, 5, 5, 5]], "output": [[5, 5, 5, 3, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 4, 5, 5, 5, 5], [5, 5, 2, 5, 5, 5, 5, 5, 5, 5], [5, 5, 2, 5, 5, 5, 5, 5, 5, 5], [1, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 5, 5, 5, 5, 4, 5, 5, 5, 5]]}]}