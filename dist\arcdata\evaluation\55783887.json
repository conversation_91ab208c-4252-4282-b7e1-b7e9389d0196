{"train": [{"input": [[4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 1, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 1, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 1, 4, 4, 4, 4, 4, 4, 4], [4, 4, 1, 4, 4, 4, 4, 4, 4], [4, 4, 4, 1, 4, 4, 4, 4, 4], [4, 4, 4, 4, 1, 4, 4, 4, 4], [4, 4, 4, 4, 4, 1, 4, 4, 4], [4, 4, 4, 4, 4, 4, 1, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 6, 8, 1, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 6, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 6, 8, 8, 8, 8], [8, 1, 8, 8, 8, 8, 8, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8]]}, {"input": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 4], [4, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 1, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 1, 4, 4, 4, 4, 6, 4, 4, 4, 4], [4, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5], [5, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 1, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5], [5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5, 6, 5, 5, 6, 5, 5], [5, 1, 5, 5, 5, 6, 5, 5, 5, 5, 5, 6], [5, 5, 1, 5, 6, 5, 5, 5, 5, 5, 6, 5], [5, 5, 5, 6, 5, 5, 5, 5, 5, 6, 5, 5], [5, 5, 6, 5, 1, 5, 5, 5, 6, 5, 5, 5], [5, 6, 5, 5, 5, 1, 5, 6, 5, 5, 5, 5], [6, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5], [5, 5, 5, 5, 5, 6, 5, 1, 5, 5, 5, 5], [5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5], [5, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 6, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5], [6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 6], [8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 6, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8], [8, 6, 8, 8, 8, 8, 8, 8, 6, 8, 1, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 1, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 1, 6, 8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8]]}], "test": [{"input": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 6, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[4, 4, 4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 6, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 6, 4, 4, 4, 4], [4, 4, 1, 4, 4, 4, 6, 4, 4, 4, 6, 4, 4, 4, 4, 4], [4, 4, 4, 1, 4, 6, 4, 4, 4, 6, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 6, 4, 4, 4, 6, 4, 4, 4, 4, 4, 1, 4], [4, 4, 4, 6, 4, 1, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 6, 4, 4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 6, 4, 4, 4, 6, 4, 1, 4, 4, 4, 4, 4, 4, 6, 4], [6, 4, 4, 4, 6, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 6, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4], [4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4], [4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 6, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}]}