{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 4, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 4, 4, 4, 3, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 0, 0, 0, 3, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 0, 4, 4, 3, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 0, 4, 0, 3, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 4, 0, 4, 0, 4, 0, 3, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 0, 4, 4, 3, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 0, 0, 0, 3, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 4, 4, 4, 4, 3, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 4, 0, 4, 0, 4, 0, 4], [0, 4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 0, 4, 0, 4], [4, 4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 4], [4, 4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0]], "output": [[4, 0, 4, 4, 4, 4, 3, 4, 4, 4], [4, 0, 4, 0, 0, 0, 3, 0, 0, 0], [4, 0, 4, 0, 4, 4, 3, 4, 4, 0], [4, 0, 4, 0, 4, 0, 3, 0, 4, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [4, 0, 4, 0, 4, 0, 3, 0, 4, 0], [4, 0, 4, 0, 4, 4, 3, 4, 4, 0], [4, 0, 4, 0, 0, 0, 3, 0, 0, 0], [4, 0, 4, 4, 4, 4, 3, 4, 4, 4], [4, 0, 0, 0, 0, 0, 3, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0]], "output": [[4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [4, 0, 4, 4, 4, 4, 3, 4, 4, 4, 4, 0], [4, 0, 4, 0, 0, 0, 3, 0, 0, 0, 4, 0], [4, 0, 4, 0, 4, 4, 3, 4, 4, 0, 4, 0], [4, 0, 4, 0, 4, 0, 3, 0, 4, 0, 4, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [4, 0, 4, 0, 4, 0, 3, 0, 4, 0, 4, 0], [4, 0, 4, 0, 4, 4, 3, 4, 4, 0, 4, 0], [4, 0, 4, 0, 0, 0, 3, 0, 0, 0, 4, 0]]}]}