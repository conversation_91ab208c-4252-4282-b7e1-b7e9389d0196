import React from 'react';
import { Box, Typography, Stack, Paper } from '@mui/material';
import { ARCGrid, ValidationResult } from '../../types';
import ARCGridDisplay from '../ARCGridDisplay/ARCGridDisplay';

interface ARCGridComparisonProps {
  proposed: ARCGrid;
  expected: ARCGrid;
  validationResult: ValidationResult;
}

const ARCGridComparison: React.FC<ARCGridComparisonProps> = ({
  proposed,
  expected,
  validationResult,
}) => {
  const renderErrorOverlay = (errorGrid: boolean[][], width: number, height: number) => {
    const cellSize = 20; // Même taille que ARCGridDisplay
    
    return (
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: width * cellSize,
          height: height * cellSize,
          pointerEvents: 'none',
        }}
      >
        {errorGrid.map((row, y) =>
          row.map((isCorrect, x) => (
            <Box
              key={`${x}-${y}`}
              sx={{
                position: 'absolute',
                left: x * cellSize,
                top: y * cellSize,
                width: cellSize,
                height: cellSize,
                backgroundColor: isCorrect ? 'transparent' : 'rgba(255, 0, 0, 0.3)',
                border: isCorrect ? 'none' : '2px solid red',
                boxSizing: 'border-box',
              }}
            />
          ))
        )}
      </Box>
    );
  };

  return (
    <Paper sx={{ p: 2, mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        Comparaison des grilles
      </Typography>
      
      <Stack direction="row" spacing={3} alignItems="flex-start">
        {/* Grille proposée */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Solution proposée
          </Typography>
          <Box sx={{ position: 'relative', display: 'inline-block' }}>
            <ARCGridDisplay grid={proposed} />
            {validationResult.errorGrid && (
              renderErrorOverlay(validationResult.errorGrid, proposed.width, proposed.height)
            )}
          </Box>
        </Box>

        {/* Grille attendue */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Solution attendue
          </Typography>
          <ARCGridDisplay grid={expected} />
        </Box>

        {/* Statistiques */}
        <Box sx={{ minWidth: 200 }}>
          <Typography variant="subtitle2" gutterBottom>
            Statistiques
          </Typography>
          <Stack spacing={1}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Précision
              </Typography>
              <Typography variant="h6" color={validationResult.isValid ? 'success.main' : 'error.main'}>
                {(validationResult.accuracy * 100).toFixed(1)}%
              </Typography>
            </Box>
            
            <Box>
              <Typography variant="body2" color="text.secondary">
                Cellules correctes
              </Typography>
              <Typography variant="body1">
                {Math.round(validationResult.accuracy * proposed.width * proposed.height)} / {proposed.width * proposed.height}
              </Typography>
            </Box>

            <Box>
              <Typography variant="body2" color="text.secondary">
                Dimensions
              </Typography>
              <Typography variant="body1">
                {proposed.width} × {proposed.height}
              </Typography>
            </Box>

            {validationResult.feedback && (
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Détails
                </Typography>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                  {validationResult.feedback}
                </Typography>
              </Box>
            )}
          </Stack>
        </Box>
      </Stack>
    </Paper>
  );
};

export default ARCGridComparison;
