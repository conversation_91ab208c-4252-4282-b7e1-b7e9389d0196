# 🎲 Niveau 1 - Objets sur Table

## 📋 Vue d'Ensemble

Ce domaine analyse les grilles ARC AGI selon l'approche **"table vue du dessus"** : objets manipulables posés sur une surface, avec détection des points d'ancrage possibles.

## 🎯 Principe Fondamental

```
Grille ARC AGI = Table vue du dessus
    ↓
Fond (couleur dominante) = Surface de la table
    ↓
Couleurs non-fond = Objets posés sur la table
    ↓
Points rouges (couleur 2) = Points d'ancrage possibles
```

## 📊 Structure de Données

```python
objects_analysis = {
    # Analyse de la table
    'table_analysis': {
        'probable_background': int,         # Couleur de fond détectée
        'background_confidence': float,     # Confiance dans la détection (0-1)
        'background_detection_method': str, # 'most_frequent', 'edge_analysis', 'manual'
        'non_background_colors': List[int], # Couleurs d'objets potentiels
        'table_coverage': float,            # Ratio de la table couverte par le fond
        'noise_pixels': int,                # Pixels isolés (bruit potentiel)
        'table_dimensions': (int, int)      # Dimensions de la table
    },
    
    # Objets détectés
    'detected_objects': [
        {
            'object_id': str,               # ID unique 'obj_color_shape_n'
            'color': int,                   # Couleur principale
            'multi_color': bool,            # Objet multi-couleur
            'color_composition': Dict[int, int], # {couleur: nb_pixels} si multi-couleur
            
            # Propriétés géométriques
            'area': int,                    # Nombre de pixels
            'center': [float, float],       # Centre de masse [ligne, colonne]
            'bbox': [int, int, int, int],   # Boîte englobante [top, left, bottom, right]
            'width': int,                   # Largeur boîte englobante
            'height': int,                  # Hauteur boîte englobante
            'aspect_ratio': float,          # width/height
            'compactness': float,           # 4π*area/perimeter²
            'perimeter': int,               # Périmètre de l'objet
            
            # Classification de forme
            'shape_classification': {
                'basic_shape': str,         # 'square', 'rectangle', 'line', 'L_shape', 'T_shape', 'cross', 'dot', 'irregular'
                'shape_confidence': float,  # Confiance dans la classification
                'shape_details': {
                    'is_solid': bool,       # Objet plein
                    'is_hollow': bool,      # Objet creux
                    'has_holes': bool,      # Objet avec trous
                    'connectivity': int     # Nombre de composantes connexes
                },
                'symmetries': {
                    'horizontal': bool,
                    'vertical': bool,
                    'diagonal_main': bool,
                    'diagonal_anti': bool,
                    'rotational_4': bool,   # Symétrie rotation 90°
                    'rotational_2': bool    # Symétrie rotation 180°
                }
            },
            
            # Représentation pour manipulation
            'object_matrix': np.ndarray,    # Matrice de l'objet extrait (fond = 0)
            'object_mask': np.ndarray,      # Masque binaire de l'objet
            'object_hash': str,             # Hash unique du motif
            'normalized_hash': str,         # Hash normalisé (position-indépendant)
            
            # Variants pour comparaisons futures (Niveau 2)
            'transformation_variants': {
                'rotations': {
                    'rot_90': np.ndarray,   # Objet tourné 90°
                    'rot_180': np.ndarray,  # Objet tourné 180°
                    'rot_270': np.ndarray   # Objet tourné 270°
                },
                'flips': {
                    'flip_horizontal': np.ndarray, # Objet retourné horizontalement
                    'flip_vertical': np.ndarray    # Objet retourné verticalement
                },
                'variant_hashes': {
                    'original': str,
                    'rot_90': str,
                    'rot_180': str,
                    'rot_270': str,
                    'flip_h': str,
                    'flip_v': str
                }
            },
            
            # Position sur la table
            'table_position': {
                'absolute_position': [int, int], # Position absolue [ligne, colonne]
                'relative_position': str,        # 'top_left', 'center', 'bottom_right', etc.
                'distance_to_edges': {
                    'top': int, 'bottom': int, 'left': int, 'right': int
                },
                'touching_edges': List[str],     # Bords de la table touchés
                'corner_proximity': str or None, # 'top_left', 'top_right', etc.
                'is_edge_object': bool,          # Objet en bord de table
                'is_corner_object': bool         # Objet dans un coin
            }
        }
    ],
    
    # Relations entre objets
    'object_relationships': {
        'spatial_relationships': [
            {
                'object1': str,
                'object2': str,
                'relationship': str,        # 'adjacent', 'overlapping', 'separated', 'aligned'
                'distance': float,          # Distance entre centres
                'relative_position': str,   # 'above', 'below', 'left', 'right', 'diagonal'
                'alignment': {
                    'horizontal': bool,     # Alignés horizontalement
                    'vertical': bool,       # Alignés verticalement
                    'diagonal': bool        # Alignés en diagonale
                },
                'gap_size': int or None     # Taille de l'espace entre objets
            }
        ],
        
        'pattern_similarities': [
            {
                'objects': List[str],       # Objets avec patterns similaires
                'similarity_type': str,     # 'identical_hash', 'variant_match', 'partial_match'
                'matching_variants': List[str], # Quels variants matchent
                'similarity_score': float   # Score de similarité (0-1)
            }
        ],
        
        'grouping_analysis': {
            'object_clusters': [            # Groupes d'objets proches
                {
                    'cluster_id': str,
                    'member_objects': List[str],
                    'cluster_center': [float, float],
                    'cluster_radius': float,
                    'cluster_density': float
                }
            ],
            'isolated_objects': List[str],  # Objets isolés
            'alignment_groups': [           # Objets alignés
                {
                    'alignment_type': str,  # 'horizontal', 'vertical', 'diagonal'
                    'aligned_objects': List[str],
                    'alignment_precision': float
                }
            ]
        }
    },
    
    # Points d'ancrage et zones spéciales
    'anchor_analysis': {
        'possible_anchor_points': [         # Points rouges (couleur 2) = ancrages possibles
            {
                'anchor_id': str,           # ID unique de l'ancrage
                'position': [int, int],     # Position [ligne, colonne]
                'bounds': [int, int, int, int], # [top, left, bottom, right] si zone
                'size': int,                # Nombre de pixels rouges connexes
                'shape': str,               # 'single_pixel', 'small_cluster', 'line', 'cross'
                'anchor_properties': {
                    'is_isolated': bool,    # Ancrage isolé
                    'is_corner_anchor': bool, # Ancrage dans un coin
                    'is_edge_anchor': bool, # Ancrage sur un bord
                    'is_center_anchor': bool # Ancrage au centre
                },
                'nearby_objects': List[str], # Objets proches de ce point
                'distance_to_nearest_object': float,
                'location_type': str        # 'corner', 'edge', 'center', 'isolated'
            }
        ],
        
        'empty_spaces': [                   # Zones vides détectées
            {
                'space_id': str,
                'position': [int, int],     # Centre de l'espace vide
                'size': int,                # Taille de la zone vide
                'dimensions': [int, int],   # Largeur, hauteur de l'espace
                'space_type': str,          # 'near_anchor', 'isolated', 'between_objects'
                'accessibility': str        # 'open', 'enclosed', 'semi_enclosed'
            }
        ],
        
        'anchor_object_associations': [     # Associations possibles ancrage-objet
            {
                'anchor_id': str,
                'nearby_object_ids': List[str],
                'distances': List[float],
                'potential_matches': List[Dict] # Objets qui pourraient s'ancrer ici
            }
        ]
    },
    
    # Statistiques globales
    'object_statistics': {
        'total_objects': int,
        'objects_by_color': Dict[int, int], # {couleur: nombre_objets}
        'objects_by_size': Dict[str, int],  # {'small': n, 'medium': n, 'large': n}
        'objects_by_shape': Dict[str, int], # {'square': n, 'line': n, etc.}
        'size_distribution': {
            'min_size': int,
            'max_size': int,
            'mean_size': float,
            'size_variance': float
        },
        'spatial_distribution': {
            'table_occupancy': float,       # Ratio de la table occupée
            'object_density': float,        # Objets par unité de surface
            'clustering_coefficient': float, # Mesure de regroupement
            'dispersion_index': float       # Mesure de dispersion
        },
        'anchor_statistics': {
            'total_anchors': int,
            'anchors_by_type': Dict[str, int],
            'anchor_coverage': float,       # Ratio de la table avec ancrages
            'anchor_object_ratio': float    # Ratio ancrages/objets
        }
    }
}
```

## 🔍 Algorithmes de Détection

### **Détection du Fond**
```python
def detect_background_color(grid_array, value_frequencies):
    """Détecte la couleur de fond de la table"""
    
    # Méthode 1: Couleur la plus fréquente
    most_frequent = max(value_frequencies, key=value_frequencies.get)
    
    # Méthode 2: Analyse des bords
    edges = np.concatenate([
        grid_array[0, :],    # Bord haut
        grid_array[-1, :],   # Bord bas
        grid_array[:, 0],    # Bord gauche
        grid_array[:, -1]    # Bord droit
    ])
    edge_frequencies = {color: np.sum(edges == color) for color in np.unique(edges)}
    edge_dominant = max(edge_frequencies, key=edge_frequencies.get)
    
    # Confiance basée sur la cohérence
    if most_frequent == edge_dominant:
        return most_frequent, 0.9
    else:
        return most_frequent, 0.6
```

### **Extraction d'Objets**
```python
def extract_objects(grid_array, background_color):
    """Extrait tous les objets non-fond"""
    
    # Masque des pixels non-fond
    object_mask = grid_array != background_color
    
    # Détection des composantes connexes
    from scipy.ndimage import label
    labeled_array, num_objects = label(object_mask)
    
    objects = []
    for obj_id in range(1, num_objects + 1):
        obj_mask = labeled_array == obj_id
        obj_pixels = np.where(obj_mask)
        
        # Extraire propriétés de l'objet
        obj_data = extract_object_properties(grid_array, obj_mask, obj_pixels)
        objects.append(obj_data)
    
    return objects
```

### **Détection des Points d'Ancrage**
```python
def detect_anchor_points(grid_array, anchor_color=2):
    """Détecte les points d'ancrage rouges (couleur 2)"""
    
    anchor_mask = grid_array == anchor_color
    if not np.any(anchor_mask):
        return []
    
    # Détection des composantes connexes d'ancrages
    from scipy.ndimage import label
    labeled_anchors, num_anchors = label(anchor_mask)
    
    anchors = []
    for anchor_id in range(1, num_anchors + 1):
        anchor_pixels = np.where(labeled_anchors == anchor_id)
        
        anchor_data = {
            'anchor_id': f'anchor_{anchor_id}',
            'position': [int(np.mean(anchor_pixels[0])), int(np.mean(anchor_pixels[1]))],
            'size': len(anchor_pixels[0]),
            'shape': classify_anchor_shape(anchor_pixels),
            'bounds': [
                int(np.min(anchor_pixels[0])), int(np.min(anchor_pixels[1])),
                int(np.max(anchor_pixels[0])), int(np.max(anchor_pixels[1]))
            ]
        }
        anchors.append(anchor_data)
    
    return anchors
```

## 🎯 Cas d'Usage Spécifiques

### **1. Objets sur Fond Noir**
- Fond noir (couleur 0) détecté automatiquement
- Objets colorés extraits et analysés
- Points rouges identifiés comme ancrages possibles

### **2. Objets sur Fond Coloré**
- Détection automatique du fond par fréquence
- Adaptation de l'extraction d'objets
- Analyse des relations spatiales

### **3. Objets Multi-Couleurs**
- Détection des objets composés de plusieurs couleurs
- Analyse de la composition colorimétrique
- Préservation de la structure interne

### **4. Points d'Ancrage Complexes**
- Détection de zones rouges étendues
- Classification de la forme des ancrages
- Association avec objets proches

## 🛡️ Règles de Pureté

### **Interdictions**
- ❌ Pas de prédiction de mouvement d'objets
- ❌ Pas de confirmation d'ancrage (rôle du Niveau 2)
- ❌ Pas d'interprétation des transformations

### **Autorisations**
- ✅ Détection factuelle des objets et ancrages
- ✅ Calcul des propriétés géométriques
- ✅ Précalcul des variants pour comparaisons futures
- ✅ Analyse des relations spatiales actuelles

## 🔗 Interface avec Autres Domaines

### **Utilisation des Résultats**
- **Niveau 2** : Compare les objets input/output pour détecter transformations
- **Domaine Colors** : Peut utiliser `probable_background` pour ses analyses
- **Domaine Spatial** : Peut utiliser `object_statistics` pour propriétés globales

---

**Ce domaine fournit une analyse complète des objets manipulables dans l'approche "table vue du dessus", essentielle pour de nombreux puzzles ARC AGI.**