{"train": [{"input": [[0, 0, 0, 0, 3], [0, 8, 8, 0, 3], [0, 8, 8, 0, 3], [0, 0, 0, 0, 3], [3, 3, 3, 3, 3]], "output": [[2, 0, 0, 0, 0, 0, 0, 2, 3, 3], [0, 2, 0, 0, 0, 0, 2, 0, 3, 3], [0, 0, 8, 8, 8, 8, 0, 0, 3, 3], [0, 0, 8, 8, 8, 8, 0, 0, 3, 3], [0, 0, 8, 8, 8, 8, 0, 0, 3, 3], [0, 0, 8, 8, 8, 8, 0, 0, 3, 3], [0, 2, 0, 0, 0, 0, 2, 0, 3, 3], [2, 0, 0, 0, 0, 0, 0, 2, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 7], [4, 4, 0, 0, 7], [4, 4, 0, 0, 6], [0, 0, 0, 0, 6], [7, 7, 6, 6, 6]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 7, 7, 7], [0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 7, 7, 7], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 7, 7, 7], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 7, 7, 7], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 7, 7, 7], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 6, 6, 6], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 6, 6, 6], [4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 6, 6, 6], [0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 6, 6, 6], [7, 7, 7, 7, 7, 7, 6, 6, 6, 6, 6, 6, 6, 6, 6], [7, 7, 7, 7, 7, 7, 6, 6, 6, 6, 6, 6, 6, 6, 6], [7, 7, 7, 7, 7, 7, 6, 6, 6, 6, 6, 6, 6, 6, 6]]}, {"input": [[0, 0, 0, 0, 9], [0, 1, 1, 0, 9], [0, 1, 1, 0, 3], [0, 0, 0, 0, 3], [9, 9, 3, 3, 4]], "output": [[2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9, 9, 9, 9], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 9, 9, 9, 9], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 9, 9, 9, 9], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 9, 9, 9, 9], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 9, 9, 9, 9], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 9, 9, 9, 9], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 9, 9, 9, 9], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 9, 9, 9, 9], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 3, 3, 3, 3], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 3, 3, 3, 3], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 3, 3, 3, 3], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 3, 3, 3, 3], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 3, 3, 3], [9, 9, 9, 9, 9, 9, 9, 9, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4], [9, 9, 9, 9, 9, 9, 9, 9, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4], [9, 9, 9, 9, 9, 9, 9, 9, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4], [9, 9, 9, 9, 9, 9, 9, 9, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4]]}], "test": [{"input": [[0, 6, 6, 0, 8], [0, 6, 6, 0, 8], [0, 0, 0, 0, 1], [0, 0, 0, 0, 7], [8, 8, 1, 7, 9]], "output": [[0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8], [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 1, 1, 1, 1, 1], [0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 1, 1, 1, 1, 1], [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 1, 1, 1, 1, 1], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 1, 1, 1, 1], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7, 9, 9, 9, 9, 9]]}]}