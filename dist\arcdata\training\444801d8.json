{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 0, 1, 1, 0, 0, 0, 0], [0, 1, 0, 0, 0, 1, 0, 0, 0, 0], [0, 1, 0, 2, 0, 1, 0, 0, 0, 0], [0, 1, 0, 0, 0, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 2, 2, 2, 2, 2, 0, 0, 0, 0], [0, 1, 1, 2, 1, 1, 0, 0, 0, 0], [0, 1, 2, 2, 2, 1, 0, 0, 0, 0], [0, 1, 2, 2, 2, 1, 0, 0, 0, 0], [0, 1, 2, 2, 2, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 0, 1, 1, 0, 0, 0, 0], [0, 1, 0, 2, 0, 1, 0, 0, 0, 0], [0, 1, 0, 0, 0, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 1, 1, 0], [0, 0, 0, 0, 1, 0, 3, 0, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 0, 0, 0, 0], [0, 1, 1, 2, 1, 1, 0, 0, 0, 0], [0, 1, 2, 2, 2, 1, 0, 0, 0, 0], [0, 1, 2, 2, 2, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 3, 3, 3, 3, 3, 0], [0, 0, 0, 0, 1, 1, 3, 1, 1, 0], [0, 0, 0, 0, 1, 3, 3, 3, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 0, 1, 1, 0, 0, 0, 0], [0, 1, 0, 6, 0, 1, 0, 0, 0, 0], [0, 1, 0, 0, 0, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 1, 1, 0], [0, 0, 0, 0, 1, 0, 8, 0, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]], "output": [[0, 6, 6, 6, 6, 6, 0, 0, 0, 0], [0, 1, 1, 6, 1, 1, 0, 0, 0, 0], [0, 1, 6, 6, 6, 1, 0, 0, 0, 0], [0, 1, 6, 6, 6, 1, 0, 0, 0, 0], [0, 1, 1, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 8, 8, 8, 0], [0, 0, 0, 0, 1, 1, 8, 1, 1, 0], [0, 0, 0, 0, 1, 8, 8, 8, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 0, 1, 1, 0, 0, 0, 0, 0], [1, 0, 4, 0, 1, 0, 0, 0, 0, 0], [1, 0, 0, 0, 1, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 1, 1, 0], [0, 0, 0, 0, 1, 0, 7, 0, 1, 0], [0, 0, 0, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]], "output": [[4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [1, 1, 4, 1, 1, 0, 0, 0, 0, 0], [1, 4, 4, 4, 1, 0, 0, 0, 0, 0], [1, 4, 4, 4, 1, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 7, 7, 7, 7, 0], [0, 0, 0, 0, 1, 1, 7, 1, 1, 0], [0, 0, 0, 0, 1, 7, 7, 7, 1, 0], [0, 0, 0, 0, 1, 7, 7, 7, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 0]]}]}