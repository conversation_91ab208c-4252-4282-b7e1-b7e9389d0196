{"train": [{"input": [[8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 0, 0, 0, 8, 8, 8, 8, 0, 8, 8], [8, 0, 0, 8, 0, 8, 0, 8, 8, 8, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 8], [8, 8, 8, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 8, 8, 0, 8, 8, 8, 8], [8, 8, 0, 8, 8, 8, 8, 0, 8, 8, 0, 0, 0, 0, 8, 8, 0, 0, 0, 8, 8], [8, 8, 8, 8, 0, 8, 8, 0, 8, 8, 0, 0, 0, 0, 8, 8, 8, 0, 8, 8, 8], [0, 0, 0, 8, 8, 0, 8, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0], [8, 8, 8, 8, 0, 0, 8, 0, 8, 0, 0, 0, 0, 0, 8, 8, 8, 0, 8, 8, 8], [8, 0, 0, 8, 0, 0, 8, 8, 0, 8, 0, 0, 0, 0, 8, 0, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 0, 8, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 0, 0, 2, 2, 2, 2, 0, 0, 0, 0, 8, 8, 0, 8, 8, 0, 8], [2, 0, 2, 2, 2, 0, 0, 2, 2, 2, 0, 0, 0, 0, 8, 8, 8, 8, 0, 8, 0], [0, 2, 2, 2, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 8], [2, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 8, 8, 0, 8, 8, 8, 0], [2, 2, 2, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 0, 0], [2, 2, 2, 2, 2, 0, 2, 0, 2, 2, 0, 0, 0, 0, 8, 0, 8, 0, 8, 8, 8], [2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 8, 0, 0, 8], [0, 2, 2, 0, 0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 8, 0, 0, 0, 8, 8, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 8, 8, 0, 0, 8, 8], [2, 0, 2, 2, 0, 2, 2, 2, 2, 2, 0, 0, 0, 0, 8, 8, 8, 0, 8, 8, 8]], "output": [[0, 2, 2, 2, 0, 0, 2, 2, 2, 2], [2, 0, 2, 2, 2, 0, 0, 2, 2, 2], [0, 2, 2, 2, 2, 2, 2, 0, 2, 0], [2, 2, 2, 2, 0, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 0, 2, 0, 0], [2, 2, 2, 2, 2, 0, 2, 0, 2, 2], [2, 2, 0, 2, 2, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 2, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 2, 2, 0, 2, 2, 2, 2, 2]]}, {"input": [[2, 0, 2, 2, 2, 2, 0, 0, 0, 0, 2, 0, 2, 2, 2, 2, 0, 0, 2], [2, 2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 2, 2, 2, 2, 2, 0, 0, 0], [0, 0, 2, 2, 0, 2, 0, 0, 0, 0, 2, 2, 2, 0, 2, 2, 2, 2, 2], [2, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 2, 2, 0, 2, 2, 2], [2, 2, 2, 0, 2, 0, 2, 0, 0, 0, 2, 0, 2, 2, 2, 2, 0, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 3, 3, 3, 3, 3, 0, 3, 3], [0, 2, 2, 0, 0, 2, 2, 0, 0, 0, 3, 3, 3, 0, 0, 0, 3, 3, 0], [0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 3, 3, 3, 0, 3, 0, 3, 0, 0], [2, 2, 2, 0, 0, 2, 2, 0, 0, 0, 3, 3, 0, 0, 0, 3, 3, 3, 3], [2, 0, 0, 2, 2, 2, 0, 0, 0, 0, 3, 0, 0, 0, 3, 0, 3, 0, 3], [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 3, 3, 0, 3, 3, 3, 0, 3], [0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 3, 3, 0, 0, 3, 0, 3, 0]], "output": [[0, 3, 3, 3, 3, 3, 0, 3, 3], [3, 3, 3, 0, 0, 0, 3, 3, 0], [3, 3, 3, 0, 3, 0, 3, 0, 0], [3, 3, 0, 0, 0, 3, 3, 3, 3], [3, 0, 0, 0, 3, 0, 3, 0, 3], [0, 3, 3, 0, 3, 3, 3, 0, 3], [0, 3, 3, 0, 0, 3, 0, 3, 0]]}, {"input": [[0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0], [1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1], [0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0], [1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1], [0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 4, 0, 4, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1], [4, 4, 4, 4, 0, 4, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0], [4, 0, 4, 0, 0, 4, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1], [0, 4, 4, 4, 4, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1], [4, 4, 4, 0, 4, 4, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1], [0, 4, 4, 4, 0, 4, 0, 0, 0, 1, 0, 1, 0, 1, 1, 1, 0], [0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1], [4, 4, 0, 4, 0, 4, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0]], "output": [[4, 0, 0, 4, 0, 4], [4, 4, 4, 4, 0, 4], [4, 0, 4, 0, 0, 4], [0, 4, 4, 4, 4, 0], [4, 4, 4, 0, 4, 4], [0, 4, 4, 4, 4, 0], [0, 4, 4, 4, 0, 4], [0, 4, 0, 0, 0, 0], [4, 4, 0, 4, 0, 4]]}], "test": [{"input": [[1, 1, 1, 1, 0, 1, 0, 0, 3, 0, 3, 3, 3, 3, 3, 3, 0], [1, 0, 1, 0, 1, 1, 0, 0, 0, 3, 0, 3, 3, 3, 0, 0, 0], [1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 0, 0], [0, 0, 0, 1, 1, 1, 0, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0], [1, 1, 1, 1, 1, 1, 0, 0, 0, 3, 0, 3, 3, 3, 0, 3, 3], [1, 1, 1, 1, 1, 1, 0, 0, 3, 3, 0, 0, 0, 3, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 3, 0, 0, 3, 3, 3, 0, 3, 0, 3, 0, 3], [0, 3, 3, 0, 0, 3, 0, 0, 0, 3, 0, 3, 3, 3, 0, 0, 0], [3, 3, 3, 3, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0, 0, 0, 3], [3, 0, 3, 0, 3, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0, 3], [0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 3, 3, 0]], "output": [[1, 1, 1, 1, 0, 1], [1, 0, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0], [0, 0, 0, 1, 1, 1], [1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1]]}]}