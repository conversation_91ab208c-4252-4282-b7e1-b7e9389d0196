{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0], [0, 3, 2, 2, 2, 3, 0, 0, 0, 0, 8], [0, 3, 2, 2, 2, 3, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3], [0, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 6, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 6, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 6, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 8, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 1, 6, 6, 6, 6, 6, 6, 6, 1, 0], [0, 0, 1, 6, 6, 6, 6, 6, 6, 6, 1, 0], [0, 0, 1, 6, 6, 6, 6, 6, 6, 6, 1, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 4, 4, 4, 6, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 0, 0, 0, 0, 0]]}]}