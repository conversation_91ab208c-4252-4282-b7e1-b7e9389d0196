{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 8, 0, 0, 0, 7, 0, 0], [0, 0, 8, 0, 0, 0, 7, 0, 0], [8, 8, 8, 8, 8, 8, 2, 8, 8], [0, 0, 8, 0, 0, 0, 7, 0, 0], [0, 0, 8, 0, 0, 0, 7, 0, 0], [0, 0, 8, 0, 0, 0, 7, 0, 0], [7, 7, 2, 7, 7, 7, 7, 7, 7], [0, 0, 8, 0, 0, 0, 7, 0, 0], [0, 0, 8, 0, 0, 0, 7, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 8, 0, 0, 7, 0, 0], [8, 8, 8, 8, 8, 8, 2, 8, 8], [0, 0, 0, 8, 0, 0, 7, 0, 0], [0, 0, 0, 8, 0, 0, 7, 0, 0], [0, 0, 0, 8, 0, 0, 7, 0, 0], [0, 0, 0, 8, 0, 0, 7, 0, 0], [0, 0, 0, 8, 0, 0, 7, 0, 0], [7, 7, 7, 2, 7, 7, 7, 7, 7], [0, 0, 0, 8, 0, 0, 7, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 7, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 7, 0, 0, 8, 0, 0, 0, 0], [8, 2, 8, 8, 8, 8, 8, 8, 8], [0, 7, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 8, 0, 0, 0, 0], [7, 7, 7, 7, 2, 7, 7, 7, 7], [0, 7, 0, 0, 8, 0, 0, 0, 0], [0, 7, 0, 0, 8, 0, 0, 0, 0]]}]}