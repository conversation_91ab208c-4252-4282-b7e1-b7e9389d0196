import React from 'react';
import {
  Stack,
  Typography,
  Alert,
  Box,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Compare as CompareIcon,
  ExpandMore as ExpandMoreIcon,
  Transform as TransformIcon,
  GridOn as GridOnIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { useExpandedSections } from '../hooks/useExpandedSections';
import { Level2PanelProps } from '../types/AnalysisPanelTypes';

export const Level2Panel: React.FC<Level2PanelProps> = ({ analysis, showValues }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections();

  if (!analysis.level_2) {
    return (
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={true}
      />

      {analysis.level_2.training_examples_comparisons ? (
        <Stack spacing={2}>
          {analysis.level_2.training_examples_comparisons.map((comparison: any, index: number) => (
            <Accordion key={comparison.example_id} expanded={expandedSections.includes(`comparison_${index}`)} onChange={handleAccordionChange(`comparison_${index}`)}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">
                  <CompareIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Comparaison Exemple {index + 1}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Stack spacing={2}>
                  {/* Compatibilité Dimensionnelle */}
                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Compatibilité Dimensionnelle
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap">
                      <Chip
                        label={comparison.transformation_analysis.dimension_compatibility.same_dimensions ? 'Mêmes dimensions' : 'Dimensions différentes'}
                        size="small"
                        color={comparison.transformation_analysis.dimension_compatibility.same_dimensions ? 'success' : 'warning'}
                      />
                      {!comparison.transformation_analysis.dimension_compatibility.same_dimensions && (
                        <>
                          <Chip
                            label={`Δ largeur: ${comparison.transformation_analysis.dimension_compatibility.width_change > 0 ? '+' : ''}${comparison.transformation_analysis.dimension_compatibility.width_change}`}
                            size="small"
                            color={comparison.transformation_analysis.dimension_compatibility.width_change === 0 ? 'success' : 'info'}
                          />
                          <Chip
                            label={`Δ hauteur: ${comparison.transformation_analysis.dimension_compatibility.height_change > 0 ? '+' : ''}${comparison.transformation_analysis.dimension_compatibility.height_change}`}
                            size="small"
                            color={comparison.transformation_analysis.dimension_compatibility.height_change === 0 ? 'success' : 'info'}
                          />
                        </>
                      )}
                    </Stack>
                  </Box>

                  {/* Grille de Différences */}
                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Grille de Différences
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap">
                      <Chip
                        label={comparison.transformation_analysis.diff_grid.exists ? 'Grille diff disponible' : 'Pas de grille diff'}
                        size="small"
                        color={comparison.transformation_analysis.diff_grid.exists ? 'success' : 'default'}
                      />
                      {comparison.transformation_analysis.diff_grid.exists && comparison.transformation_analysis.diff_grid.change_ratio !== null && (
                        <Chip
                          label={`Changements: ${(comparison.transformation_analysis.diff_grid.change_ratio * 100).toFixed(1)}%`}
                          size="small"
                          color={
                            comparison.transformation_analysis.diff_grid.change_ratio > 0.5 ? 'error' :
                              comparison.transformation_analysis.diff_grid.change_ratio > 0.2 ? 'warning' : 'success'
                          }
                        />
                      )}
                      {comparison.transformation_analysis.diff_grid.change_positions && (
                        <Chip
                          label={`Positions modifiées: ${comparison.transformation_analysis.diff_grid.change_positions.length}`}
                          size="small"
                          color="info"
                        />
                      )}
                    </Stack>
                  </Box>

                  {/* Analyse de la Grille Diff */}
                  {comparison.transformation_analysis.diff_grid.diff_analysis && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Analyse de la Grille Diff
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        La grille diff a été analysée avec la même structure que les grilles input/output.
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
                        <Chip
                          label={`${comparison.transformation_analysis.diff_grid.diff_analysis.colors.present_colors.length} couleurs détectées`}
                          size="small"
                          color="info"
                        />
                        <Chip
                          label={`${comparison.transformation_analysis.diff_grid.diff_analysis.objects.detected_objects.length} objets détectés`}
                          size="small"
                          color="primary"
                        />
                      </Stack>
                    </Box>
                  )}
                </Stack>
              </AccordionDetails>
            </Accordion>
          ))}
        </Stack>
      ) : (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Aucune comparaison disponible pour ce puzzle.
          </Typography>
        </Alert>
      )}
    </Stack>
  );
};