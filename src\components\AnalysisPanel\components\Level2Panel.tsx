import React from 'react';
import { Stack, Typography, Alert } from '@mui/material';
import { Compare as CompareIcon } from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { Level2PanelProps } from '../types/AnalysisPanelTypes';

export const Level2Panel: React.FC<Level2PanelProps> = ({ analysis }) => {
  if (!analysis.level_2) {
    return (
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={true}
      />

      {analysis.level_2.training_examples_comparisons ? (
        <Stack spacing={2}>
          {analysis.level_2.training_examples_comparisons.map((comparison: any, index: number) => (
            <Alert key={comparison.example_id} severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Exemple {index + 1} : Comparaisons disponibles (implémentation détaillée à venir)
              </Typography>
            </Alert>
          ))}
        </Stack>
      ) : (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Aucune comparaison disponible pour ce puzzle.
          </Typography>
        </Alert>
      )}
    </Stack>
  );
};