{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 2, 5, 2, 0, 2, 5, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 2, 0, 2, 5, 2, 0, 2, 5, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 3, 5, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 5, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 4, 5, 4, 0, 4, 5], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 0, 4, 0], [0, 0, 0, 0, 4, 0, 4, 5, 4, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 1, 5, 1, 0, 1, 5, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 1, 0, 1, 5, 1, 0, 1, 5]]}]}