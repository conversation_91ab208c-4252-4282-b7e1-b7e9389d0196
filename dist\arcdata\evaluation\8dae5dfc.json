{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8, 0, 2, 2, 2, 2, 2, 2], [0, 0, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8, 0, 2, 1, 1, 1, 1, 2], [0, 0, 8, 7, 7, 4, 4, 4, 4, 7, 7, 8, 0, 2, 1, 3, 3, 1, 2], [0, 0, 8, 7, 7, 4, 3, 3, 4, 7, 7, 8, 0, 2, 1, 3, 3, 1, 2], [0, 0, 8, 7, 7, 4, 3, 3, 4, 7, 7, 8, 0, 2, 1, 1, 1, 1, 2], [0, 0, 8, 7, 7, 4, 3, 3, 4, 7, 7, 8, 0, 2, 2, 2, 2, 2, 2], [0, 0, 8, 7, 7, 4, 3, 3, 4, 7, 7, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 7, 7, 4, 3, 3, 4, 7, 7, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 7, 7, 4, 4, 4, 4, 7, 7, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 3, 3, 3, 3, 3, 3], [0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 3, 1, 1, 1, 1, 3], [0, 0, 3, 4, 4, 7, 7, 7, 7, 4, 4, 3, 0, 3, 1, 2, 2, 1, 3], [0, 0, 3, 4, 4, 7, 8, 8, 7, 4, 4, 3, 0, 3, 1, 2, 2, 1, 3], [0, 0, 3, 4, 4, 7, 8, 8, 7, 4, 4, 3, 0, 3, 1, 1, 1, 1, 3], [0, 0, 3, 4, 4, 7, 8, 8, 7, 4, 4, 3, 0, 3, 3, 3, 3, 3, 3], [0, 0, 3, 4, 4, 7, 8, 8, 7, 4, 4, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 4, 4, 7, 8, 8, 7, 4, 4, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 4, 4, 7, 7, 7, 7, 4, 4, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0], [0, 0, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 8, 8, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 8, 8, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 2, 2, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 2, 2, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 8, 8, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 8, 8, 8, 8, 8, 8, 3, 1, 1, 0, 0], [0, 0, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 3, 3, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 3, 3, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 1, 1, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 1, 1, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 3, 3, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 3, 3, 3, 3, 3, 3, 8, 2, 2, 0, 0], [0, 0, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 2, 2, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 2, 1, 1, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 2, 1, 1, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0], [0, 0, 0, 0, 6, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 2, 2, 2, 2, 2, 2, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 2, 1, 1, 1, 1, 2, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 2, 1, 1, 1, 1, 2, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 2, 2, 2, 2, 2, 2, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 4, 4, 4, 4, 4, 4, 4, 4, 3, 6, 0], [0, 0, 0, 0, 6, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 0], [0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0]], "output": [[1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 8, 8, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 8, 8, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 4, 4, 4, 4, 4, 4, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 3, 3, 3, 3, 3, 3, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 3, 6, 6, 6, 6, 3, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 3, 6, 6, 6, 6, 3, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 3, 3, 3, 3, 3, 3, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 4, 4, 4, 4, 4, 4, 4, 4, 2, 1, 0], [0, 0, 0, 0, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 0], [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0]]}, {"input": [[0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 6, 6, 6, 6, 6, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 6, 8, 8, 8, 6, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 6, 6, 6, 6, 6, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 1, 8, 8, 8, 8, 8, 8, 1, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 1, 8, 8, 8, 8, 8, 8, 1, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 2, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 6, 6, 6, 6, 6, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 6, 1, 1, 1, 6, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 6, 6, 6, 6, 6, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 4, 4, 4, 4, 4, 4, 4, 4, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 4, 2, 2, 2, 2, 2, 2, 4, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 4, 2, 2, 2, 2, 2, 2, 4, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 4, 4, 4, 4, 4, 4, 4, 4, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 6, 6, 6, 6, 6, 6, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 6, 1, 1, 1, 1, 6, 3, 3, 0, 0, 5, 5, 5, 5, 5, 5], [0, 3, 3, 6, 1, 4, 4, 1, 6, 3, 3, 0, 0, 5, 4, 4, 4, 4, 5], [0, 3, 3, 6, 1, 1, 1, 1, 6, 3, 3, 0, 0, 5, 4, 8, 8, 4, 5], [0, 3, 3, 6, 6, 6, 6, 6, 6, 3, 3, 0, 0, 5, 4, 8, 8, 4, 5], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 5, 4, 4, 4, 4, 5], [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0], [0, 0, 0, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 2, 4, 4, 4, 4, 4, 4, 4, 2, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 2, 4, 3, 3, 3, 3, 3, 4, 2, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 2, 4, 4, 4, 4, 4, 4, 4, 2, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 6, 0, 0, 0], [0, 0, 0, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 0, 0, 0], [0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 1, 1, 1, 1, 1, 1, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 1, 6, 6, 6, 6, 1, 4, 4, 0, 0, 8, 8, 8, 8, 8, 8], [0, 4, 4, 1, 6, 3, 3, 6, 1, 4, 4, 0, 0, 8, 4, 4, 4, 4, 8], [0, 4, 4, 1, 6, 6, 6, 6, 1, 4, 4, 0, 0, 8, 4, 5, 5, 4, 8], [0, 4, 4, 1, 1, 1, 1, 1, 1, 4, 4, 0, 0, 8, 4, 5, 5, 4, 8], [0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 8, 4, 4, 4, 4, 8], [0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 8, 8, 8, 8, 8, 8, 8, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 8, 6, 6, 6, 6, 6, 8, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 8, 8, 8, 8, 8, 8, 8, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 4, 3, 0, 0, 0], [0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0]]}]}