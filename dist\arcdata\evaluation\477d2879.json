{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 8, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0], [0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0], [0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 2, 2, 2, 2, 3, 3, 3, 3, 3], [3, 3, 3, 3, 2, 8, 8, 2, 3, 3, 3, 3, 3], [3, 3, 2, 2, 2, 8, 8, 2, 2, 3, 3, 3, 3], [3, 3, 2, 8, 8, 8, 8, 8, 2, 3, 3, 3, 3], [3, 3, 2, 8, 2, 2, 2, 8, 2, 3, 3, 3, 3], [3, 3, 2, 8, 2, 3, 2, 8, 2, 3, 3, 3, 3], [3, 3, 2, 8, 2, 3, 2, 8, 2, 2, 3, 3, 3], [3, 2, 2, 8, 2, 3, 2, 8, 8, 2, 3, 3, 3], [3, 2, 8, 8, 2, 3, 2, 8, 2, 2, 3, 3, 3], [3, 2, 2, 2, 2, 3, 2, 2, 2, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 3, 1, 1, 0, 0, 1, 8, 1, 0, 0], [0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0], [1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 0], [0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0], [0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0], [0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 1, 0, 1, 0, 6, 1, 1, 0], [0, 7, 0, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 1, 0], [0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 4, 4], [2, 2, 2, 3, 3, 3, 4, 4, 8, 8, 8, 4, 4], [2, 2, 2, 3, 4, 4, 4, 4, 8, 6, 8, 4, 4], [3, 3, 3, 3, 4, 4, 4, 4, 8, 6, 8, 8, 4], [7, 7, 3, 4, 4, 4, 4, 4, 8, 6, 6, 8, 4], [7, 7, 3, 4, 4, 4, 4, 8, 8, 6, 8, 8, 4], [7, 7, 3, 3, 3, 3, 4, 8, 6, 6, 8, 4, 4], [7, 7, 7, 7, 7, 3, 4, 8, 6, 6, 8, 8, 4], [7, 7, 7, 3, 3, 3, 4, 8, 6, 6, 6, 8, 4], [7, 7, 7, 3, 4, 4, 4, 8, 6, 6, 8, 8, 4], [7, 7, 3, 3, 4, 4, 4, 8, 8, 8, 8, 4, 4], [7, 7, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [7, 7, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]]}, {"input": [[0, 0, 9, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0], [0, 1, 6, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0], [0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0], [0, 8, 0, 0, 0, 3, 0, 0, 0, 0, 1, 1, 0], [0, 0, 0, 0, 0, 1, 0, 0, 8, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0], [1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0], [0, 0, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0], [0, 0, 1, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], [7, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0]], "output": [[9, 9, 9, 9, 6, 8, 8, 8, 8, 8, 8, 8, 8], [6, 6, 9, 9, 6, 8, 8, 3, 3, 3, 3, 3, 8], [8, 6, 6, 6, 6, 8, 8, 3, 8, 8, 8, 3, 8], [8, 8, 8, 8, 8, 8, 8, 3, 8, 8, 3, 3, 8], [8, 8, 8, 8, 8, 3, 3, 3, 8, 8, 3, 8, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 3, 3, 8], [8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 3, 8], [8, 8, 8, 8, 8, 3, 3, 8, 8, 8, 8, 3, 8], [2, 2, 2, 8, 8, 8, 3, 8, 3, 3, 3, 3, 8], [7, 7, 2, 8, 8, 8, 3, 3, 3, 8, 8, 8, 8], [7, 7, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8], [7, 7, 7, 7, 2, 2, 8, 8, 8, 8, 8, 8, 8], [7, 7, 7, 7, 7, 2, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[0, 5, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0], [0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0], [1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0], [0, 0, 7, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 8, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 6, 0], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 4, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0]], "output": [[5, 5, 5, 3, 7, 7, 7, 7, 7, 7, 7, 7, 7], [5, 5, 5, 3, 7, 7, 6, 6, 6, 6, 6, 7, 7], [5, 5, 3, 3, 7, 7, 6, 8, 8, 8, 6, 7, 7], [3, 3, 3, 7, 7, 7, 6, 6, 8, 8, 6, 6, 7], [7, 7, 7, 7, 7, 7, 7, 6, 8, 8, 8, 6, 7], [7, 7, 7, 7, 7, 7, 7, 6, 8, 8, 8, 6, 7], [7, 7, 7, 7, 7, 7, 7, 6, 8, 8, 8, 6, 7], [7, 7, 7, 7, 7, 6, 6, 6, 8, 8, 8, 6, 7], [7, 7, 7, 7, 7, 6, 8, 8, 8, 8, 8, 6, 7], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7], [4, 4, 4, 4, 4, 4, 4, 4, 6, 7, 7, 7, 7], [4, 4, 4, 4, 4, 4, 4, 6, 6, 7, 7, 7, 7], [4, 4, 4, 4, 4, 4, 4, 6, 7, 7, 7, 7, 7]]}]}