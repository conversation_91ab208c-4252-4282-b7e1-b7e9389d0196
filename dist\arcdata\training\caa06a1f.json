{"test": [{"input": [[8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 3, 3, 3, 3, 3, 3], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5], [5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8], [7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5, 7, 8, 5]]}], "train": [{"input": [[6, 7, 6, 7, 6, 7, 6, 3, 3, 3, 3], [7, 6, 7, 6, 7, 6, 7, 3, 3, 3, 3], [6, 7, 6, 7, 6, 7, 6, 3, 3, 3, 3], [7, 6, 7, 6, 7, 6, 7, 3, 3, 3, 3], [6, 7, 6, 7, 6, 7, 6, 3, 3, 3, 3], [7, 6, 7, 6, 7, 6, 7, 3, 3, 3, 3], [6, 7, 6, 7, 6, 7, 6, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7], [6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6], [7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7], [6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6], [7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7], [6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6], [7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7], [6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6], [7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7], [6, 7, 6, 7, 6, 7, 6, 7, 6, 7, 6], [7, 6, 7, 6, 7, 6, 7, 6, 7, 6, 7]]}, {"input": [[6, 3, 6, 3, 6, 3, 6, 1], [3, 6, 3, 6, 3, 6, 3, 1], [6, 3, 6, 3, 6, 3, 6, 1], [3, 6, 3, 6, 3, 6, 3, 1], [6, 3, 6, 3, 6, 3, 6, 1], [3, 6, 3, 6, 3, 6, 3, 1], [6, 3, 6, 3, 6, 3, 6, 1], [1, 1, 1, 1, 1, 1, 1, 1]], "output": [[3, 6, 3, 6, 3, 6, 3, 6], [6, 3, 6, 3, 6, 3, 6, 3], [3, 6, 3, 6, 3, 6, 3, 6], [6, 3, 6, 3, 6, 3, 6, 3], [3, 6, 3, 6, 3, 6, 3, 6], [6, 3, 6, 3, 6, 3, 6, 3], [3, 6, 3, 6, 3, 6, 3, 6], [6, 3, 6, 3, 6, 3, 6, 3]]}, {"input": [[5, 4, 5, 4, 5, 6], [4, 5, 4, 5, 4, 6], [5, 4, 5, 4, 5, 6], [4, 5, 4, 5, 4, 6], [5, 4, 5, 4, 5, 6], [6, 6, 6, 6, 6, 6]], "output": [[4, 5, 4, 5, 4, 5], [5, 4, 5, 4, 5, 4], [4, 5, 4, 5, 4, 5], [5, 4, 5, 4, 5, 4], [4, 5, 4, 5, 4, 5], [5, 4, 5, 4, 5, 4]]}]}