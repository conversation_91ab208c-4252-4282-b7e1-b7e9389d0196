{"train": [{"input": [[0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [6, 0, 0, 8, 0, 6, 0, 8, 0, 0, 6], [0, 0, 6, 8, 0, 0, 0, 8, 0, 6, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 6, 0, 8, 0, 0, 6, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 6, 0, 8, 0, 0, 0, 8, 6, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 6, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [0, 0, 0, 8, 6, 0, 0, 8, 0, 0, 0]], "output": [[1, 0, 1], [1, 0, 0], [0, 0, 0]]}, {"input": [[6, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 6, 8, 0, 0, 6], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [6, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [0, 0, 0, 8, 0, 0, 6, 8, 6, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [6, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 6, 0, 8, 0, 6, 0, 8, 0, 0, 6]], "output": [[0, 0, 0], [0, 0, 1], [1, 0, 0]]}, {"input": [[0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 6], [0, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [0, 6, 0, 8, 0, 6, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 0], [6, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 6, 0, 0], [0, 6, 0, 8, 0, 0, 0, 8, 0, 0, 6], [0, 0, 0, 8, 6, 0, 0, 8, 0, 0, 0]], "output": [[0, 1, 1], [0, 0, 0], [0, 0, 1]]}, {"input": [[0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 6], [0, 0, 6, 8, 0, 0, 0, 8, 6, 0, 0], [0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 0], [6, 0, 0, 8, 0, 0, 6, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 6, 8, 0, 0, 0, 8, 6, 0, 0], [0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 0]], "output": [[0, 0, 1], [0, 1, 0], [0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 8, 0, 0, 0, 8, 6, 0, 6], [0, 6, 0, 8, 0, 0, 6, 8, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [0, 0, 6, 8, 0, 6, 0, 8, 0, 0, 0], [0, 0, 0, 8, 6, 0, 0, 8, 0, 0, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 6, 8, 0, 0, 0, 8, 0, 0, 0], [6, 0, 0, 8, 0, 0, 0, 8, 0, 6, 0], [0, 0, 0, 8, 0, 6, 0, 8, 0, 0, 0]], "output": [[0, 0, 1], [0, 1, 1], [1, 0, 0]]}]}