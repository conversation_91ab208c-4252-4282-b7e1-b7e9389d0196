# 🔀 Niveau 0 - Routage des Données

## 📋 Vue d'Ensemble

Le **Niveau 0** fournit les données brutes qui sont ensuite **routées** vers les différents domaines d'analyse du **Niveau 1**.

## 🎯 Principe de Routage

```
Niveau 0 (Don<PERSON><PERSON> Brutes)
    ↓
┌─────────────────────────────────────┐
│        ROUTEUR DE DONNÉES           │
└─────────────────────────────────────┘
    ↓           ↓           ↓           ↓
Séparations  Bordures   Objets    Patterns
(Niveau 1B)  (Niveau 1B) (Niveau 1) (Niveau 1)
```

## 📊 Données Sources (Niveau 0)

```python
level_0_data = {
    'input_grid': {
        'grid_array': np.ndarray,           # SOURCE UNIQUE
        'width': int,
        'height': int,
        'value_frequencies': Dict[int, int]
    },
    'output_grid': {
        'grid_array': np.ndarray,           # SOURCE UNIQUE
        'width': int,
        'height': int,
        'value_frequencies': Dict[int, int]
    }
}
```

## 🔀 Routage par Domaine

### **1. Vers Détection Géométrique (1A)**
```python
# Utilise : grid_array
geometric_detection.analyze_uniform_lines(grid_array)
geometric_detection.analyze_diagonals(grid_array)
```

### **2. Vers Classification Structurelle (1B)**
```python
# Utilise : grid_array + résultats 1A
structural_classification.classify_borders(grid_array, uniform_lines)
structural_classification.classify_separators(grid_array, uniform_lines)
structural_classification.classify_grid_lines(grid_array, uniform_lines)
```

### **3. Vers Analyse de Blocs (1C)**
```python
# Utilise : grid_array + résultats 1B
block_analysis.extract_blocks(grid_array, separators, grid_lines)
block_analysis.analyze_block_content(extracted_blocks)
```

### **4. Vers Analyse d'Objets**
```python
# Utilise : grid_array + value_frequencies
objects.detect_background(grid_array, value_frequencies)
objects.extract_objects(grid_array, background_color)
objects.detect_anchor_points(grid_array, color=2)  # Points rouges
```

### **5. Vers Autres Domaines**
```python
# Colors
colors.analyze_color_distribution(value_frequencies)

# Symmetries  
symmetries.analyze_symmetries(grid_array)

# Patterns
patterns.detect_repeating_patterns(grid_array)

# Spatial Properties
spatial.analyze_spatial_distribution(grid_array, value_frequencies)
```

## 🛡️ Règles de Routage

### **Interdictions Strictes**
- ❌ **Aucun domaine** ne peut accéder directement aux données d'un autre domaine
- ❌ **Aucun domaine** ne peut modifier les données du Niveau 0
- ❌ **Aucune donnée dérivée** ne peut être stockée au Niveau 0

### **Autorisations**
- ✅ **Lecture seule** des données Niveau 0 par tous les domaines
- ✅ **Utilisation des résultats** des sous-niveaux précédents (1A → 1B → 1C)
- ✅ **Calculs dérivés** stockés dans le domaine approprié

## 📋 Flux de Données Détaillé

### **Étape 1 : Niveau 1A (Détection Géométrique)**
```python
input_1a = level_0['input_grid']['grid_array']
output_1a = {
    'uniform_rows': detect_uniform_rows(input_1a),
    'uniform_columns': detect_uniform_columns(input_1a),
    'uniform_diagonals': detect_uniform_diagonals(input_1a)
}
```

### **Étape 2 : Niveau 1B (Classification Structurelle)**
```python
input_1b = {
    'grid_array': level_0['input_grid']['grid_array'],
    'uniform_lines': output_1a
}
output_1b = {
    'borders': classify_borders(input_1b),
    'separators': classify_separators(input_1b),
    'grid_lines': classify_grid_lines(input_1b)
}
```

### **Étape 3 : Niveau 1C (Analyse de Blocs)**
```python
input_1c = {
    'grid_array': level_0['input_grid']['grid_array'],
    'structural_elements': output_1b
}
output_1c = {
    'detected_blocks': extract_and_analyze_blocks(input_1c)
}
```

### **Étape 4 : Autres Domaines (Parallèles)**
```python
# Objets (utilise directement Niveau 0)
objects_input = {
    'grid_array': level_0['input_grid']['grid_array'],
    'value_frequencies': level_0['input_grid']['value_frequencies']
}

# Colors (utilise directement Niveau 0)
colors_input = level_0['input_grid']['value_frequencies']

# Etc.
```

## 🎯 Avantages de ce Routage

### **1. Traçabilité Complète**
- Chaque donnée dérivée peut être tracée jusqu'à sa source Niveau 0
- Flux de données explicite et documenté

### **2. Isolation des Domaines**
- Chaque domaine reste indépendant
- Pas de contamination croisée

### **3. Évolutivité**
- Ajout facile de nouveaux domaines
- Modification d'un domaine sans impact sur les autres

### **4. Debuggabilité**
- Identification facile des sources d'erreur
- Tests isolés par domaine

---

**Ce routage garantit une architecture propre et maintenable pour l'analyse des puzzles ARC AGI.**