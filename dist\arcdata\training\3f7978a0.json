{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 8, 0, 0, 8], [0, 5, 0, 0, 0, 5, 0, 0, 0], [0, 5, 0, 8, 0, 5, 0, 8, 0], [0, 5, 0, 0, 0, 5, 0, 0, 0], [0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 0, 0, 0, 8], [5, 0, 0, 0, 5], [5, 0, 8, 0, 5], [5, 0, 0, 0, 5], [8, 0, 0, 0, 8]]}, {"input": [[0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8], [0, 0, 8, 0, 0, 0, 0, 0, 8, 0, 0], [8, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 8, 8, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 0, 8, 0, 8, 0, 0, 0, 8, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 8, 8, 0]], "output": [[8, 0, 0, 0, 0, 0, 8], [5, 0, 0, 0, 0, 0, 5], [5, 0, 0, 8, 8, 0, 5], [5, 0, 0, 0, 0, 0, 5], [8, 0, 8, 0, 0, 0, 8]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 8, 0, 0, 0], [0, 0, 8, 5, 0, 8, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 8, 0, 0, 0, 0], [0, 0, 8, 5, 0, 8, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 8, 8, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0]], "output": [[8, 0, 0, 0, 8], [5, 0, 0, 0, 5], [5, 0, 8, 0, 5], [5, 0, 0, 0, 5], [5, 0, 8, 0, 5], [8, 0, 0, 0, 8]]}], "test": [{"input": [[8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 8, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 8], [5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 5, 0, 0, 8, 0, 0, 8, 0, 0, 0], [5, 0, 8, 5, 8, 0, 0, 0, 0, 0, 0, 0, 8], [5, 0, 0, 5, 0, 8, 0, 0, 0, 0, 0, 0, 0], [5, 8, 0, 5, 0, 0, 0, 0, 0, 0, 8, 0, 8], [5, 0, 0, 5, 0, 0, 0, 8, 0, 0, 0, 0, 0], [8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8]], "output": [[8, 0, 0, 8], [5, 8, 0, 5], [5, 0, 0, 5], [5, 0, 0, 5], [5, 0, 0, 5], [5, 0, 8, 5], [5, 0, 0, 5], [5, 8, 0, 5], [5, 0, 0, 5], [8, 0, 0, 8]]}]}