{"train": [{"input": [[2, 1, 1, 1, 2], [0, 1, 0, 1, 0], [0, 1, 0, 1, 0], [0, 1, 0, 1, 0], [0, 0, 0, 0, 0]], "output": [[2, 1, 1, 1, 2], [4, 1, 0, 1, 4], [4, 1, 0, 1, 4], [4, 1, 0, 1, 4], [4, 4, 4, 4, 4]]}, {"input": [[0, 0, 0, 1, 2], [0, 0, 0, 1, 0], [0, 1, 0, 1, 0], [0, 1, 0, 0, 0], [2, 1, 0, 0, 0]], "output": [[0, 0, 0, 1, 2], [4, 4, 4, 1, 4], [4, 1, 4, 1, 4], [4, 1, 4, 4, 4], [2, 1, 0, 0, 0]]}, {"input": [[2, 0, 0, 0, 0, 0], [1, 1, 1, 1, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1], [0, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0]], "output": [[2, 4, 4, 4, 4, 0], [1, 1, 1, 1, 4, 0], [0, 0, 4, 4, 4, 0], [0, 0, 4, 1, 1, 1], [0, 0, 4, 4, 4, 2], [0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 2], [0, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 0], [2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[4, 4, 4, 4, 4, 2], [4, 1, 1, 1, 1, 1], [4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 4], [2, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 1, 1, 0, 0, 0, 1, 2], [0, 0, 0, 1, 1, 0, 0, 0, 1, 0], [0, 0, 0, 1, 1, 0, 0, 0, 1, 0], [0, 0, 0, 1, 1, 0, 0, 0, 1, 0], [0, 1, 0, 1, 1, 0, 1, 0, 1, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [2, 1, 0, 0, 0, 0, 1, 0, 0, 0]], "output": [[0, 0, 0, 1, 1, 0, 0, 0, 1, 2], [0, 0, 0, 1, 1, 0, 0, 0, 1, 4], [0, 0, 0, 1, 1, 0, 0, 0, 1, 4], [4, 4, 4, 1, 1, 4, 4, 4, 1, 4], [4, 1, 4, 1, 1, 4, 1, 4, 1, 4], [4, 1, 4, 4, 4, 4, 1, 4, 4, 4], [4, 1, 0, 0, 0, 0, 1, 0, 0, 0], [4, 1, 0, 0, 0, 0, 1, 0, 0, 0], [4, 1, 0, 0, 0, 0, 1, 0, 0, 0], [2, 1, 0, 0, 0, 0, 1, 0, 0, 0]]}], "test": [{"input": [[2, 1, 0, 0, 0, 1, 0, 0, 0, 2], [0, 1, 0, 0, 0, 1, 0, 1, 0, 0], [0, 1, 0, 1, 0, 1, 0, 1, 0, 0], [0, 1, 0, 1, 0, 1, 0, 1, 0, 0], [0, 1, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 0, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 0, 0, 0]], "output": [[2, 1, 0, 0, 0, 1, 4, 4, 4, 2], [4, 1, 4, 4, 4, 1, 4, 1, 0, 0], [4, 1, 4, 1, 4, 1, 4, 1, 0, 0], [4, 1, 4, 1, 4, 1, 4, 1, 0, 0], [4, 1, 4, 1, 4, 1, 4, 1, 0, 0], [4, 4, 4, 1, 4, 4, 4, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 1, 0, 0], [0, 0, 0, 1, 0, 1, 0, 0, 0, 0]]}]}