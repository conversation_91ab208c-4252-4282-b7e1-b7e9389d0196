import React from 'react';
import {
  Stack,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  GridOn as GridOnIcon,
  <PERSON>lette as PaletteIcon,
  DataObject as DataObjectIcon,
  ExpandMore as ExpandMoreIcon,
  AutoAwesome as SymmetryIcon,
  Pattern as PatternIcon,
  Transform as TransformIcon
} from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { AnalysisAccordion } from './shared/AnalysisAccordion';
import { useExpandedSections } from '../hooks/useExpandedSections';
import { Level1PanelProps } from '../types/AnalysisPanelTypes';

export const Level1Panel: React.FC<Level1PanelProps> = ({ analysis, showValues }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections(['geometric_detection']);

  if (!analysis.level_1) {
    return (
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={true}
      />

      {/* Analyses Complètes par Exemple d'Entraînement */}
      <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 4 }}>
        Analyses Complètes par Exemple d'Entraînement
      </Typography>

      {analysis.level_1.training_examples_analysis && (
        <Stack spacing={2}>
          {analysis.level_1.training_examples_analysis.map((exampleAnalysis: any, index: number) => (
            <AnalysisAccordion
              key={exampleAnalysis.example_id}
              id={`example_${index}`}
              title={`Exemple d'Entraînement ${index + 1}`}
              icon={<AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
              expanded={isExpanded(`example_${index}`)}
              onChange={handleAccordionChange(`example_${index}`)}
            >
              <Stack spacing={2}>
                {/* Vue d'ensemble Input/Output */}
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1}>
                          <Chip label={`${exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                          <Chip label={`${exampleAnalysis.input_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                          <Chip label={`Fond: ${showValues ? exampleAnalysis.input_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1}>
                          <Chip label={`${exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                          <Chip label={`${exampleAnalysis.output_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                          <Chip label={`Fond: ${showValues ? exampleAnalysis.output_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {/* Analyses détaillées par domaine */}
                <Stack spacing={1}>
                  {/* Détection Géométrique */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Détection Géométrique
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1}>
                          <Chip label={`Lignes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                          <Chip label={`Colonnes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                          {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length > 0 && (
                            <Typography variant="caption">
                              Lignes: {exampleAnalysis.input_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(${r.color})`).join(', ')}
                            </Typography>
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1}>
                          <Chip label={`Lignes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                          <Chip label={`Colonnes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                          {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length > 0 && (
                            <Typography variant="caption">
                              Lignes: {exampleAnalysis.output_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(${r.color})`).join(', ')}
                            </Typography>
                          )}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Couleurs */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PaletteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse des Couleurs
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1} direction="row" flexWrap="wrap">
                          {exampleAnalysis.input_analysis.colors.present_colors.map((color: string) => (
                            <Chip
                              key={color}
                              label={showValues ? `${color}: ${exampleAnalysis.input_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.input_analysis.colors.color_distribution[color]}`}
                              size="small"
                              sx={{
                                backgroundColor: `var(--symbol-${color}-bg)`,
                                color: `var(--symbol-${color}-text)`,
                                fontWeight: 'bold'
                              }}
                            />
                          ))}
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1} direction="row" flexWrap="wrap">
                          {exampleAnalysis.output_analysis.colors.present_colors.map((color: string) => (
                            <Chip
                              key={color}
                              label={showValues ? `${color}: ${exampleAnalysis.output_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.output_analysis.colors.color_distribution[color]}`}
                              size="small"
                              sx={{
                                backgroundColor: `var(--symbol-${color}-bg)`,
                                color: `var(--symbol-${color}-text)`,
                                fontWeight: 'bold'
                              }}
                            />
                          ))}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Objets sur Table */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <DataObjectIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Objets sur Table
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input ({exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets)</Typography>
                        <Stack spacing={1}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap">
                              <Chip
                                label={showValues ? `Fond: ${exampleAnalysis.input_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance: ${(exampleAnalysis.input_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.input_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                size="small"
                                color="warning"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                            <Stack spacing={1}>
                              {exampleAnalysis.input_analysis.objects.detected_objects.map((obj: any, objIndex: number) => (
                                <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                    <Typography variant="caption">
                                      {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                      {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                    </Typography>
                                  </Stack>
                                </Card>
                              ))}
                            </Stack>
                          </Box>
                          {exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                            <Box>
                              <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                              <Chip
                                label={`${exampleAnalysis.input_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                size="small"
                                color="error"
                              />
                            </Box>
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output ({exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets)</Typography>
                        <Stack spacing={1}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap">
                              <Chip
                                label={showValues ? `Fond: ${exampleAnalysis.output_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance: ${(exampleAnalysis.output_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.output_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                size="small"
                                color="warning"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Objets Détectés:</Typography>
                            <Stack spacing={1}>
                              {exampleAnalysis.output_analysis.objects.detected_objects.map((obj: any, objIndex: number) => (
                                <Card key={objIndex} variant="outlined" sx={{ p: 1 }}>
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <Box sx={{ width: 12, height: 12, backgroundColor: `var(--symbol-${obj.color}-bg)`, borderRadius: 0.5 }} />
                                    <Typography variant="caption">
                                      {obj.shape_classification.basic_shape} ({obj.width}×{obj.height})
                                      {showValues && ` - Couleur ${obj.color} - Centre (${obj.center[0].toFixed(1)}, ${obj.center[1].toFixed(1)})`}
                                    </Typography>
                                  </Stack>
                                </Card>
                              ))}
                            </Stack>
                          </Box>
                          {exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length > 0 && (
                            <Box>
                              <Typography variant="caption" gutterBottom>Points d'Ancrage:</Typography>
                              <Chip
                                label={`${exampleAnalysis.output_analysis.objects.anchor_analysis.possible_anchor_points.length} points`}
                                size="small"
                                color="error"
                              />
                            </Box>
                          )}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Classification Structurelle */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Classification Structurelle
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input - Structure</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Bordures:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Complétude: ${exampleAnalysis.input_analysis.structural_classification.borders.border_completeness}`}
                                size="small"
                                color={exampleAnalysis.input_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                              />
                              <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                  <Chip
                                    key={border}
                                    label={border.replace('_border', '').toUpperCase()}
                                    size="small"
                                    color={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                    variant={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                  />
                                ))}
                              </Stack>
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Total: ${exampleAnalysis.input_analysis.structural_classification.separators.length}`}
                                size="small"
                                color="secondary"
                              />
                              {exampleAnalysis.input_analysis.structural_classification.separators.slice(0, 3).map((sep: any, sepIndex: number) => (
                                <Chip
                                  key={sepIndex}
                                  label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                size="small"
                                color={exampleAnalysis.input_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                              />
                              {exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size && (
                                <Chip
                                  label={`${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.input_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                  size="small"
                                  color="info"
                                />
                              )}
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output - Structure</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Bordures:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Complétude: ${exampleAnalysis.output_analysis.structural_classification.borders.border_completeness}`}
                                size="small"
                                color={exampleAnalysis.output_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                              />
                              <Stack direction="row" spacing={0.5} flexWrap="wrap">
                                {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                  <Chip
                                    key={border}
                                    label={border.replace('_border', '').toUpperCase()}
                                    size="small"
                                    color={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                    variant={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                  />
                                ))}
                              </Stack>
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Total: ${exampleAnalysis.output_analysis.structural_classification.separators.length}`}
                                size="small"
                                color="secondary"
                              />
                              {exampleAnalysis.output_analysis.structural_classification.separators.slice(0, 3).map((sep: any, sepIndex: number) => (
                                <Chip
                                  key={sepIndex}
                                  label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Structure de Grille:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'Grille détectée' : 'Pas de grille'}
                                size="small"
                                color={exampleAnalysis.output_analysis.structural_classification.grid_lines.has_grid_structure ? 'success' : 'default'}
                              />
                              {exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size && (
                                <Chip
                                  label={`${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[0]}×${exampleAnalysis.output_analysis.structural_classification.grid_lines.grid_size[1]} blocs`}
                                  size="small"
                                  color="info"
                                />
                              )}
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Symétries */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <SymmetryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse des Symétries
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input - Symétries ({exampleAnalysis.input_analysis.symmetries.symmetry_count})</Typography>
                        <Stack direction="row" spacing={1} flexWrap="wrap">
                          <Chip
                            label="Horizontale"
                            size="small"
                            color={exampleAnalysis.input_analysis.symmetries.horizontal ? 'success' : 'default'}
                            variant={exampleAnalysis.input_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Verticale"
                            size="small"
                            color={exampleAnalysis.input_analysis.symmetries.vertical ? 'success' : 'default'}
                            variant={exampleAnalysis.input_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Diagonale principale"
                            size="small"
                            color={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                            variant={exampleAnalysis.input_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Diagonale anti"
                            size="small"
                            color={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                            variant={exampleAnalysis.input_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output - Symétries ({exampleAnalysis.output_analysis.symmetries.symmetry_count})</Typography>
                        <Stack direction="row" spacing={1} flexWrap="wrap">
                          <Chip
                            label="Horizontale"
                            size="small"
                            color={exampleAnalysis.output_analysis.symmetries.horizontal ? 'success' : 'default'}
                            variant={exampleAnalysis.output_analysis.symmetries.horizontal ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Verticale"
                            size="small"
                            color={exampleAnalysis.output_analysis.symmetries.vertical ? 'success' : 'default'}
                            variant={exampleAnalysis.output_analysis.symmetries.vertical ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Diagonale principale"
                            size="small"
                            color={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'success' : 'default'}
                            variant={exampleAnalysis.output_analysis.symmetries.diagonal_main ? 'filled' : 'outlined'}
                          />
                          <Chip
                            label="Diagonale anti"
                            size="small"
                            color={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'success' : 'default'}
                            variant={exampleAnalysis.output_analysis.symmetries.diagonal_anti ? 'filled' : 'outlined'}
                          />
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Motifs Répétitifs */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Motifs Répétitifs
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input - Motifs</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Patterns: ${exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Couverture: ${(exampleAnalysis.input_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                size="small"
                                color="secondary"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Complexité:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Complexité: ${exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                size="small"
                                color={
                                  exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                    exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                }
                              />
                              <Chip
                                label={`Régularité: ${(exampleAnalysis.input_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output - Motifs</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Statistiques:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Patterns: ${exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.total_patterns}`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_density * 100).toFixed(2)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Couverture: ${(exampleAnalysis.output_analysis.repeating_patterns.pattern_statistics.pattern_coverage * 100).toFixed(1)}%`}
                                size="small"
                                color="secondary"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Complexité:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Complexité: ${exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity}`}
                                size="small"
                                color={
                                  exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'complex' ? 'error' :
                                    exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.pattern_complexity === 'medium' ? 'warning' : 'success'
                                }
                              />
                              <Chip
                                label={`Régularité: ${(exampleAnalysis.output_analysis.repeating_patterns.complexity_analysis.regularity_index * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Propriétés Spatiales */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Propriétés Spatiales
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input - Propriétés Spatiales</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Densité objets: ${(exampleAnalysis.input_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Ratio fond: ${(exampleAnalysis.input_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Ratio rempli: ${(exampleAnalysis.input_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                size="small"
                                color="secondary"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={showValues ? `Centre: (${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.input_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                size="small"
                                color="warning"
                              />
                              <Chip
                                label={`Distribution: ${exampleAnalysis.input_analysis.spatial_properties.spatial_distribution}`}
                                size="small"
                                color={
                                  exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                    exampleAnalysis.input_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                }
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output - Propriétés Spatiales</Typography>
                        <Stack spacing={2}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Densité et Distribution:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={`Densité objets: ${(exampleAnalysis.output_analysis.spatial_properties.object_density * 100).toFixed(2)}%`}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Ratio fond: ${(exampleAnalysis.output_analysis.spatial_properties.background_ratio * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Ratio rempli: ${(exampleAnalysis.output_analysis.spatial_properties.filled_ratio * 100).toFixed(1)}%`}
                                size="small"
                                color="secondary"
                              />
                            </Stack>
                          </Box>
                          <Box>
                            <Typography variant="caption" gutterBottom>Centre de Masse et Distribution:</Typography>
                            <Stack spacing={1}>
                              <Chip
                                label={showValues ? `Centre: (${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[0].toFixed(1)}, ${exampleAnalysis.output_analysis.spatial_properties.center_of_mass[1].toFixed(1)})` : 'Centre calculé'}
                                size="small"
                                color="warning"
                              />
                              <Chip
                                label={`Distribution: ${exampleAnalysis.output_analysis.spatial_properties.spatial_distribution}`}
                                size="small"
                                color={
                                  exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'clustered' ? 'error' :
                                    exampleAnalysis.output_analysis.spatial_properties.spatial_distribution === 'uniform' ? 'success' : 'info'
                                }
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>
                </Stack>
              </Stack>
            </AnalysisAccordion>
          ))}
        </Stack>
      )}

      {/* Analyses Détaillées */}
      <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 4 }}>
        Analyses Détaillées
      </Typography>

      {/* Détection Géométrique (1A) - Tous les exemples */}
      <AnalysisAccordion
        id="geometric_detection"
        title="Détection Géométrique"
        icon={<GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
        expanded={isExpanded('geometric_detection')}
        onChange={handleAccordionChange('geometric_detection')}
      >
        <Stack spacing={3}>
          {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis: any, index: number) => (
            <Box key={exampleAnalysis.example_id}>
              <Typography variant="h6" gutterBottom>
                Exemple {index + 1}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="primary">Input - Lignes Uniformes</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Lignes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Colonnes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length}`}
                          size="small"
                          color="primary"
                        />
                        {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Lignes: {exampleAnalysis.input_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(couleur ${r.color})`).join(', ')}
                          </Typography>
                        )}
                        {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Colonnes: {exampleAnalysis.input_analysis.geometric_detection.uniform_columns.map((c: any) => `${c.index}(couleur ${c.color})`).join(', ')}
                          </Typography>
                        )}
                      </Stack>
                      {Object.keys(exampleAnalysis.input_analysis.geometric_detection.color_frequency_in_lines).length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" gutterBottom>Distribution par Couleur:</Typography>
                          <Stack direction="row" spacing={1} flexWrap="wrap">
                            {Object.entries(exampleAnalysis.input_analysis.geometric_detection.color_frequency_in_lines).map(([color, count]) => (
                              <Chip
                                key={color}
                                label={showValues ? `${color}: ${count}` : `${count}`}
                                size="small"
                                sx={{
                                  backgroundColor: `var(--symbol-${color}-bg)`,
                                  color: `var(--symbol-${color}-text)`,
                                  fontWeight: 'bold'
                                }}
                              />
                            ))}
                          </Stack>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="secondary">Output - Lignes Uniformes</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Lignes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Colonnes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length}`}
                          size="small"
                          color="primary"
                        />
                        {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Lignes: {exampleAnalysis.output_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(couleur ${r.color})`).join(', ')}
                          </Typography>
                        )}
                        {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Colonnes: {exampleAnalysis.output_analysis.geometric_detection.uniform_columns.map((c: any) => `${c.index}(couleur ${c.color})`).join(', ')}
                          </Typography>
                        )}
                      </Stack>
                      {Object.keys(exampleAnalysis.output_analysis.geometric_detection.color_frequency_in_lines).length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" gutterBottom>Distribution par Couleur:</Typography>
                          <Stack direction="row" spacing={1} flexWrap="wrap">
                            {Object.entries(exampleAnalysis.output_analysis.geometric_detection.color_frequency_in_lines).map(([color, count]) => (
                              <Chip
                                key={color}
                                label={showValues ? `${color}: ${count}` : `${count}`}
                                size="small"
                                sx={{
                                  backgroundColor: `var(--symbol-${color}-bg)`,
                                  color: `var(--symbol-${color}-text)`,
                                  fontWeight: 'bold'
                                }}
                              />
                            ))}
                          </Stack>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )) || (
            <Typography variant="body2" color="text.secondary">
              Aucune donnée de détection géométrique disponible pour les exemples d'entraînement.
            </Typography>
          )}
        </Stack>
      </AnalysisAccordion>

      {/* Classification Structurelle (1B) - Tous les exemples */}
      <AnalysisAccordion
        id="structural_classification"
        title="Classification Structurelle"
        icon={<TransformIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
        expanded={isExpanded('structural_classification')}
        onChange={handleAccordionChange('structural_classification')}
      >
        <Stack spacing={3}>
          {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis: any, index: number) => (
            <Box key={exampleAnalysis.example_id}>
              <Typography variant="h6" gutterBottom>
                Exemple {index + 1}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="primary">Input - Structure</Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption" gutterBottom>Bordures:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={`Complétude: ${exampleAnalysis.input_analysis.structural_classification.borders.border_completeness}`}
                              size="small"
                              color={exampleAnalysis.input_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                            />
                            <Stack direction="row" spacing={0.5} flexWrap="wrap">
                              {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                <Chip
                                  key={border}
                                  label={border.replace('_border', '').toUpperCase()}
                                  size="small"
                                  color={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                  variant={exampleAnalysis.input_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                />
                              ))}
                            </Stack>
                          </Stack>
                        </Box>
                        <Box>
                          <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={`Total: ${exampleAnalysis.input_analysis.structural_classification.separators.length}`}
                              size="small"
                              color="secondary"
                            />
                            {exampleAnalysis.input_analysis.structural_classification.separators.slice(0, 3).map((sep: any, sepIndex: number) => (
                              <Chip
                                key={sepIndex}
                                label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="secondary">Output - Structure</Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption" gutterBottom>Bordures:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={`Complétude: ${exampleAnalysis.output_analysis.structural_classification.borders.border_completeness}`}
                              size="small"
                              color={exampleAnalysis.output_analysis.structural_classification.borders.border_completeness === 'complete' ? 'success' : 'default'}
                            />
                            <Stack direction="row" spacing={0.5} flexWrap="wrap">
                              {(['top_border', 'bottom_border', 'left_border', 'right_border'] as const).map(border => (
                                <Chip
                                  key={border}
                                  label={border.replace('_border', '').toUpperCase()}
                                  size="small"
                                  color={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'success' : 'default'}
                                  variant={exampleAnalysis.output_analysis.structural_classification.borders[border]?.exists ? 'filled' : 'outlined'}
                                />
                              ))}
                            </Stack>
                          </Stack>
                        </Box>
                        <Box>
                          <Typography variant="caption" gutterBottom>Séparateurs:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={`Total: ${exampleAnalysis.output_analysis.structural_classification.separators.length}`}
                              size="small"
                              color="secondary"
                            />
                            {exampleAnalysis.output_analysis.structural_classification.separators.slice(0, 3).map((sep: any, sepIndex: number) => (
                              <Chip
                                key={sepIndex}
                                label={showValues ? `${sep.type} @ ${sep.position} (couleur ${sep.color})` : `${sep.type} @ ${sep.position}`}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )) || (
            <Typography variant="body2" color="text.secondary">
              Aucune donnée de classification structurelle disponible pour les exemples d'entraînement.
            </Typography>
          )}
        </Stack>
      </AnalysisAccordion>

      {/* Objets sur Table - Tous les exemples */}
      <AnalysisAccordion
        id="objects"
        title="Objets sur Table"
        icon={<DataObjectIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
        expanded={isExpanded('objects')}
        onChange={handleAccordionChange('objects')}
      >
        <Stack spacing={3}>
          {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis: any, index: number) => (
            <Box key={exampleAnalysis.example_id}>
              <Typography variant="h6" gutterBottom>
                Exemple {index + 1}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="primary">Input - Objets ({exampleAnalysis.input_analysis.objects.object_statistics.total_objects})</Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={showValues ? `Fond: ${exampleAnalysis.input_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                              size="small"
                              color="primary"
                            />
                            <Chip
                              label={`Confiance: ${(exampleAnalysis.input_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                              size="small"
                              color="info"
                            />
                            <Chip
                              label={`Densité: ${(exampleAnalysis.input_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                              size="small"
                              color="warning"
                            />
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="secondary">Output - Objets ({exampleAnalysis.output_analysis.objects.object_statistics.total_objects})</Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                          <Stack spacing={1}>
                            <Chip
                              label={showValues ? `Fond: ${exampleAnalysis.output_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                              size="small"
                              color="primary"
                            />
                            <Chip
                              label={`Confiance: ${(exampleAnalysis.output_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                              size="small"
                              color="info"
                            />
                            <Chip
                              label={`Densité: ${(exampleAnalysis.output_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                              size="small"
                              color="warning"
                            />
                          </Stack>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )) || (
            <Typography variant="body2" color="text.secondary">
              Aucune donnée d'objets disponible pour les exemples d'entraînement.
            </Typography>
          )}
        </Stack>
      </AnalysisAccordion>

      {/* Analyse de Blocs */}
      <AnalysisAccordion
        id="blocks"
        title="Analyse de Blocs"
        icon={<GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
        expanded={isExpanded('blocks')}
        onChange={handleAccordionChange('blocks')}
      >
        <Stack spacing={3}>
          {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis: any, index: number) => (
            <Box key={exampleAnalysis.example_id}>
              <Typography variant="h6" gutterBottom>
                Exemple {index + 1}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="primary">Input - Blocs ({exampleAnalysis.input_analysis.block_analysis.detected_blocks.length})</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Blocs détectés: ${exampleAnalysis.input_analysis.block_analysis.detected_blocks.length}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Uniformité: ${exampleAnalysis.input_analysis.block_analysis.block_uniformity.all_same_size ? 'Uniforme' : 'Variable'}`}
                          size="small"
                          color={exampleAnalysis.input_analysis.block_analysis.block_uniformity.all_same_size ? 'success' : 'warning'}
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="secondary">Output - Blocs ({exampleAnalysis.output_analysis.block_analysis.detected_blocks.length})</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Blocs détectés: ${exampleAnalysis.output_analysis.block_analysis.detected_blocks.length}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Uniformité: ${exampleAnalysis.output_analysis.block_analysis.block_uniformity.all_same_size ? 'Uniforme' : 'Variable'}`}
                          size="small"
                          color={exampleAnalysis.output_analysis.block_analysis.block_uniformity.all_same_size ? 'success' : 'warning'}
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )) || (
            <Typography variant="body2" color="text.secondary">
              Aucune donnée de blocs disponible pour les exemples d'entraînement.
            </Typography>
          )}
        </Stack>
      </AnalysisAccordion>

      {/* Analyse de Mosaïques */}
      <AnalysisAccordion
        id="mosaics"
        title="Analyse de Mosaïques"
        icon={<PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
        expanded={isExpanded('mosaics')}
        onChange={handleAccordionChange('mosaics')}
      >
        <Stack spacing={3}>
          {analysis.level_1?.training_examples_analysis?.map((exampleAnalysis: any, index: number) => (
            <Box key={exampleAnalysis.example_id}>
              <Typography variant="h6" gutterBottom>
                Exemple {index + 1}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="primary">Input - Mosaïques</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={exampleAnalysis.input_analysis.mosaics.mosaic_detection.is_potential_mosaic ? 'Mosaïque détectée' : 'Pas de mosaïque'}
                          size="small"
                          color={exampleAnalysis.input_analysis.mosaics.mosaic_detection.is_potential_mosaic ? 'success' : 'default'}
                        />
                        <Chip
                          label={`Confiance: ${(exampleAnalysis.input_analysis.mosaics.mosaic_detection.confidence_score * 100).toFixed(1)}%`}
                          size="small"
                          color="info"
                        />
                        <Chip
                          label={`Zones uniformes: ${exampleAnalysis.input_analysis.mosaics.mosaic_detection.uniform_zones_detection.total_uniform_zones}`}
                          size="small"
                          color="secondary"
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="secondary">Output - Mosaïques</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={exampleAnalysis.output_analysis.mosaics.mosaic_detection.is_potential_mosaic ? 'Mosaïque détectée' : 'Pas de mosaïque'}
                          size="small"
                          color={exampleAnalysis.output_analysis.mosaics.mosaic_detection.is_potential_mosaic ? 'success' : 'default'}
                        />
                        <Chip
                          label={`Confiance: ${(exampleAnalysis.output_analysis.mosaics.mosaic_detection.confidence_score * 100).toFixed(1)}%`}
                          size="small"
                          color="info"
                        />
                        <Chip
                          label={`Zones uniformes: ${exampleAnalysis.output_analysis.mosaics.mosaic_detection.uniform_zones_detection.total_uniform_zones}`}
                          size="small"
                          color="secondary"
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )) || (
            <Typography variant="body2" color="text.secondary">
              Aucune donnée de mosaïques disponible pour les exemples d'entraînement.
            </Typography>
          )}
        </Stack>
      </AnalysisAccordion>
    </Stack>
  );
};