import React from 'react';
import { <PERSON><PERSON>, Typo<PERSON>, <PERSON>rid, <PERSON>, CardContent, Chip, Box } from '@mui/material';
import { Analytics as AnalyticsIcon, GridOn as GridOnIcon, Palette as PaletteIcon, DataObject as DataObjectIcon } from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { AnalysisAccordion } from './shared/AnalysisAccordion';
import { useExpandedSections } from '../hooks/useExpandedSections';
import { Level1PanelProps } from '../types/AnalysisPanelTypes';

export const Level1Panel: React.FC<Level1PanelProps> = ({ analysis, showValues }) => {
  const { handleAccordionChange, isExpanded } = useExpandedSections(['geometric_detection']);

  if (!analysis.level_1) {
    return (
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={true}
      />

      {/* Analyses Complètes par Exemple d'Entraînement */}
      {analysis.level_1.training_examples_analysis && (
        <Stack spacing={2}>
          {analysis.level_1.training_examples_analysis.map((exampleAnalysis: any, index: number) => (
            <AnalysisAccordion
              key={exampleAnalysis.example_id}
              id={`example_${index}`}
              title={`Exemple d'Entraînement ${index + 1}`}
              icon={<AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />}
              expanded={isExpanded(`example_${index}`)}
              onChange={handleAccordionChange(`example_${index}`)}
            >
              <Stack spacing={2}>
                {/* Vue d'ensemble Input/Output */}
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1}>
                          <Chip label={`${exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                          <Chip label={`${exampleAnalysis.input_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                          <Chip label={`Fond: ${showValues ? exampleAnalysis.input_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1}>
                          <Chip label={`${exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets`} size="small" color="primary" />
                          <Chip label={`${exampleAnalysis.output_analysis.colors.present_colors.length} couleurs`} size="small" color="info" />
                          <Chip label={`Fond: ${showValues ? exampleAnalysis.output_analysis.objects.table_analysis.probable_background : 'détecté'}`} size="small" color="secondary" />
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {/* Analyses détaillées par domaine */}
                <Stack spacing={1}>
                  {/* Détection Géométrique */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <GridOnIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Détection Géométrique
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1}>
                          <Chip label={`Lignes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                          <Chip label={`Colonnes uniformes: ${exampleAnalysis.input_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                          {showValues && exampleAnalysis.input_analysis.geometric_detection.uniform_rows.length > 0 && (
                            <Typography variant="caption">
                              Lignes: {exampleAnalysis.input_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(${r.color})`).join(', ')}
                            </Typography>
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1}>
                          <Chip label={`Lignes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length}`} size="small" />
                          <Chip label={`Colonnes uniformes: ${exampleAnalysis.output_analysis.geometric_detection.uniform_columns.length}`} size="small" />
                          {showValues && exampleAnalysis.output_analysis.geometric_detection.uniform_rows.length > 0 && (
                            <Typography variant="caption">
                              Lignes: {exampleAnalysis.output_analysis.geometric_detection.uniform_rows.map((r: any) => `${r.index}(${r.color})`).join(', ')}
                            </Typography>
                          )}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Couleurs */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PaletteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Analyse des Couleurs
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input</Typography>
                        <Stack spacing={1} direction="row" flexWrap="wrap">
                          {exampleAnalysis.input_analysis.colors.present_colors.map((color: string) => (
                            <Chip
                              key={color}
                              label={showValues ? `${color}: ${exampleAnalysis.input_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.input_analysis.colors.color_distribution[color]}`}
                              size="small"
                              sx={{
                                backgroundColor: `var(--symbol-${color}-bg)`,
                                color: `var(--symbol-${color}-text)`,
                                fontWeight: 'bold'
                              }}
                            />
                          ))}
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output</Typography>
                        <Stack spacing={1} direction="row" flexWrap="wrap">
                          {exampleAnalysis.output_analysis.colors.present_colors.map((color: string) => (
                            <Chip
                              key={color}
                              label={showValues ? `${color}: ${exampleAnalysis.output_analysis.colors.color_distribution[color]}` : `${exampleAnalysis.output_analysis.colors.color_distribution[color]}`}
                              size="small"
                              sx={{
                                backgroundColor: `var(--symbol-${color}-bg)`,
                                color: `var(--symbol-${color}-text)`,
                                fontWeight: 'bold'
                              }}
                            />
                          ))}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Objets sur Table */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <DataObjectIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Objets sur Table
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="primary">Input ({exampleAnalysis.input_analysis.objects.object_statistics.total_objects} objets)</Typography>
                        <Stack spacing={1}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap">
                              <Chip
                                label={showValues ? `Fond: ${exampleAnalysis.input_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance: ${(exampleAnalysis.input_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.input_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                size="small"
                                color="warning"
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom color="secondary">Output ({exampleAnalysis.output_analysis.objects.object_statistics.total_objects} objets)</Typography>
                        <Stack spacing={1}>
                          <Box>
                            <Typography variant="caption" gutterBottom>Analyse de la Table:</Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap">
                              <Chip
                                label={showValues ? `Fond: ${exampleAnalysis.output_analysis.objects.table_analysis.probable_background}` : 'Fond détecté'}
                                size="small"
                                color="primary"
                              />
                              <Chip
                                label={`Confiance: ${(exampleAnalysis.output_analysis.objects.table_analysis.background_confidence * 100).toFixed(1)}%`}
                                size="small"
                                color="info"
                              />
                              <Chip
                                label={`Densité: ${(exampleAnalysis.output_analysis.objects.object_statistics.table_occupancy * 100).toFixed(2)}%`}
                                size="small"
                                color="warning"
                              />
                            </Stack>
                          </Box>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>
                </Stack>
              </Stack>
            </AnalysisAccordion>
          ))}
        </Stack>
      )}
    </Stack>
  );
};