{"train": [{"input": [[1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 3, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 3, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 3, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2]], "output": [[1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 2, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 1, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 1, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0, 0, 2]]}, {"input": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7]], "output": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 4, 0, 0], [0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 0, 0, 0, 0, 9, 0, 0, 0], [0, 0, 0, 9, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9]]}], "test": [{"input": [[5, 3, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 3, 0, 0, 3, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 3, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 0, 3, 0, 0, 4], [5, 0, 0, 3, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 3, 0, 0, 0, 0, 4], [5, 0, 3, 0, 0, 0, 3, 0, 0, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4]], "output": [[5, 5, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 4, 0, 0, 4, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 5, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 0, 4, 0, 0, 4], [5, 0, 0, 5, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4], [5, 0, 0, 0, 5, 0, 0, 0, 0, 4], [5, 0, 5, 0, 0, 0, 4, 0, 0, 4], [5, 0, 0, 0, 0, 0, 0, 0, 0, 4]]}]}