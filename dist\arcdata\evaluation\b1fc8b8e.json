{"train": [{"input": [[0, 0, 0, 8, 0, 0], [0, 0, 8, 8, 8, 0], [0, 8, 0, 8, 8, 0], [8, 8, 8, 0, 0, 0], [0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 8, 0, 0, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [0, 8, 0, 0, 8], [8, 8, 0, 8, 8]]}, {"input": [[8, 8, 8, 8, 0, 0], [8, 8, 8, 8, 8, 8], [0, 8, 8, 0, 8, 8], [0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[8, 8, 0, 8, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [8, 8, 0, 8, 8], [8, 8, 0, 8, 8]]}, {"input": [[0, 0, 0, 8, 0, 0], [0, 8, 8, 8, 8, 0], [8, 8, 8, 8, 8, 0], [0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 8, 0, 0, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [0, 8, 0, 0, 8], [8, 8, 0, 8, 8]]}, {"input": [[0, 0, 8, 8, 0, 0], [8, 8, 8, 8, 0, 0], [8, 8, 8, 8, 8, 8], [0, 0, 8, 8, 8, 8], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[8, 8, 0, 8, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [8, 8, 0, 8, 8], [8, 8, 0, 8, 8]]}, {"input": [[0, 0, 0, 8, 0, 0], [0, 8, 8, 8, 0, 0], [8, 8, 8, 0, 8, 0], [0, 8, 8, 8, 8, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 8, 0, 0, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [0, 8, 0, 0, 8], [8, 8, 0, 8, 8]]}], "test": [{"input": [[0, 0, 8, 8, 0, 0], [8, 8, 8, 8, 0, 0], [8, 8, 0, 8, 8, 0], [0, 8, 8, 8, 8, 0], [0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[8, 8, 0, 8, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [8, 8, 0, 8, 8], [8, 8, 0, 8, 8]]}, {"input": [[0, 8, 0, 8, 0, 0], [8, 8, 8, 8, 8, 0], [0, 0, 0, 8, 8, 8], [0, 0, 0, 0, 8, 8], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 8, 0, 0, 8], [8, 8, 0, 8, 8], [0, 0, 0, 0, 0], [0, 8, 0, 0, 8], [8, 8, 0, 8, 8]]}]}