{"train": [{"input": [[0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5, 2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5], [4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5], [8, 8, 2, 4, 8, 5, 8, 4, 2, 8, 8, 5, 8, 8, 2, 4, 8, 5, 8, 4, 2, 8, 8, 5], [5, 5, 5, 5, 5, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 2, 4, 8, 3, 0, 4, 2, 8, 0, 3, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 3, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5], [2, 2, 2, 4, 2, 3, 2, 4, 2, 2, 2, 3, 2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5], [8, 8, 2, 4, 8, 3, 8, 4, 2, 8, 8, 3, 8, 8, 2, 4, 8, 5, 8, 4, 2, 8, 8, 5], [0, 0, 2, 4, 8, 3, 0, 4, 2, 8, 0, 3, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [5, 5, 5, 5, 5, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5, 2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5], [4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 5], [2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5, 2, 2, 2, 4, 2, 5, 2, 4, 2, 2, 2, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [0, 0, 2, 4, 8, 5, 0, 4, 2, 8, 0, 5, 0, 0, 2, 4, 0, 5, 0, 4, 2, 0, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[3, 3, 3, 3, 3, 3, 3], [3, 0, 4, 2, 8, 0, 3], [3, 4, 4, 4, 4, 4, 3], [3, 2, 4, 2, 2, 2, 3], [3, 8, 4, 2, 8, 8, 3], [3, 0, 4, 2, 8, 0, 3], [3, 3, 3, 3, 3, 3, 3]]}, {"input": [[0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [8, 8, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 2, 3, 3, 8, 3, 3, 2, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3], [1, 1, 2, 3, 1, 8, 1, 3, 2, 1, 1, 8, 1, 1, 8, 3, 1, 8, 1, 3, 8, 1, 1, 8, 1, 1], [8, 8, 2, 8, 8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 2, 3, 1, 8, 0, 3, 2, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [3, 3, 2, 3, 3, 8, 3, 3, 2, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3], [8, 8, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 8, 3, 1, 8, 1, 3, 8, 1, 1, 8, 1, 1, 8, 3, 1, 8, 1, 3, 8, 1, 1, 8, 1, 1], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3, 8, 3, 3], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 8, 3, 1, 8, 1, 3, 8, 1, 1, 8, 1, 1, 8, 3, 1, 8, 1, 3, 8, 1, 1, 8, 1, 1], [0, 0, 8, 3, 1, 8, 0, 3, 8, 1, 0, 8, 0, 0, 8, 3, 0, 8, 0, 3, 8, 0, 0, 8, 1, 0]], "output": [[2, 2, 2, 2, 2, 2, 2], [2, 3, 3, 8, 3, 3, 2], [2, 3, 1, 8, 1, 3, 2], [2, 8, 8, 8, 8, 8, 2], [2, 3, 1, 8, 0, 3, 2], [2, 3, 3, 8, 3, 3, 2], [2, 2, 2, 2, 2, 2, 2]]}, {"input": [[0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3], [1, 1, 3, 1, 8, 5, 1, 1, 3, 8, 1, 5, 1, 1, 3, 1, 1, 5, 1, 8, 3, 1, 1, 5, 8, 1, 3, 1], [8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [1, 1, 3, 1, 8, 5, 1, 1, 3, 8, 1, 5, 1, 1, 3, 1, 1, 5, 1, 8, 3, 1, 1, 5, 8, 1, 3, 1], [3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3], [8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3], [1, 1, 3, 1, 8, 5, 1, 1, 3, 8, 1, 5, 1, 1, 3, 1, 1, 5, 1, 8, 3, 1, 1, 5, 8, 1, 3, 1], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 3, 1, 8, 6, 0, 1, 3, 8, 0, 6, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [8, 8, 3, 8, 8, 6, 8, 8, 3, 8, 8, 6, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8], [3, 3, 3, 3, 3, 6, 3, 3, 3, 3, 3, 6, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3], [0, 0, 3, 1, 8, 6, 0, 1, 3, 8, 0, 6, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [0, 0, 3, 1, 8, 6, 0, 1, 3, 8, 0, 6, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8, 8, 5, 8, 8, 3, 8], [0, 0, 3, 1, 8, 5, 0, 1, 3, 8, 0, 5, 0, 0, 3, 1, 0, 5, 0, 8, 3, 0, 0, 5, 8, 0, 3, 1], [3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3, 3, 5, 3, 3, 3, 3], [1, 1, 3, 1, 8, 5, 1, 1, 3, 8, 1, 5, 1, 1, 3, 1, 1, 5, 1, 8, 3, 1, 1, 5, 8, 1, 3, 1]], "output": [[6, 6, 6, 6, 6, 6, 6], [6, 0, 1, 3, 8, 0, 6], [6, 8, 8, 3, 8, 8, 6], [6, 3, 3, 3, 3, 3, 6], [6, 0, 1, 3, 8, 0, 6], [6, 0, 1, 3, 8, 0, 6], [6, 6, 6, 6, 6, 6, 6]]}], "test": [{"input": [[0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [1, 1, 1, 2, 3, 4, 1, 2, 1, 3, 1, 4, 1, 1, 3, 2, 1, 4, 1, 3, 1, 1, 1, 4], [2, 2, 2, 2, 3, 4, 2, 2, 2, 3, 2, 4, 2, 2, 3, 2, 2, 4, 2, 3, 2, 2, 2, 4], [3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [2, 2, 2, 2, 3, 4, 2, 2, 2, 3, 2, 4, 2, 2, 3, 2, 2, 4, 2, 3, 2, 2, 2, 4], [1, 1, 1, 2, 3, 4, 1, 2, 1, 3, 1, 4, 1, 1, 3, 2, 1, 4, 1, 3, 1, 1, 1, 4], [3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 8, 8, 8, 8, 8, 8, 3, 3, 3, 4], [2, 2, 2, 2, 3, 4, 2, 2, 2, 3, 2, 4, 2, 2, 8, 2, 2, 4, 2, 8, 2, 2, 2, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 8, 2, 0, 4, 0, 8, 1, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 8, 4, 4, 4, 4, 8, 4, 4, 4, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 8, 2, 0, 4, 0, 8, 1, 0, 0, 4], [3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 4, 3, 3, 8, 8, 8, 8, 8, 8, 3, 3, 3, 4], [1, 1, 1, 2, 3, 4, 1, 2, 1, 3, 1, 4, 1, 1, 3, 2, 1, 4, 1, 3, 1, 1, 1, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [0, 0, 1, 2, 3, 4, 0, 2, 1, 3, 0, 4, 0, 0, 3, 2, 0, 4, 0, 3, 1, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[8, 8, 8, 8, 8, 8], [8, 2, 2, 4, 2, 8], [8, 2, 0, 4, 0, 8], [8, 4, 4, 4, 4, 8], [8, 2, 0, 4, 0, 8], [8, 8, 8, 8, 8, 8]]}]}