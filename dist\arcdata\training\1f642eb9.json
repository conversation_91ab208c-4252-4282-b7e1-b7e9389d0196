{"train": [{"input": [[0, 0, 0, 0, 9, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [6, 0, 0, 0, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 9, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 9, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [6, 0, 0, 0, 6, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 4, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 2], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [3, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 0, 0, 6, 7, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 2, 0, 0, 0, 2], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [3, 0, 0, 3, 8, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 6], [3, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [2, 0, 0, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 7, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 8, 6, 0, 0, 0, 6], [3, 0, 0, 3, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 8, 0, 0, 0, 0], [2, 0, 0, 2, 8, 8, 0, 0, 0, 0], [0, 0, 0, 7, 8, 2, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 7, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 6, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 0, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 8, 8, 8, 8, 0, 0, 0, 7], [3, 0, 8, 8, 8, 8, 0, 0, 0, 0], [4, 0, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 6, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, 0, 9, 6, 8, 2, 0, 0, 0, 0], [0, 0, 8, 8, 8, 7, 0, 0, 0, 7], [3, 0, 3, 8, 8, 8, 0, 0, 0, 0], [4, 0, 4, 8, 6, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 6, 0, 0, 0, 0, 0]]}]}