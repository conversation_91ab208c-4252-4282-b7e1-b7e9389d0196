ARC AGI Testing Application - Spécifications Complètes
Vue d'ensemble du projet
Application web React + TypeScript + MUI pour tester automatiquement les puzzles ARC AGI avec multiple fournisseurs IA, incluant un mode demo pour l'enchaînement automatique des tests.

Architecture technique
Stack technologique
Frontend : React 19 + TypeScript + MUI v7
State Management : Redux Toolkit + RTK Query
Routing : React Router v7
Styling : MUI sx + Emotion
Database : SQLite (locale)
Configuration : Fichier .env pour clés API
Fournisseurs IA supportés
const providers = {
local: {
ollama: { endpoint: 'http://localhost:11434' },
lmstudio: { endpoint: 'http://localhost:1234' }
},
api: {
openrouter: { endpoint: 'https://openrouter.ai/api/v1', requiresKey: true },
groq: { endpoint: 'https://api.groq.com/openai/v1', requiresKey: true },
openai: { endpoint: 'https://api.openai.com/v1', requiresKey: true },
perplexity: { endpoint: 'https://api.perplexity.ai', requiresKey: true },
deepseek: { endpoint: 'https://api.deepseek.com/v1', requiresKey: true },
qwen: { endpoint: 'https://dashscope.aliyuncs.com/api/v1', requiresKey: true },
kimi: { endpoint: 'https://api.moonshot.cn/v1', requiresKey: true }
}
}
Interface utilisateur
Layout principal
┌─────────────────────────────────────────────────────────────┐
│ Header: [Puzzle Selector] [Navigation] [Random] [Demo] [Theme] │
├─────────────────────────────┬───────────────────────────────┤
│ │ ┌─ Chat ─┐ ┌─ Analyse ─┐ │
│ │ │ │ │ │ │
│ Grilles │ │ Messages│ │ Analysis │ │
│ (Input/Output) │ │ │ │ Data │ │
│ │ │ Input │ │ │ │
│ [☑] Afficher différences │ │ [Send] │ └───────────┘ │
│ [☑] Afficher valeurs │ └─────────┘ ┌─ Prompts ─┐ │
│ │ │ Template │ │
│ │ │ Editor │ │
│ │ └───────────┘ │
│ │ ┌─ Demo Progress ─────────┐ │
│ │ │ [■■■■■□□□□□] 50% │ │
│ │ │ 125/250 puzzles │ │
│ │ │ Success: 78% │ │
│ │ └────────────────────────┘ │
└───
Couleurs ARC officielles
:root {
--symbol-0-bg: #000000; --symbol-0-text: #ffffff; /_ Noir _/
--symbol-1-bg: #2563eb; --symbol-1-text: #ffffff; /_ Bleu _/
--symbol-2-bg: #dc2626; --symbol-2-text: #ffffff; /_ Rouge _/
--symbol-3-bg: #16a34a; --symbol-3-text: #ffffff; /_ Vert _/
--symbol-4-bg: #eab308; --symbol-4-text: #1e293b; /_ Jaune _/
--symbol-5-bg: #6b7280; --symbol-5-text: #ffffff; /_ Gris _/
--symbol-6-bg: #c026d3; --symbol-6-text: #ffffff; /_ Fuchsia _/
--symbol-7-bg: #ea580c; --symbol-7-text: #ffffff; /_ Orange _/
--symbol-8-bg: #0ea5e9; --symbol-8-text: #ffffff; /_ Teal _/
--symbol-9-bg: #7f1d1d; --symbol-9-text: #ffffff; /_ Marron _/
}

Types TypeScript principaux
interface ARCGrid {
grid: number[][];
width: number;
height: number;
}

interface ARCPuzzle {
id: string;
train: { input: ARCGrid; output: ARCGrid }[];
test: { input: ARCGrid; output?: ARCGrid }[];
}

interface ChatMessage {
id: string;
role: 'demo' | 'user' | 'chercheur' | 'system';
content: string;
timestamp: Date;
attachments?: {
grids?: ARCGrid[];
analysis?: PuzzleAnalysis;
visualGrid?: string;
};
metadata?: {
provider: string;
model: string;
tokens: number;
cost?: number;
responseTime: number;
validationResult?: ValidationResult;
};
}

interface ValidationResult {
isValid: boolean;
accuracy: number;
errorGrid?: boolean[][]; // Grille true/false des erreurs
feedback: string;
}

interface PuzzleAnalysis {
grid_info: any;
objects: any;
patterns: any;
transformations: any;
symmetries: any;
complexity: any;
colors: any;
spatial_relations: any;
line_uniformity: any;
diff_analysis: any;
enhanced_objects: any;
repeating_patterns: any;
}

// Types pour le mode demo
interface DemoMode {
isActive: boolean;
currentIndex: number;
totalPuzzles: number;
autoAdvanceDelay: number; // en secondes
selectedSubset: 'training' | 'evaluation' | 'both';
selectedProvider: string;
selectedModel: string;
stopOnError: boolean;
batchSize: number;
}

interface DemoSession {
id: string;
startTime: Date;
endTime?: Date;
puzzlesProcessed: number;
successCount: number;
failureCount: number;
averageResponseTime: number;
totalCost: number;
results: DemoResult[];
}

interface DemoResult {
puzzleId: string;
success: boolean;
responseTime: number;
cost: number;
accuracy: number;
errorCount: number;
attempts: number;
}

Fonctionnalités clés à implémenter

1. Composant ARCGridDisplay
   Affichage dynamique des grilles (taille optimisée)
   Support des différences (grille true/false)
   Option d'affichage des valeurs dans les cellules
   Côte à côte : Input + Diff (si coché) → Output
   interface ARCGridDisplayProps {
   grid: ARCGrid;
   showDiff?: boolean;
   diffGrid?: boolean[][];
   maxSize?: number;
   showValues?: boolean;
   }

const ARCGridDisplay: React.FC<ARCGridDisplayProps> = ({
grid, showDiff, diffGrid, maxSize = 300, showValues
}) => {
const cellSize = Math.min(maxSize / Math.max(grid.width, grid.height), 30);

return (

<div className="arc-grid" style={{
      display: 'grid',
      gridTemplateColumns: `repeat(${grid.width}, ${cellSize}px)`,
      gap: '1px',
      border: '2px solid #333'
    }}>
{grid.grid.flat().map((value, index) => {
const isError = showDiff && diffGrid?.[Math.floor(index / grid.width)]?.[index % grid.width] === false;
return (
<div
key={index}
className={`arc-cell ${isError ? 'error' : ''}`}
style={{
              width: cellSize,
              height: cellSize,
              backgroundColor: `var(--symbol-${value}-bg)`,
              color: `var(--symbol-${value}-text)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: showValues ? `${cellSize * 0.4}px` : '0'
            }} >
{showValues ? value : ''}
</div>
);
})}
</div>
);
};

2. Système de Chat
   Auto-injection de l'analyse du puzzle courant
   Templates de prompts multi-étapes :
   Analyse des exemples
   Test de la règle sur les exemples
   Proposition de solution uniquement si règle validée
   Validation automatique avec grille d'erreurs
   Retry automatique si solution invalide
   Format markdown pour les prompts avec grilles visuelles
3. Mode Demo - Enchaînement automatique des puzzles
   class DemoRunner {
   private demoSession: DemoSession;
   private currentPuzzleIndex: number = 0;
   private isPaused: boolean = false;
   private isRunning: boolean = false;

async startDemo(config: DemoMode) {
this.isRunning = true;
this.demoSession = this.initializeSession(config);

    const puzzles = await this.loadPuzzleSet(config.selectedSubset);

    for (let i = 0; i < puzzles.length && this.isRunning; i++) {
      if (this.isPaused) {
        await this.waitForResume();
      }

      const puzzle = puzzles[i];
      this.currentPuzzleIndex = i;

      // Mise à jour de l'UI
      this.updateProgressUI(i, puzzles.length);

      try {
        // Traitement automatique du puzzle
        const result = await this.processPuzzleAutomatically(puzzle, config);
        this.demoSession.results.push(result);

        // Attente avant le puzzle suivant
        if (config.autoAdvanceDelay > 0) {
          await this.delay(config.autoAdvanceDelay * 1000);
        }

      } catch (error) {
        console.error(`Erreur sur puzzle ${puzzle.id}:`, error);

        if (config.stopOnError) {
          this.stopDemo();
          break;
        }
      }
    }

    this.finalizeDemoSession();

}

async processPuzzleAutomatically(puzzle: ARCPuzzle, config: DemoMode): Promise<DemoResult> {
const startTime = Date.now();
let attempts = 0;
let success = false;
let accuracy = 0;
let errorCount = 0;

    // 1. Analyse automatique du puzzle
    const analysis = await this.arcAnalyzer.analyze(puzzle);

    // 2. Génération du prompt
    const prompt = this.generateAutoPrompt(puzzle, analysis);

    // 3. Envoi à l'IA avec retry automatique
    while (attempts < 3 && !success) {
      attempts++;

      try {
        const response = await this.sendToProvider(prompt, config);
        const solution = this.parseAISolution(response);
        const validation = this.validateSolution(solution, puzzle.test[0].output);

        success = validation.isValid;
        accuracy = validation.accuracy;
        errorCount = validation.errorGrid ?
          validation.errorGrid.flat().filter(cell => !cell).length : 0;

        if (!success && attempts < 3) {
          // Retry avec feedback d'erreur
          const feedbackPrompt = this.generateFeedbackPrompt(validation);
          prompt = feedbackPrompt;
        }

      } catch (error) {
        console.warn(`Tentative ${attempts} échouée:`, error);
      }
    }

    const responseTime = Date.now() - startTime;

    return {
      puzzleId: puzzle.id,
      success,
      responseTime,
      cost: this.calculateCost(config.selectedProvider, response?.tokens || 0),
      accuracy,
      errorCount,
      attempts
    };

}
} 4. Dashboard de suivi en temps réel
const DemoProgressDashboard = ({ demoSession }: { demoSession: DemoSession }) => {
const successRate = (demoSession.successCount / demoSession.puzzlesProcessed) \* 100;

return (
<Card>
<CardHeader title="Progression Demo" />
<CardContent>
<Grid container spacing={3}>
<Grid item xs={12} md={3}>
<Paper sx={{ p: 2, textAlign: 'center' }}>
<Typography variant="h4" color="primary">
{demoSession.puzzlesProcessed}
</Typography>
<Typography variant="body2">Puzzles traités</Typography>
</Paper>
</Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {successRate.toFixed(1)}%
              </Typography>
              <Typography variant="body2">Taux de réussite</Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4">
                {demoSession.averageResponseTime}ms
              </Typography>
              <Typography variant="body2">Temps moyen</Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                ${demoSession.totalCost.toFixed(4)}
              </Typography>
              <Typography variant="body2">Coût total</Typography>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <LinearProgress
              variant="determinate"
              value={(demoSession.puzzlesProcessed / 800) * 100}
              sx={{ height: 10, borderRadius: 5 }}
            />
            <Typography variant="body2" sx={{ mt: 1 }}>
              Puzzle {currentPuzzleIndex + 1} / {totalPuzzles}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>

);
};

5. Intégration ArcAnalyzer

# API Python existante à intégrer

class ARCAnalyzer:
def analyze_puzzle(self, puzzle: Dict) -> Dict[str, Any]:
return {
'grid_info': self.analyze_grid_structure(puzzle),
'objects': self.analyze_objects(puzzle),
'patterns': self.detect_patterns(puzzle),
'transformations': self.infer_transformations(puzzle),
'symmetries': self.find_symmetries(puzzle),
'complexity': self.measure_complexity(puzzle),
'colors': self.analyze_colors(puzzle),
'spatial_relations': self.analyze_spatial_relations(puzzle),
'line_uniformity': self.analyze_line_uniformity(puzzle),
'diff_analysis': self.analyze_differences(puzzle),
'enhanced_objects': self.analyze_objects_with_regionprops(puzzle),
'repeating_patterns': self.analyze_repeating_patterns(puzzle)
}

6. Gestion des erreurs
   Timeouts : Augmentation progressive des délais + retry
   Rate limits : Mise en pause automatique
   Parsing errors : Validation format JSON + feedback
7. Base de données SQLite
   CREATE TABLE chat_sessions (
   id TEXT PRIMARY KEY,
   puzzle_id TEXT,
   provider TEXT,
   model TEXT,
   messages TEXT, -- JSON
   metrics TEXT, -- JSON
   created_at DATETIME
   );

CREATE TABLE puzzle_cache (
puzzle_id TEXT PRIMARY KEY,
analysis TEXT,
cached_at DATETIME
);

CREATE TABLE provider_metrics (
provider TEXT,
model TEXT,
puzzle_id TEXT,
success_rate REAL,
avg_response_time INTEGER,
total_cost REAL,
updated_at DATETIME
);

CREATE TABLE demo_sessions (
id TEXT PRIMARY KEY,
config TEXT, -- JSON
results TEXT, -- JSON
start_time DATETIME,
end_time DATETIME,
status TEXT -- 'running', 'paused', 'completed', 'stopped'
);

8. Export de données
   JSON : Données brutes (conversations, métriques, résultats demo)
   HTML : Rapports visuels avec grilles colorées et graphiques
   Templates de prompts

const promptTemplates = {
analysis: `
Analysez ce puzzle ARC étape par étape :

1. **Analyse des exemples d'entraînement** :

   - Observez les patterns visuels
   - Identifiez les transformations
   - Formulez une règle hypothétique

2. **Validation sur les exemples** :

   - Testez votre règle sur chaque exemple
   - Vérifiez la cohérence
   - Ajustez si nécessaire

3. **Application au test** :
   - Appliquez la règle validée
   - Générez l'output au format JSON
   - Expliquez votre raisonnement

Grilles d'entraînement :
{training_grids}

Grille de test :
{test_input}
`,

validation: `
Votre solution précédente contient des erreurs.

Grille de différences (❌ = erreur, ✅ = correct) :
{error_grid_visual}

Erreurs détectées : {error_count}/{total_cells}

Réanalysez le puzzle en tenant compte de ces erreurs.
`,

demo: `
Mode automatique - Résolvez ce puzzle ARC en suivant la méthodologie :

1. Analysez les patterns dans les exemples d'entraînement
2. Formulez une règle de transformation
3. Validez cette règle sur tous les exemples
4. Si validation OK, appliquez au test
5. Retournez uniquement le JSON de la grille solution

Puzzle: {puzzle_id}
{training_examples}
Test input: {test_input}

Réponse attendue: JSON uniquement
`
}

const generateMarkdownPrompt = (puzzle: ARCPuzzle, analysis?: string) => {
const formatGridAsMarkdown = (grid: ARCGrid, title: string) => {
const symbols = ['⬛', '🟦', '🟥', '🟩', '🟨', '⬜', '🟪', '🟧', '🟦', '🟫'];
return `

### ${title}

\`\`\`
${grid.grid.map(row => row.map(cell => symbols[cell]).join('')).join('\n')}
\`\`\`
`;
};

let prompt = `# Puzzle ARC ${puzzle.id}\n\n`;

puzzle.train.forEach((example, i) => {
prompt += formatGridAsMarkdown(example.input, `Exemple ${i+1} - Input`);
prompt += formatGridAsMarkdown(example.output, `Exemple ${i+1} - Output`);
});

prompt += formatGridAsMarkdown(puzzle.test[0].input, 'Test - Input');

if (analysis) {
prompt += `\n## Analyse automatique\n${analysis}\n`;
}

return prompt;
};

Workflow principal
Mode normal
Chargement puzzle → Cache analysis si disponible
Sélection fournisseur → Configuration endpoint + modèle
Génération prompt → Template + analysis + grilles visuelles
Envoi IA → Gestion timeouts + rate limits
Parsing réponse → Extraction JSON + validation format
Validation solution → Comparaison + grille d'erreurs
Feedback/Retry → Si erreurs détectées
Sauvegarde → Session + métriques + export
Mode demo
Configuration demo → Subset, fournisseur, délais, options
Chargement batch → Liste des puzzles à traiter
Boucle automatique :
Analyse puzzle courant
Génération prompt automatique
Envoi IA + validation
Retry si échec (max 3 tentatives)
Sauvegarde résultat
Attente délai configuré
Passage au puzzle suivant
Suivi temps réel → Dashboard de progression
Contrôles → Pause/Resume/Stop
Finalisation → Rapport complet + export
Fonctionnalités Header
Sélecteur de puzzle + subset (training/evaluation)
Navigation (précédent/suivant/aléatoire)
Bouton Demo → Activation mode automatique
Toggle thème (sombre/clair/système)
Statistiques et métriques
Taux de réussite par fournisseur/modèle
Temps de réponse moyen
Coût par requête et total
Comparaisons entre versions/fournisseurs
Évolution des performances dans le temps
Analyse des échecs : types d'erreurs fréquentes
Instructions pour l'implémentation :

Utiliser MUI v7 avec thème personnalisé incluant les couleurs ARC
Responsive design (desktop + tablette)
Gestion d'état Redux Toolkit avec persistance
Composants réutilisables et modulaires
Tests unitaires pour les fonctions critiques
Mode demo comme fonctionnalité centrale de benchmarking
Interface temps réel pour le suivi des sessions demo
Export complet des données et rapports visuels
L'application devient ainsi un outil complet de benchmarking automatique des IA sur les puzzles ARC, avec capacité de traitement en lot et analyse comparative des performances.
