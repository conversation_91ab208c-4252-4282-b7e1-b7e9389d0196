# 🏗️ Architecture Pure en Niveaux - Système d'Analyse ARC AGI

## 📋 Vue d'Ensemble

Cette architecture définit une séparation stricte en 4 niveaux pour l'analyse des puzzles ARC AGI, garantissant la pureté des données et évitant les contaminations croisées.

## 🎯 Principe Fondamental

**Règle Absolue** : Chaque niveau ne peut utiliser que les données des niveaux inférieurs, sans jamais faire référence aux niveaux supérieurs.

```
Niveau 0 → Niveau 1 → Niveau 2 → Niveau 3
   ↑          ↑          ↑          ↑
Données    Analyses   Comparaisons Synthèse
Brutes     Dérivées   Input/Output Entraînement
```

---

## 📊 Niveau 0 : Données Brutes

### Responsabilité

Stockage des données factuelles pures, sans aucune interprétation ni calcul dérivé.

### Structure

```python
level_0 = {
    'input_grid': {
        'grid_array': np.ndarray,           # Grille brute input - SOURCE DE VÉRITÉ
        'width': int,                       # Largeur (nombre de colonnes)
        'height': int,                      # Hauteur (nombre de lignes)
        'value_frequencies': Dict[int, int] # Comptages complets {0:15, 1:1, 2:5, 3:14}
    },
    'output_grid': {
        'grid_array': np.ndarray,           # Grille brute output - SOURCE DE VÉRITÉ
        'width': int,                       # Largeur (nombre de colonnes)
        'height': int,                      # Hauteur (nombre de lignes)
        'value_frequencies': Dict[int, int] # Comptages complets {0:15, 1:1, 2:5, 3:14}
    }
}
```

### Caractéristiques

- ✅ **Pureté totale** : Aucune interprétation
- ✅ **Reproductibilité** : Même grille = même données
- ✅ **Atomicité** : Mesures les plus basiques possibles
- ❌ **Pas de comparaisons** : Aucune référence input/output
- ❌ **Pas de calculs dérivés** : Aucune analyse

---

## 🔍 Niveau 1 : Analyses Dérivées par Domaine

### Responsabilité

Analyse de chaque grille individuellement dans des domaines spécialisés, plus calcul conditionnel de la grille `diff`. Le Niveau 1 est décomposé en sous-niveaux pour une analyse structurelle approfondie.

### Structure

```python
level_1 = {
    'input_analysis': {
        'colors': {
            'present_colors': List[int],        # [0, 1, 2, 3]
            'dominant_color': int,              # Couleur la plus fréquente
            'background_color': int,            # Généralement 0
            'non_background_colors': List[int]  # Couleurs d'objets
        },
        'symmetries': {
            'horizontal': bool,         # Symétrie horizontale
            'vertical': bool,           # Symétrie verticale
            'diagonal_main': bool,      # Symétrie diagonale principale
            'diagonal_anti': bool,      # Symétrie diagonale anti
            'symmetry_count': int       # Nombre total de symétries
        },
        'geometric_detection': {
            # Niveau 1A : Détection géométrique pure
            'uniform_rows': [
                {'index': int, 'color': int, 'length': int}  # Lignes uniformes détectées
            ],
            'uniform_columns': [
                {'index': int, 'color': int, 'length': int}  # Colonnes uniformes détectées
            ],
            'uniform_main_diagonals': [
                {'start': (int, int), 'end': (int, int), 'color': int, 'length': int}
            ],
            'uniform_anti_diagonals': [
                {'start': (int, int), 'end': (int, int), 'color': int, 'length': int}
            ],
            'line_distribution': {
                'rows': int, 'columns': int, 'main_diagonals': int, 'anti_diagonals': int
            },
            'color_frequency_in_lines': Dict[int, int]  # Combien de lignes par couleur
        },
        'structural_classification': {
            # Niveau 1B : Typage fonctionnel des lignes
            'borders': {
                'top_border': {'exists': bool, 'row': int, 'color': int, 'complete': bool},
                'bottom_border': {'exists': bool, 'row': int, 'color': int, 'complete': bool},
                'left_border': {'exists': bool, 'column': int, 'color': int, 'complete': bool},
                'right_border': {'exists': bool, 'column': int, 'color': int, 'complete': bool},
                'border_completeness': str  # 'complete', 'partial', 'none'
            },
            'separators': [
                {
                    'type': str,  # 'vertical' | 'horizontal'
                    'position': int,
                    'color': int,
                    'spans_full_dimension': bool,
                    'divides_into_blocks': bool,
                    'left_block_width': int,  # Pour séparateurs verticaux
                    'right_block_width': int,
                    'top_block_height': int,  # Pour séparateurs horizontaux
                    'bottom_block_height': int
                }
            ],
            'grid_lines': {
                'has_grid_structure': bool,
                'vertical_grid_lines': List[int],  # Positions des lignes de grille
                'horizontal_grid_lines': List[int],
                'creates_regular_blocks': bool,
                'block_dimensions': (int, int),  # (hauteur, largeur) des blocs créés
                'grid_size': (int, int)  # (lignes, colonnes) de blocs
            },
            'structural_roles': {
                'containment_lines': List[int],  # Lignes qui "contiennent" (bordures)
                'division_lines': List[int],     # Lignes qui "divisent" (séparateurs/grille)
                'decoration_lines': List[int],   # Lignes purement décoratives
                'functional_line_ratio': float   # Ratio lignes fonctionnelles
            }
        },
        'block_analysis': {
            # Niveau 1C : Analyse des blocs résultants
            'detected_blocks': [
                {
                    'id': str,  # 'block_0_0'
                    'bounds': {'top': int, 'left': int, 'bottom': int, 'right': int},
                    'dimensions': (int, int),  # (hauteur, largeur)
                    'area': int,
                    'separated_by': List[str],  # Références aux séparateurs
                    'block_grid_array': np.ndarray,  # Sous-grille extraite du bloc
                    'block_statistics': {
                        'value_frequencies': Dict[int, int],  # Comptages dans ce bloc uniquement
                        'width': int,                         # Largeur du bloc
                        'height': int,                        # Hauteur du bloc
                        'total_pixels': int                   # Nombre total de pixels
                    },
                    'block_color_analysis': {
                        'present_colors': List[int],          # Couleurs présentes dans ce bloc
                        'dominant_color': int,                # Couleur dominante du bloc
                        'probable_background': int or None,   # Couleur de fond probable (la plus fréquente)
                        'non_background_colors': List[int],   # Autres couleurs du bloc
                        'color_diversity': int,               # Nombre de couleurs différentes
                        'background_ratio': float             # Ratio de la couleur de fond (0-1)
                    },
                    'block_uniformity': {
                        'is_uniform': bool,                   # Bloc entièrement uniforme
                        'uniform_color': int or None,         # Couleur si uniforme
                        'uniformity_ratio': float,            # Ratio de la couleur dominante
                        'has_pattern': bool,                  # Présence de motifs internes
                        'pattern_type': str or None           # Type de motif si détecté
                    },
                    'block_symmetries': {
                        'horizontal': bool,                   # Symétrie horizontale du bloc
                        'vertical': bool,                     # Symétrie verticale du bloc
                        'diagonal_main': bool,                # Symétrie diagonale principale
                        'diagonal_anti': bool,                # Symétrie diagonale anti
                        'symmetry_count': int                 # Nombre total de symétries
                    },
                    'block_pattern_hash': str,                # Hash unique du motif du bloc
                    'block_transformations': {
                        'rotations': {
                            'rot_90': np.ndarray,             # Bloc tourné 90°
                            'rot_180': np.ndarray,            # Bloc tourné 180°
                            'rot_270': np.ndarray             # Bloc tourné 270°
                        },
                        'flips': {
                            'flip_horizontal': np.ndarray,    # Bloc retourné horizontalement
                            'flip_vertical': np.ndarray,      # Bloc retourné verticalement
                            'flip_main_diagonal': np.ndarray, # Bloc retourné diagonale principale
                            'flip_anti_diagonal': np.ndarray  # Bloc retourné diagonale anti
                        },
                        'transformation_hashes': {
                            'original': str,                  # Hash du bloc original
                            'rot_90': str,                    # Hash après rotation 90°
                            'rot_180': str,                   # Hash après rotation 180°
                            'rot_270': str,                   # Hash après rotation 270°
                            'flip_h': str,                    # Hash après flip horizontal
                            'flip_v': str,                    # Hash après flip vertical
                            'flip_main_d': str,               # Hash après flip diagonale principale
                            'flip_anti_d': str                # Hash après flip diagonale anti
                        }
                    }
                }
            ],
            'block_uniformity': {
                'all_same_size': bool,
                'uniform_dimensions': (int, int),
                'size_variations': List[Dict],
                'total_blocks': int
            },
            'block_content_patterns': {
                'uniform_blocks': List[str],              # IDs des blocs uniformes
                'patterned_blocks': List[str],            # IDs des blocs avec motifs
                'diverse_blocks': List[str],              # IDs des blocs très variés
                'background_consistency': {
                    'same_background_across_blocks': bool,  # Même fond dans tous les blocs
                    'common_background_color': int or None, # Couleur de fond commune
                    'background_variations': Dict[str, int] # Variations par bloc
                },
                'content_similarity': {
                    'identical_blocks': List[List[str]],    # Groupes de blocs identiques
                    'similar_blocks': List[List[str]],      # Groupes de blocs similaires
                    'unique_blocks': List[str]              # Blocs uniques
                },
                'pattern_relationships': {
                    'same_pattern_groups': List[List[str]], # Groupes avec même motif (hash identique)
                    'rotation_groups': [                    # Groupes liés par rotation
                        {
                            'base_block': str,              # Bloc de référence
                            'rotated_variants': List[Dict], # [{'block_id': str, 'rotation': str}]
                            'rotation_type': str            # '90', '180', '270', 'multiple'
                        }
                    ],
                    'flip_groups': [                        # Groupes liés par flip
                        {
                            'base_block': str,              # Bloc de référence
                            'flipped_variants': List[Dict], # [{'block_id': str, 'flip_type': str}]
                            'flip_type': str                # 'horizontal', 'vertical', 'diagonal', 'multiple'
                        }
                    ],
                    'transformation_matrix': Dict[str, Dict[str, str]], # {block1: {block2: 'rot_90'}}
                    'pattern_families': [                   # Familles de motifs liés par transformations
                        {
                            'family_id': str,               # ID unique de la famille
                            'base_pattern': str,            # Hash du motif de base
                            'member_blocks': List[str],     # Blocs membres de cette famille
                            'transformations': Dict[str, str] # {block_id: transformation_needed}
                        }
                    ]
                },
                'anomaly_detection': {
                    # Détection d'anomalies et de patterns séquentiels entre blocs
                    'color_frequency_analysis': {
                        'per_color_counts': Dict[int, List[int]], # {color: [count_block0, count_block1, ...]}
                        'color_progressions': [              # Progressions détectées par couleur
                            {
                                'color': int,                # Couleur analysée
                                'sequence': List[int],       # Séquence des comptages [1, 2, 3, 4]
                                'progression_type': str,     # 'arithmetic', 'geometric', 'constant', 'irregular'
                                'step': int or float,        # Pas de la progression si régulière
                                'missing_blocks': List[str], # Blocs qui cassent la progression
                                'anomaly_blocks': List[str]  # Blocs avec comptage anormal
                            }
                        ],
                        'total_pixel_progressions': {
                            'sequence': List[int],           # Séquence des totaux de pixels non-fond
                            'progression_type': str,         # Type de progression globale
                            'step': int or float,            # Pas de la progression
                            'anomaly_blocks': List[str]      # Blocs avec total anormal
                        }
                    },
                    'color_presence_analysis': {
                        'color_universality': Dict[int, Dict], # {color: {'present_in': [blocks], 'absent_from': [blocks]}}
                        'missing_color_patterns': [          # Patterns de couleurs manquantes
                            {
                                'missing_color': int,        # Couleur manquante
                                'present_in_blocks': List[str], # Blocs qui l'ont
                                'absent_from_blocks': List[str], # Blocs qui ne l'ont pas
                                'pattern_type': str,         # 'single_missing', 'multiple_missing', 'systematic'
                                'anomaly_severity': str      # 'minor', 'major', 'critical'
                            }
                        ],
                        'color_completeness_score': float,   # Score de complétude des couleurs (0-1)
                        'expected_vs_actual': {
                            'expected_colors': List[int],    # Couleurs attendues dans tous les blocs
                            'actual_distribution': Dict[str, List[int]], # {block_id: [colors_present]}
                            'deviation_blocks': List[str]   # Blocs qui dévient de l'attendu
                        }
                    },
                    'sequential_anomalies': {
                        'block_order_analysis': {
                            'ordered_by_position': List[str], # Blocs ordonnés par position (gauche->droite, haut->bas)
                            'sequential_patterns': [         # Patterns séquentiels détectés
                                {
                                    'pattern_type': str,     # 'color_increase', 'pattern_rotation', 'complexity_growth'
                                    'sequence_description': str, # Description du pattern
                                    'conforming_blocks': List[str], # Blocs qui suivent le pattern
                                    'breaking_blocks': List[str],   # Blocs qui cassent le pattern
                                    'expected_next': Dict,   # Prédiction pour le bloc suivant
                                    'confidence': float      # Confiance dans le pattern (0-1)
                                }
                            ]
                        },
                        'outlier_detection': {
                            'statistical_outliers': [       # Blocs statistiquement anormaux
                                {
                                    'block_id': str,
                                    'anomaly_type': str,     # 'color_count', 'pattern_complexity', 'size_deviation'
                                    'expected_value': float, # Valeur attendue
                                    'actual_value': float,   # Valeur réelle
                                    'deviation_score': float, # Score de déviation (z-score)
                                    'severity': str          # 'mild', 'moderate', 'severe'
                                }
                            ],
                            'pattern_outliers': [           # Blocs avec motifs anormaux
                                {
                                    'block_id': str,
                                    'anomaly_description': str, # Description de l'anomalie
                                    'expected_pattern': str,    # Pattern attendu (hash)
                                    'actual_pattern': str,      # Pattern réel (hash)
                                    'similarity_score': float   # Similarité avec l'attendu (0-1)
                                }
                            ]
                        }
                    },
                    'completion_predictions': {
                        # Prédictions pour compléter les patterns détectés
                        'missing_elements': [               # Éléments manquants prédits
                            {
                                'target_block': str,        # Bloc à modifier
                                'missing_type': str,        # 'color', 'pattern', 'count'
                                'predicted_addition': Dict, # Ce qui devrait être ajouté
                                'confidence': float,        # Confiance dans la prédiction
                                'reasoning': str            # Explication du raisonnement
                            }
                        ],
                        'sequence_completions': [           # Complétions de séquences
                            {
                                'sequence_type': str,       # Type de séquence à compléter
                                'current_sequence': List,   # Séquence actuelle
                                'predicted_next': List,     # Éléments suivants prédits
                                'completion_confidence': float # Confiance dans la complétion
                            }
                        ]
                    }
                }
            },
            'logical_operations_analysis': {
                # Analyse des opérations logiques possibles entre blocs de même taille
                'same_size_pairs': [                        # Paires de blocs de même taille
                    {
                        'block1': str,
                        'block2': str,
                        'dimensions': (int, int),
                        'logical_results': {
                            'xor_result': np.ndarray,       # block1 XOR block2
                            'and_result': np.ndarray,       # block1 AND block2
                            'or_result': np.ndarray,        # block1 OR block2
                            'xor_hash': str,                # Hash du résultat XOR
                            'and_hash': str,                # Hash du résultat AND
                            'or_hash': str                  # Hash du résultat OR
                        },
                        'operation_properties': {
                            'xor_uniform': bool,            # Résultat XOR uniforme
                            'and_uniform': bool,            # Résultat AND uniforme
                            'or_uniform': bool,             # Résultat OR uniforme
                            'xor_empty': bool,              # XOR donne grille vide (0)
                            'and_empty': bool,              # AND donne grille vide (0)
                            'or_matches_block1': bool,      # OR identique à block1
                            'or_matches_block2': bool       # OR identique à block2
                        }
                    }
                ],
                'operation_patterns': {
                    'consistent_xor_results': List[str],    # Hashs XOR qui apparaissent souvent
                    'consistent_and_results': List[str],    # Hashs AND qui apparaissent souvent
                    'consistent_or_results': List[str],     # Hashs OR qui apparaissent souvent
                    'operation_symmetries': {
                        'xor_symmetric_pairs': List[Tuple[str, str]], # Paires où XOR est symétrique
                        'and_commutative': bool,            # AND commutatif pour tous les blocs
                        'or_commutative': bool              # OR commutatif pour tous les blocs
                    }
                },
                'result_block_matching': {
                    # Vérifier si les résultats d'opérations correspondent à d'autres blocs
                    'xor_matches_existing': List[Dict],     # [{'operation': (block1, block2), 'matches': block3}]
                    'and_matches_existing': List[Dict],
                    'or_matches_existing': List[Dict],
                    'operation_chains': List[Dict]          # Chaînes d'opérations possibles
                }
            },
            'block_relationships': {
                'adjacent_pairs': [
                    {'block1': str, 'block2': str, 'shared_edge': str}
                ],
                'isolation_level': str  # 'isolated', 'border_separated', 'grid_separated'
            }
        },
        'objects': {
            # Approche "table vue du dessus" - objets manipulables posés sur une surface
            'table_analysis': {
                'probable_background': int,         # Couleur de fond probable (table)
                'background_confidence': float,     # Confiance dans la détection du fond
                'non_background_colors': List[int], # Couleurs d'objets potentiels
                'table_coverage': float,            # Ratio de la table couverte par le fond
                'noise_pixels': int                 # Pixels isolés (bruit potentiel)
            },
            'detected_objects': [
                {
                    'object_id': str,               # ID unique de l'objet
                    'color': int,                   # Couleur principale de l'objet
                    'multi_color': bool,            # Objet multi-couleur
                    'color_composition': Dict[int, int], # {couleur: nombre_pixels} si multi-couleur

                    # Propriétés géométriques
                    'area': int,                    # Nombre de pixels
                    'center': [float, float],       # Centre de masse [x, y]
                    'bbox': [int, int, int, int],   # Boîte englobante [x1, y1, x2, y2]
                    'width': int,                   # Largeur boîte englobante
                    'height': int,                  # Hauteur boîte englobante
                    'aspect_ratio': float,          # width/height
                    'compactness': float,           # 4π*area/perimeter²

                    # Forme et structure
                    'shape_classification': {
                        'basic_shape': str,         # 'square', 'rectangle', 'line', 'L_shape', 'T_shape', 'cross', 'irregular'
                        'shape_confidence': float,  # Confiance dans la classification
                        'symmetries': {
                            'horizontal': bool,
                            'vertical': bool,
                            'diagonal_main': bool,
                            'diagonal_anti': bool,
                            'rotational_4': bool,   # Symétrie rotation 90°
                            'rotational_2': bool    # Symétrie rotation 180°
                        }
                    },

                    # Représentation pour manipulation
                    'object_matrix': np.ndarray,    # Matrice de l'objet extrait (fond = 0)
                    'object_mask': np.ndarray,      # Masque binaire de l'objet
                    'object_hash': str,             # Hash unique du motif
                    'normalized_hash': str,         # Hash normalisé (position-indépendant)

                    # Données pour comparaisons futures (Niveau 2)
                    'transformation_variants': {
                        # Variants précalculés pour comparaisons rapides au Niveau 2
                        'rotations': {
                            'rot_90': np.ndarray,    # Objet tourné 90° (pour comparaison)
                            'rot_180': np.ndarray,   # Objet tourné 180° (pour comparaison)
                            'rot_270': np.ndarray    # Objet tourné 270° (pour comparaison)
                        },
                        'flips': {
                            'flip_horizontal': np.ndarray, # Objet retourné H (pour comparaison)
                            'flip_vertical': np.ndarray    # Objet retourné V (pour comparaison)
                        },
                        'variant_hashes': {
                            # Hashs des variants pour comparaisons rapides
                            'original': str,
                            'rot_90': str,
                            'rot_180': str,
                            'rot_270': str,
                            'flip_h': str,
                            'flip_v': str
                        }
                    },

                    # Position sur la table
                    'table_position': {
                        'absolute_position': [int, int], # Position absolue sur la table
                        'relative_position': str,        # 'top_left', 'center', 'bottom_right', etc.
                        'distance_to_edges': {
                            'top': int, 'bottom': int, 'left': int, 'right': int
                        },
                        'touching_edges': List[str],     # Bords de la table touchés
                        'corner_proximity': str or None  # 'top_left', 'top_right', etc. si proche
                    }
                }
            ],

            # Analyse des relations entre objets
            'object_relationships': {
                'spatial_relationships': [
                    {
                        'object1': str,
                        'object2': str,
                        'relationship': str,        # 'adjacent', 'overlapping', 'separated', 'aligned'
                        'distance': float,          # Distance entre centres
                        'relative_position': str,   # 'above', 'below', 'left', 'right', 'diagonal'
                        'alignment': {
                            'horizontal': bool,     # Alignés horizontalement
                            'vertical': bool,       # Alignés verticalement
                            'diagonal': bool        # Alignés en diagonale
                        }
                    }
                ],
                'pattern_similarities': [
                    {
                        'objects': List[str],       # Objets avec patterns similaires
                        'similarity_type': str,     # 'identical_hash', 'variant_match', 'partial_match'
                        'matching_variants': List[str], # Quels variants matchent entre objets
                        'similarity_score': float   # Score de similarité (0-1)
                    }
                ]
            },

            # Points d'ancrage et zones spéciales
            'anchor_analysis': {
                'possible_anchor_points': [         # Points rouges (couleur 2) = ancrages possibles
                    {
                        'position': [int, int],     # Position [ligne, colonne] du point
                        'bounds': [int, int, int, int], # [ligne1, col1, ligne2, col2] si zone
                        'size': int,                # Nombre de pixels rouges connexes
                        'shape': str,               # 'single_pixel', 'small_cluster', 'line', 'cross'
                        'nearby_objects': List[str], # Objets proches de ce point possible
                        'location_type': str        # 'corner', 'edge', 'center', 'isolated'
                    }
                ],
                'empty_spaces': [                   # Zones vides détectées
                    {
                        'position': [int, int],
                        'size': int,                # Taille de la zone vide
                        'space_type': str,          # 'near_anchor', 'isolated', 'between_objects'
                        'dimensions': [int, int]    # Largeur, hauteur de l'espace
                    }
                ]
            },

            # Statistiques globales
            'object_statistics': {
                'total_objects': int,
                'objects_by_color': Dict[int, int], # {couleur: nombre_objets}
                'objects_by_size': Dict[str, int],  # {'small': n, 'medium': n, 'large': n}
                'objects_by_shape': Dict[str, int], # {'square': n, 'line': n, etc.}
                'table_occupancy': float,           # Ratio de la table occupée par objets
                'object_density': float,            # Objets par unité de surface
                'clustering_coefficient': float     # Mesure de regroupement des objets
            }
        },
        'repeating_patterns': {
            'pattern_count': int,                           # Nombre de motifs répétitifs
            'patterns': Dict[str, int],                     # {pattern_hash: occurrences}
            'max_occurrences': int,                         # Maximum d'occurrences
            'pattern_complexity': str,                      # 'none', 'low', 'medium', 'high'
            'dominant_pattern_size': int,                   # Taille du motif le plus fréquent
            'pattern_distribution': Dict[int, int]          # {taille: nombre_de_motifs}
        },
        'spatial_properties': {
            'object_density': float,                    # Densité d'objets (objets/cellules)
            'background_ratio': float,                  # Ratio de fond (0-1)
            'filled_ratio': float,                      # Ratio de cellules non-vides
            'center_of_mass': [float, float],           # Centre de masse de tous les objets
            'bounding_box_coverage': float,             # Couverture des boîtes englobantes
            'spatial_distribution': str                 # 'clustered', 'dispersed', 'uniform'
        }
    },
    'output_analysis': {
        # Structure identique à input_analysis
        'colors': {...},
        'symmetries': {...},
        'geometric_detection': {...},
        'structural_classification': {...},
        'block_analysis': {...},
        'objects': {...},
        'repeating_patterns': {...},
        'spatial_properties': {...}
    },
    'transformation_analysis': {
        'dimension_compatibility': {
            'same_dimensions': bool,                    # input.width == output.width and input.height == output.height
            'width_change': int,                        # output.width - input.width
            'height_change': int,                       # output.height - input.height
            'scaling_factor': float or None,            # Facteur d'échelle si applicable
            'transformation_type': str                  # 'same_size' | 'resize' | 'crop' | 'expand'
        },
        'diff_grid': {
            'exists': bool,                             # True si dimensions identiques
            'grid_array': np.ndarray or None,           # Grille de changements logiques
            'change_ratio': float or None,              # Ratio de cellules changées
            'change_positions': List[Tuple] or None,    # [(x,y), ...] positions changements
            'unchanged_positions': List[Tuple] or None, # [(x,y), ...] positions inchangées
            'diff_analysis': {                          # Analyse complète de la grille diff
                'colors': {...} or None,                # Couleurs des changements
                'symmetries': {...} or None,            # Symétries des changements
                'geometric_detection': {                # Détection géométrique des changements
                    'change_lines': List[Dict],         # Lignes de changements uniformes
                    'change_distribution': Dict,        # Distribution des changements
                    'change_clustering': str            # 'clustered', 'dispersed', 'linear'
                } or None,
                'structural_classification': {          # Classification des changements
                    'addition_lines': List[Dict],       # Lignes où couleurs sont ajoutées
                    'substitution_lines': List[Dict],   # Lignes où couleurs sont remplacées
                    'change_borders': List[Dict],       # Changements formant bordures
                    'change_separators': List[Dict],    # Changements créant séparations
                    'change_fills': List[Dict]          # Zones remplies
                } or None,
                'block_analysis': {                     # Analyse des blocs de changements
                    'contiguous_change_regions': List[Dict],  # Régions connexes de changements
                    'change_isolation': str,            # 'clustered', 'linear', 'scattered'
                    'preservation_blocks': List[Dict]   # Blocs entièrement préservés
                } or None,
                'objects': {...} or None,               # "Objets de changement"
                'repeating_patterns': {...} or None,    # Patterns dans les changements
                'spatial_properties': {...} or None     # Propriétés spatiales des changements
            }
        }
    }
}
```

### Formule Fondamentale

```python
# Si input.shape == output.shape:
input + diff = output
# Donc (logique de changement, pas arithmétique) :
diff[i,j] = 0 si input[i,j] == output[i,j]  # Pas de changement
diff[i,j] = output[i,j] si input[i,j] != output[i,j]  # Changement vers cette couleur

# Sinon:
diff = None  # Information cruciale !
```

### Caractéristiques

- ✅ **Domaines séparés** : Chaque analyse reste dans son domaine
- ✅ **Sous-niveaux structurés** : 1A (détection), 1B (classification), 1C (blocs)
- ✅ **Grille diff logique** : 0 = pas de changement, couleur = changement vers cette couleur
- ✅ **Grille diff conditionnelle** : Calculée seulement si dimensions identiques
- ✅ **Information d'absence** : `diff = None` est une donnée importante
- ✅ **Analyse complète de diff** : Même structure d'analyse que input/output
- ❌ **Pas de corrélations croisées** : Aucune référence entre domaines
- ❌ **Pas de comparaisons inter-exemples** : Analyse exemple par exemple

---

## 🔄 Niveau 2 : Comparaisons Input/Output par Exemple

### Responsabilité

Comparaison des analyses du Niveau 1 entre input et output pour un exemple donné.

### Structure

```python
level_2 = {
    'transformations_by_domain': {
        'colors': {
            'added_colors': List[int],              # Couleurs ajoutées
            'removed_colors': List[int],            # Couleurs supprimées
            'preserved_colors': List[int],          # Couleurs conservées
            'color_count_change': int,              # Différence nombre de couleurs
            'dominant_color_change': [int, int],    # [ancien, nouveau] dominant
            'background_change': bool               # Changement de fond
        },
        'symmetries': {
            'lost_symmetries': List[str],           # ['vertical', 'horizontal']
            'gained_symmetries': List[str],         # Symétries acquises
            'preserved_symmetries': List[str],      # Symétries conservées
            'symmetry_count_change': int,           # Différence nombre symétries
            'symmetry_stability': str               # 'stable', 'increased', 'decreased'
        },
        'structural_transformations': {
            'geometric_changes': {
                'line_count_changes': Dict[str, int],   # Changements par type de ligne
                'new_uniform_lines': Dict[str, List],   # Nouvelles lignes uniformes
                'lost_uniform_lines': Dict[str, List],  # Lignes uniformes perdues
                'line_evolution': str                   # 'increasing', 'decreasing', 'stable'
            },
            'classification_changes': {
                'border_changes': Dict[str, Dict],      # Changements de bordures
                'separator_changes': List[Dict],        # Changements de séparateurs
                'grid_structure_change': Dict,          # Évolution structure de grille
                'functional_role_shifts': List[Dict]    # Changements de rôles fonctionnels
            },
            'block_transformations': {
                'block_count_change': int,              # Différence nombre de blocs
                'block_size_changes': List[Dict],       # Changements de tailles
                'block_content_changes': List[Dict],    # Changements de contenus
                'block_relationship_changes': List[Dict], # Changements de relations
                'isolation_level_change': str           # Évolution du niveau d'isolation
            }
        },
        'objects': {
            'object_count_change': int,             # Différence nombre d'objets
            'size_changes': List[Dict],             # Changements de taille par objet
            'position_changes': List[Dict],         # Changements de position par objet
            'color_changes': List[Dict],            # Changements de couleur par objet
            'new_objects': List[Dict],              # Objets apparus
            'disappeared_objects': List[Dict],      # Objets disparus
            'transformation_type': str              # 'creation', 'deletion', 'modification'
        },
        'repeating_patterns': {
            'pattern_count_change': int,            # Différence nombre de patterns
            'new_patterns': Dict[str, int],         # Nouveaux patterns
            'lost_patterns': Dict[str, int],        # Patterns disparus
            'pattern_evolution': str,               # 'generation', 'elimination', 'stable'
            'complexity_change': str                # 'increased', 'decreased', 'stable'
        },
        'spatial_properties': {
            'density_change': float,                # Changement de densité
            'distribution_change': str,             # Changement de distribution
            'center_of_mass_shift': [float, float], # Déplacement centre de masse
            'coverage_change': float                # Changement de couverture
        }
    },
    'diff_exploitation': {
        # Exploitation de la grille diff si elle existe
        'diff_available': bool,
        'pixel_level_analysis': {
            'change_locations': List[Tuple],        # Positions exactes des changements
            'change_values': Dict[Tuple, Tuple],    # {(x,y): (old_val, new_val)}
            'change_clustering': str,               # 'clustered', 'dispersed', 'linear'
            'change_symmetry': Dict[str, bool]      # Symétries des changements
        } or None,
        'transformation_formula': {
            'formula_type': str,                    # 'addition', 'substitution', 'complex'
            'formula_description': str,             # Description de input + diff = output
            'consistency_score': float              # Score de cohérence (0-1)
        } or None
    }
}
```

### Caractéristiques

- ✅ **Comparaisons factuelles** : Différences objectives entre input/output
- ✅ **Exploitation de diff** : Utilisation maximale de la grille diff si disponible
- ✅ **Formule input + diff = output** : Exploitation complète sans déborder sur niveau 3
- ❌ **Pas de synthèse multi-exemples** : Analyse exemple par exemple uniquement

---

## 📊 Niveau 3 : Synthèse par Entraînement

### Responsabilité

Synthèse des patterns récurrents à travers tous les exemples d'entraînement.

### Structure

```python
level_3 = {
    'training_patterns_by_domain': {
        'colors': {
            'consistent_additions': List[int],           # Couleurs toujours ajoutées
            'consistent_removals': List[int],            # Couleurs toujours supprimées
            'consistent_preservations': List[int],       # Couleurs toujours préservées
            'transformation_pattern': str,               # 'color_substitution', 'color_removal'
            'consistency_score': float,                  # Score de cohérence (0-1)
            'dominant_color_rule': str or None           # Règle sur couleur dominante
        },
        'symmetries': {
            'consistent_losses': List[str],              # Symétries toujours perdues
            'consistent_gains': List[str],               # Symétries toujours acquises
            'consistent_preservations': List[str],       # Symétries toujours préservées
            'symmetry_pattern': str,                     # 'vertical_loss', 'symmetry_increase'
            'stability_trend': str                       # 'always_stable', 'always_change'
        },
        'structural_patterns': {
            'geometric_consistency': {
                'consistent_line_changes': Dict[str, int],  # Changements constants par type
                'line_evolution_trend': str,                # 'always_increase', 'always_decrease'
                'pattern_regularity': str                   # 'highly_regular', 'irregular'
            },
            'classification_consistency': {
                'border_pattern': str,                      # Pattern constant des bordures
                'separator_pattern': str,                   # Pattern constant des séparateurs
                'grid_evolution_pattern': str,              # Pattern d'évolution de grille
                'functional_role_consistency': float        # Score de cohérence des rôles
            },
            'block_consistency': {
                'consistent_block_changes': Dict[str, Any], # Changements constants des blocs
                'block_transformation_pattern': str,        # Pattern de transformation
                'isolation_trend': str,                     # Tendance d'isolation
                'structural_stability': float               # Score de stabilité structurelle
            }
        },
        'objects': {
            'consistent_count_change': int or None,      # Changement constant nombre objets
            'consistent_transformations': List[str],     # Transformations toujours présentes
            'object_behavior_pattern': str,              # 'creation', 'modification', 'mixed'
            'size_change_pattern': str,                  # 'always_grow', 'always_shrink'
            'position_change_pattern': str               # 'always_move', 'always_static'
        },
        'repeating_patterns': {
            'pattern_evolution_consistency': str,        # 'always_generate', 'always_eliminate'
            'complexity_trend': str,                     # 'always_increase', 'always_decrease'
            'pattern_type_consistency': List[str]        # Types de patterns toujours présents
        },
        'spatial_properties': {
            'density_trend': str,                        # 'always_increase', 'always_decrease'
            'distribution_consistency': str,             # Distribution spatiale constante
            'coverage_trend': str                        # 'always_expand', 'always_contract'
        }
    },
    'diff_availability_patterns': {
        'diff_always_available': bool,                   # Toujours même dimensions
        'diff_never_available': bool,                    # Toujours redimensionnement
        'diff_conditional': bool,                        # Parfois disponible
        'dimension_change_pattern': str,                 # 'consistent_resize', 'mixed_changes'
        'transformation_strategy': str                   # 'pixel_level' vs 'structural'
    },
    'global_transformation_rule': {
        'rule_type': str,                               # 'geometric', 'color_based', 'pattern_based'
        'rule_description': str,                        # Description de la règle globale
        'confidence_score': float,                      # Confiance dans la règle (0-1)
        'exceptions': List[int]                         # Indices des exemples non conformes
    }
}
```

### Caractéristiques

- ✅ **Synthèse multi-exemples** : Patterns à travers tout l'entraînement
- ✅ **Règles globales** : Hypothèses sur la règle du puzzle
- ✅ **Scores de confiance** : Évaluation de la cohérence des patterns
- ✅ **Gestion des exceptions** : Identification des cas non conformes

---

## 🛡️ Règles de Pureté Architecturale

### Interdictions Strictes

#### Niveau 0

- ❌ **Aucun calcul dérivé** : Seulement stockage de données brutes
- ❌ **Aucune comparaison** : Pas de référence input/output
- ❌ **Aucune interprétation** : Pas d'analyse des données

#### Niveau 1

- ❌ **Pas de corrélations croisées** : Chaque domaine reste isolé
- ❌ **Pas de comparaisons inter-exemples** : Analyse exemple par exemple
- ❌ **Pas de synthèse** : Pas de patterns multi-exemples

#### Niveau 2

- ❌ **Pas de synthèse multi-exemples** : Comparaisons exemple par exemple uniquement
- ❌ **Pas de règles globales** : Pas d'hypothèses sur la règle du puzzle

#### Niveau 3

- ❌ **Pas de nouvelles données brutes** : Utilise uniquement les niveaux inférieurs

### Autorisations par Niveau

#### Niveau 0 → Niveau 1

- ✅ Utilisation des données brutes pour calculs dérivés
- ✅ Analyses spécialisées par domaine

#### Niveau 1 → Niveau 2

- ✅ Comparaisons entre analyses input/output
- ✅ Exploitation de la grille diff

#### Niveau 2 → Niveau 3

- ✅ Synthèse des comparaisons multi-exemples
- ✅ Détection de patterns récurrents

---

## 🎯 Avantages de cette Architecture

### 1. Pureté des Données

- **Niveau 0** : Données factuelles incontestables
- **Niveau 1** : Analyses reproductibles et testables
- **Niveau 2** : Comparaisons objectives
- **Niveau 3** : Synthèses basées sur des faits

### 2. Séparation des Responsabilités

- Chaque niveau a un rôle précis et limité
- Pas de contamination croisée entre niveaux
- Évolution indépendante de chaque niveau

### 3. Debuggabilité

- Traçabilité claire des données
- Tests isolés par niveau
- Identification facile des sources d'erreur

### 4. Évolutivité

- Ajout facile de nouveaux domaines au Niveau 1
- Extension des analyses sans impact sur les autres niveaux
- Architecture modulaire et flexible

### 5. Exploitation Maximale de la Grille Diff

- Calcul conditionnel intelligent avec logique de changement
- Information cruciale de l'absence
- Formule `input + diff = output` exploitée au maximum
- Analyse structurelle complète des changements

### 6. Architecture Structurelle Avancée

- Détection géométrique pure (Niveau 1A)
- Classification fonctionnelle des éléments (Niveau 1B)
- Analyse des blocs et relations (Niveau 1C)
- Préparation optimale pour les comparaisons du Niveau 2

---

## 📋 Checklist de Validation

### Avant d'ajouter du code :

#### Niveau 0

- [ ] Les données sont-elles purement factuelles ?
- [ ] Aucun calcul dérivé n'est-il effectué ?
- [ ] Aucune comparaison input/output n'est-elle faite ?

#### Niveau 1

- [ ] L'analyse reste-t-elle dans un seul domaine ?
- [ ] Les sous-niveaux 1A/1B/1C sont-ils respectés ?
- [ ] Aucune corrélation croisée n'est-elle effectuée ?
- [ ] La grille diff est-elle calculée avec la logique de changement ?
- [ ] La grille diff est-elle analysée avec la même structure ?

#### Niveau 2

- [ ] Les comparaisons sont-elles factuelles ?
- [ ] L'exploitation de diff reste-t-elle au niveau exemple ?
- [ ] Aucune synthèse multi-exemples n'est-elle faite ?

#### Niveau 3

- [ ] La synthèse utilise-t-elle uniquement les niveaux inférieurs ?
- [ ] Les patterns sont-ils basés sur des faits ?
- [ ] Les règles globales sont-elles justifiées ?

---

**Cette architecture garantit une analyse pure, reproductible et évolutive des puzzles ARC AGI, avec une exploitation maximale de l'information de transformation via la grille diff.**
