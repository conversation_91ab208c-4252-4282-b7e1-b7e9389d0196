{"train": [{"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 8, 8, 8, 8, 1, 8, 1, 1, 8, 1, 1, 1, 1, 1, 1], [1, 8, 2, 2, 8, 1, 1, 1, 8, 8, 8, 8, 1, 1, 8, 1], [1, 8, 2, 8, 8, 8, 1, 1, 8, 2, 2, 8, 1, 1, 1, 1], [1, 8, 8, 8, 8, 1, 1, 1, 8, 2, 8, 8, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 1, 1, 8, 8, 8, 8, 8, 1, 1, 1], [1, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1], [1, 1, 8, 8, 8, 8, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 8, 2, 2, 8, 1, 1, 1, 1, 8, 1, 1, 8, 1, 1], [1, 1, 8, 2, 8, 8, 1, 1, 8, 8, 8, 8, 1, 1, 1, 1], [8, 1, 8, 8, 8, 8, 1, 1, 8, 2, 2, 8, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 2, 8, 1, 1, 8, 1], [1, 1, 1, 1, 1, 1, 8, 1, 8, 8, 8, 8, 1, 1, 1, 1], [1, 1, 8, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 8, 1, 1, 8, 8, 1], [1, 8, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1]], "output": [[8, 8, 8, 8], [8, 2, 2, 8], [8, 8, 2, 8], [8, 8, 8, 8]]}, {"input": [[8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 8, 1, 8, 1, 1, 1], [1, 8, 8, 8, 8, 1, 8, 1, 1, 8, 8, 1, 1, 1, 1, 1], [1, 8, 2, 2, 8, 8, 1, 1, 8, 1, 1, 8, 8, 1, 1, 1], [1, 8, 2, 2, 8, 1, 1, 8, 1, 1, 1, 8, 1, 1, 8, 1], [1, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 8, 1], [1, 8, 1, 8, 1, 1, 1, 8, 1, 1, 8, 1, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 2, 2, 8], [1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 8, 2, 2, 8], [1, 8, 1, 1, 1, 8, 2, 8, 8, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 8, 8, 2, 8, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 8, 8, 8, 8], [1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 8, 2, 2, 8], [1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 8, 2, 2, 8], [1, 1, 8, 1, 1, 8, 1, 1, 1, 1, 8, 1, 8, 8, 8, 8]], "output": [[8, 8, 8, 8], [8, 2, 8, 8], [8, 8, 2, 8], [8, 8, 8, 8]]}, {"input": [[1, 8, 1, 8, 1, 1, 1, 8, 1, 1, 8, 1, 1, 1, 1, 8, 8, 1], [1, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 8, 1], [8, 8, 2, 8, 2, 8, 1, 1, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1], [1, 8, 8, 2, 8, 8, 8, 1, 1, 8, 2, 8, 2, 8, 8, 1, 1, 1], [8, 8, 2, 2, 8, 8, 1, 1, 1, 8, 8, 2, 8, 8, 1, 8, 1, 1], [1, 8, 8, 8, 8, 8, 1, 1, 1, 8, 2, 2, 8, 8, 8, 1, 1, 1], [1, 8, 1, 8, 1, 1, 8, 1, 1, 8, 8, 8, 8, 8, 8, 1, 8, 8], [8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1], [1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 1], [8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 1, 8, 1, 1, 1, 1], [8, 8, 1, 1, 1, 8, 2, 8, 2, 8, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 8, 1, 1, 8, 8, 2, 8, 8, 1, 1, 1, 1, 1, 1, 1, 8], [1, 1, 1, 1, 1, 8, 8, 2, 2, 8, 1, 1, 8, 8, 1, 1, 8, 1], [1, 8, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 8, 1], [1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 1], [1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1, 1, 1], [1, 8, 1, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[8, 8, 8, 8, 8], [8, 2, 8, 2, 8], [8, 8, 2, 8, 8], [8, 8, 2, 2, 8], [8, 8, 8, 8, 8]]}], "test": [{"input": [[1, 1, 1, 1, 8, 1, 1, 1, 1, 8, 1, 1, 1, 8, 8, 8, 8, 8], [1, 1, 1, 8, 1, 1, 8, 1, 1, 8, 1, 1, 8, 8, 2, 2, 8, 8], [1, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 8, 8, 8, 2, 8, 8, 8], [1, 1, 8, 2, 2, 8, 8, 1, 1, 1, 1, 1, 1, 8, 8, 8, 2, 8], [1, 1, 8, 2, 8, 8, 8, 1, 8, 1, 8, 1, 1, 8, 8, 8, 8, 8], [1, 8, 8, 8, 8, 2, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 1, 8, 8, 8, 8, 8, 1, 1, 8, 1, 8, 1, 1, 1, 1, 1, 1], [1, 8, 1, 8, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1, 8, 8, 1], [1, 1, 1, 1, 8, 1, 8, 1, 1, 8, 2, 2, 8, 8, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 8, 1, 1, 8, 2, 8, 8, 8, 8, 1, 1, 1], [8, 8, 8, 8, 8, 8, 1, 1, 1, 8, 8, 8, 2, 8, 1, 1, 1, 1], [8, 2, 2, 8, 8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 1, 1], [8, 2, 8, 8, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 8, 8, 2, 8, 1, 1, 1, 8, 8, 8, 8, 8, 1, 1, 1, 8, 1], [8, 8, 8, 8, 8, 1, 1, 1, 8, 8, 2, 2, 8, 1, 1, 1, 1, 8], [1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 2, 2, 8, 1, 1, 1, 8, 8], [8, 8, 1, 1, 1, 1, 1, 1, 8, 2, 8, 8, 8, 1, 1, 1, 1, 1], [8, 1, 8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1]], "output": [[8, 8, 8, 8, 8], [8, 8, 2, 2, 8], [8, 8, 2, 2, 8], [8, 2, 8, 8, 8], [8, 8, 8, 8, 8]]}]}