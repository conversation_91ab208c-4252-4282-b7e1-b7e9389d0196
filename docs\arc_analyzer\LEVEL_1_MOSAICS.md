# 🧩 Niveau 1 - Mosaïques (Puzzles Rares)

## 📋 Vue d'Ensemble

Ce domaine analyse les **vraies mosaïques** dans ARC AGI : puzzles rares caractérisés par des propriétés de **pliage** et **symétrie complexe**.

## 🎯 Principe Fondamental

```
Mosaïque ARC AGI = Grille avec propriétés de pliage
    ↓
Grille plus grande que la moyenne
    ↓
Répartition des couleurs très équilibrée
    ↓
Zones uniformes visibles (input et diff)
    ↓
Pliures possibles : verticale, horizontale, diagonale
```

## 🔍 **Critères d'Identification des Mosaïques**

### **1. Taille de Grille**
- **Critère** : Grille significativement plus grande que la moyenne ARC AGI
- **Seuil typique** : > 15×15 pixels (à ajuster selon les données)
- **Justification** : Les mosaïques nécessitent de l'espace pour les patterns de pliage

### **2. Équilibre des Couleurs**
- **Critère** : Distribution très équilibrée des couleurs
- **Mesure** : Entropie élevée, pas de couleur ultra-dominante
- **Justification** : Les pliages créent une répartition homogène

### **3. Zones Uniformes**
- **Critère** : Présence de zones de couleur uniforme
- **Détection** : Dans input ET visible dans la grille diff
- **Extraction** : Coordonnées exactes de ces zones

## 📊 Structure de Données

```python
mosaics_analysis = {
    # Détection préliminaire de mosaïque
    'mosaic_detection': {
        'is_potential_mosaic': bool,    # Critères de base respectés
        'confidence_score': float,      # Confiance dans la détection (0-1)
        'detection_reasons': List[str], # Raisons de la détection
        
        'size_analysis': {
            'grid_dimensions': (int, int),
            'total_pixels': int,
            'size_category': str,       # 'small', 'medium', 'large', 'very_large'
            'size_percentile': float,   # Percentile par rapport aux grilles ARC moyennes
            'exceeds_average': bool     # Dépasse la taille moyenne
        },
        
        'color_balance_analysis': {
            'color_entropy': float,     # Entropie de Shannon des couleurs
            'balance_score': float,     # Score d'équilibre (0-1)
            'dominant_color_ratio': float, # Ratio de la couleur dominante
            'color_distribution': Dict[int, float], # Distribution par couleur
            'is_well_balanced': bool    # Distribution équilibrée
        },
        
        'uniform_zones_detection': {
            'uniform_zones': [
                {
                    'zone_id': str,
                    'color': int,
                    'bounds': [int, int, int, int], # [top, left, bottom, right]
                    'area': int,
                    'shape': str,           # 'rectangle', 'square', 'irregular'
                    'position': str,        # 'corner', 'edge', 'center'
                    'coordinates': List[Tuple] # Toutes les coordonnées de la zone
                }
            ],
            'total_uniform_zones': int,
            'uniform_coverage_ratio': float, # Ratio de grille couverte par zones uniformes
            'zone_colors': List[int]    # Couleurs des zones uniformes
        }
    },
    
    # Analyse des pliures possibles
    'folding_analysis': {
        'vertical_folds': [
            {
                'fold_id': str,
                'fold_position': int,       # Position de la ligne de pliage (colonne)
                'fold_type': str,           # 'center', 'offset', 'multiple'
                'left_section': {
                    'bounds': [int, int, int, int],
                    'content_hash': str,
                    'dominant_colors': List[int]
                },
                'right_section': {
                    'bounds': [int, int, int, int], 
                    'content_hash': str,
                    'dominant_colors': List[int]
                },
                'fold_quality': {
                    'perfect_match': bool,      # Pliage parfait
                    'match_percentage': float,  # % de correspondance après pliage
                    'mismatch_positions': List[Tuple], # Positions qui ne matchent pas
                    'fold_confidence': float    # Confiance dans ce pliage (0-1)
                }
            }
        ],
        
        'horizontal_folds': [
            {
                'fold_id': str,
                'fold_position': int,       # Position de la ligne de pliage (ligne)
                'fold_type': str,
                'top_section': {
                    'bounds': [int, int, int, int],
                    'content_hash': str,
                    'dominant_colors': List[int]
                },
                'bottom_section': {
                    'bounds': [int, int, int, int],
                    'content_hash': str, 
                    'dominant_colors': List[int]
                },
                'fold_quality': {
                    'perfect_match': bool,
                    'match_percentage': float,
                    'mismatch_positions': List[Tuple],
                    'fold_confidence': float
                }
            }
        ],
        
        'diagonal_folds': [
            {
                'fold_id': str,
                'diagonal_type': str,       # 'main', 'anti', 'custom'
                'fold_line': {
                    'start': (int, int),
                    'end': (int, int),
                    'equation': str         # Équation de la droite de pliage
                },
                'section_1': {
                    'region_points': List[Tuple],
                    'content_hash': str,
                    'dominant_colors': List[int]
                },
                'section_2': {
                    'region_points': List[Tuple],
                    'content_hash': str,
                    'dominant_colors': List[int]
                },
                'fold_quality': {
                    'perfect_match': bool,
                    'match_percentage': float,
                    'mismatch_positions': List[Tuple],
                    'fold_confidence': float
                }
            }
        ],
        
        'best_fold_candidate': {
            'fold_type': str,           # 'vertical', 'horizontal', 'diagonal'
            'fold_id': str,             # ID du meilleur pliage
            'confidence': float,        # Confiance dans ce pliage
            'match_quality': float,     # Qualité de la correspondance
            'missing_pieces': List[Dict] # Morceaux manquants identifiés
        }
    },
    
    # Analyse des morceaux manquants
    'missing_pieces_analysis': {
        'identified_gaps': [
            {
                'gap_id': str,
                'position': [int, int, int, int], # Bounds du morceau manquant
                'expected_content': np.ndarray or None, # Contenu attendu par pliage
                'gap_size': int,            # Nombre de pixels manquants
                'gap_shape': str,           # 'rectangle', 'irregular', 'complex'
                'surrounding_context': {
                    'adjacent_colors': List[int],
                    'pattern_context': str,
                    'fold_relationship': str # Relation avec le pliage détecté
                },
                'completion_confidence': float # Confiance dans la prédiction
            }
        ],
        
        'completion_strategy': {
            'primary_method': str,      # 'fold_mirror', 'pattern_continuation', 'context_fill'
            'fold_based_completion': bool, # Complétion basée sur pliage
            'pattern_based_completion': bool, # Complétion basée sur patterns
            'hybrid_approach': bool     # Approche combinée
        }
    },
    
    # Patterns hiérarchiques
    'hierarchical_patterns': {
        'has_hierarchy': bool,
        'hierarchy_levels': int,        # Nombre de niveaux hiérarchiques
        'level_analysis': [
            {
                'level': int,           # Niveau hiérarchique (0 = base)
                'pattern_size': (int, int), # Taille du pattern à ce niveau
                'repetition_count': (int, int), # Répétitions par dimension
                'level_tiles': List[str], # Tuiles impliquées à ce niveau
                'level_complexity': str
            }
        ],
        
        'fractal_properties': {
            'is_fractal_like': bool,
            'self_similarity': float,   # Score de auto-similarité (0-1)
            'scaling_factor': float or None, # Facteur d'échelle si fractal
            'fractal_dimension': float or None # Dimension fractale estimée
        }
    },
    
    # Analyse des défauts et anomalies
    'defect_analysis': {
        'has_defects': bool,
        'defect_types': List[str],      # 'missing_tile', 'wrong_tile', 'partial_tile', 'color_error'
        'defect_locations': [
            {
                'position': (int, int),
                'defect_type': str,
                'expected_tile': str,   # Tuile attendue
                'actual_content': np.ndarray, # Contenu réel
                'severity': str         # 'minor', 'major', 'critical'
            }
        ],
        
        'pattern_breaks': [             # Ruptures dans le pattern
            {
                'break_position': (int, int),
                'break_type': str,      # 'alignment', 'color', 'size', 'missing'
                'affected_area': Dict,  # Zone affectée
                'break_severity': float
            }
        ],
        
        'completion_analysis': {
            'pattern_completeness': float, # Complétude du pattern (0-1)
            'missing_elements': List[Dict], # Éléments manquants
            'extra_elements': List[Dict],   # Éléments en trop
            'completion_suggestions': List[Dict] # Suggestions de complétion
        }
    },
    
    # Variations et transformations de tuiles
    'tile_variations': {
        'variation_analysis': [
            {
                'base_tile': str,
                'variations': List[str],    # IDs des variations
                'variation_types': List[str], # 'color', 'rotation', 'flip', 'scale'
                'transformation_matrix': Dict[str, str], # {variant_id: transformation}
                'variation_frequency': Dict[str, int]
            }
        ],
        
        'color_variations': {
            'has_color_variants': bool,
            'color_mappings': List[Dict], # Mappings de couleurs entre variants
            'color_consistency': float,  # Cohérence des couleurs
            'palette_variations': List[List[int]] # Palettes de couleurs utilisées
        },
        
        'geometric_variations': {
            'has_rotations': bool,
            'rotation_angles': List[int], # Angles de rotation détectés
            'has_reflections': bool,
            'reflection_axes': List[str], # Axes de réflexion
            'has_scaling': bool,
            'scale_factors': List[float]
        }
    },
    
    # Patterns de remplissage
    'filling_patterns': {
        'background_filling': {
            'has_background_pattern': bool,
            'background_tile': str or None,
            'background_color': int or None,
            'filling_method': str       # 'solid', 'tiled', 'gradient', 'noise'
        },
        
        'space_utilization': {
            'coverage_efficiency': float, # Efficacité de couverture
            'empty_space_ratio': float,  # Ratio d'espace vide
            'overlap_ratio': float,      # Ratio de chevauchement
            'gap_distribution': Dict     # Distribution des espaces vides
        }
    },
    
    # Statistiques globales
    'mosaic_statistics': {
        'pattern_density': float,       # Densité de patterns
        'repetition_factor': float,     # Facteur de répétition moyen
        'complexity_score': float,      # Score de complexité globale
        'regularity_index': float,      # Indice de régularité
        'diversity_index': float,       # Indice de diversité des tuiles
        
        'size_statistics': {
            'min_tile_size': (int, int),
            'max_tile_size': (int, int),
            'mean_tile_size': (float, float),
            'size_variance': float
        },
        
        'color_statistics': {
            'colors_per_tile': Dict[str, int], # {tile_id: nb_couleurs}
            'global_color_usage': Dict[int, float], # Usage global par couleur
            'color_diversity_per_tile': Dict[str, float]
        }
    }
}
```

## 🔍 Algorithmes de Détection

### **Détection Préliminaire de Mosaïque**
```python
def detect_potential_mosaic(grid_array, value_frequencies):
    """Détecte si la grille pourrait être une mosaïque"""
    
    height, width = grid_array.shape
    total_pixels = height * width
    
    # Critère 1: Taille de grille
    size_threshold = 225  # 15x15, à ajuster selon données ARC
    is_large = total_pixels > size_threshold
    
    # Critère 2: Équilibre des couleurs
    color_entropy = calculate_entropy(value_frequencies)
    max_color_ratio = max(value_frequencies.values()) / total_pixels
    is_balanced = color_entropy > 1.5 and max_color_ratio < 0.6
    
    # Critère 3: Zones uniformes
    uniform_zones = detect_uniform_zones(grid_array)
    has_uniform_zones = len(uniform_zones) > 0
    
    # Score de confiance
    confidence = 0.0
    if is_large: confidence += 0.4
    if is_balanced: confidence += 0.4  
    if has_uniform_zones: confidence += 0.2
    
    return {
        'is_potential_mosaic': confidence > 0.6,
        'confidence_score': confidence,
        'size_analysis': {'exceeds_average': is_large},
        'color_balance_analysis': {'is_well_balanced': is_balanced},
        'uniform_zones_detection': {'uniform_zones': uniform_zones}
    }
```

### **Détection des Pliures**
```python
def detect_folding_patterns(grid_array):
    """Détecte les pliures possibles dans la grille"""
    
    height, width = grid_array.shape
    folds = {'vertical_folds': [], 'horizontal_folds': [], 'diagonal_folds': []}
    
    # Pliures verticales
    for col in range(1, width):
        left_section = grid_array[:, :col]
        right_section = grid_array[:, col:]
        
        # Tenter le pliage (miroir de la section droite)
        if right_section.shape[1] <= left_section.shape[1]:
            right_flipped = np.fliplr(right_section)
            # Comparer avec la partie correspondante de gauche
            comparison_section = left_section[:, -right_section.shape[1]:]
            
            match_percentage = calculate_match_percentage(comparison_section, right_flipped)
            
            if match_percentage > 0.7:  # Seuil de correspondance
                folds['vertical_folds'].append({
                    'fold_id': f'vertical_{col}',
                    'fold_position': col,
                    'match_percentage': match_percentage,
                    'fold_confidence': match_percentage
                })
    
    # Pliures horizontales (logique similaire)
    for row in range(1, height):
        top_section = grid_array[:row, :]
        bottom_section = grid_array[row:, :]
        
        if bottom_section.shape[0] <= top_section.shape[0]:
            bottom_flipped = np.flipud(bottom_section)
            comparison_section = top_section[-bottom_section.shape[0]:, :]
            
            match_percentage = calculate_match_percentage(comparison_section, bottom_flipped)
            
            if match_percentage > 0.7:
                folds['horizontal_folds'].append({
                    'fold_id': f'horizontal_{row}',
                    'fold_position': row,
                    'match_percentage': match_percentage,
                    'fold_confidence': match_percentage
                })
    
    # Pliures diagonales (plus complexe, diagonale principale et anti)
    diagonal_folds = detect_diagonal_folds(grid_array)
    folds['diagonal_folds'] = diagonal_folds
    
    return folds
```

### **Identification des Morceaux Manquants**
```python
def identify_missing_pieces(grid_array, best_fold):
    """Identifie les morceaux manquants basés sur le meilleur pliage"""
    
    if not best_fold:
        return []
    
    missing_pieces = []
    fold_type = best_fold['fold_type']
    
    if fold_type == 'vertical':
        # Analyser les différences après pliage vertical
        fold_pos = best_fold['fold_position']
        left_section = grid_array[:, :fold_pos]
        right_section = grid_array[:, fold_pos:]
        
        # Identifier les positions où le pliage ne correspond pas
        right_flipped = np.fliplr(right_section)
        comparison_section = left_section[:, -right_section.shape[1]:]
        
        mismatch_positions = np.where(comparison_section != right_flipped)
        
        for i, j in zip(mismatch_positions[0], mismatch_positions[1]):
            # Calculer la position absolue dans la grille originale
            abs_col = left_section.shape[1] - right_section.shape[1] + j
            
            missing_pieces.append({
                'gap_id': f'gap_{i}_{abs_col}',
                'position': [i, abs_col, i+1, abs_col+1],
                'expected_content': right_flipped[i, j],
                'gap_size': 1,
                'fold_relationship': f'mirrors_position_{i}_{fold_pos + (right_section.shape[1] - 1 - j)}'
            })
    
    # Logique similaire pour pliages horizontaux et diagonaux...
    
    return missing_pieces
```

## 🎯 Cas d'Usage Spécifiques

### **1. Mosaïque avec Pliage Vertical**
- Grille large avec symétrie verticale imparfaite
- Détection de la ligne de pliage optimale
- Identification des morceaux manquants d'un côté

### **2. Mosaïque avec Pliage Horizontal**  
- Grille haute avec symétrie horizontale imparfaite
- Détection de la ligne de pliage optimale
- Identification des morceaux manquants en haut/bas

### **3. Mosaïque avec Pliage Diagonal**
- Grille avec symétrie diagonale complexe
- Détection de l'axe de pliage diagonal
- Identification des morceaux manquants par rapport à la diagonale

### **4. Mosaïque Multi-Pliages**
- Grille avec plusieurs axes de pliage possibles
- Sélection du meilleur candidat de pliage
- Analyse des morceaux manquants selon l'axe choisi

## 🛡️ Règles de Pureté

### **Interdictions**
- ❌ Pas de complétion automatique des morceaux manquants
- ❌ Pas de comparaison input/output (rôle du Niveau 2)
- ❌ Pas de prédiction de la solution finale

### **Autorisations**
- ✅ Détection factuelle des propriétés de pliage
- ✅ Identification des zones uniformes et leurs coordonnées
- ✅ Analyse de l'équilibre des couleurs
- ✅ Calcul des correspondances de pliage
- ✅ Identification des positions de morceaux manquants

## 🔗 Interface avec Autres Domaines

### **Utilisation des Résultats**
- **Niveau 2** : Compare les mosaïques input/output pour détecter les complétions
- **Domaine Colors** : Utilise l'analyse d'équilibre des couleurs
- **Domaine Spatial** : Utilise les statistiques de taille de grille

### **Données Utilisées**
- **Niveau 0** : `grid_array` et `value_frequencies` pour tous les critères
- **Grille diff** : Analyse des zones uniformes visibles dans les changements

### **Information Critique pour Niveau 2**
- **Coordonnées exactes** des morceaux manquants identifiés
- **Contenu attendu** basé sur les propriétés de pliage
- **Confiance** dans chaque prédiction de pliage

---

**Ce domaine fournit une analyse spécialisée des vraies mosaïques ARC AGI, puzzles rares caractérisés par des propriétés de pliage complexes et des morceaux manquants à compléter.**