{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 4, 4, 4, 4, 4, 4, 4], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 8, 8, 8, 8, 8, 8, 8], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[2, 2, 2, 2, 2, 2], [1, 1, 1, 1, 1, 1], [3, 3, 3, 3, 3, 3], [4, 4, 4, 4, 4, 4], [8, 8, 8, 8, 8, 8], [6, 6, 6, 6, 6, 6]]}, {"input": [[0, 0, 0, 0, 0, 2, 0, 0, 4, 0], [0, 5, 5, 5, 0, 2, 0, 0, 4, 0], [0, 5, 5, 5, 0, 2, 0, 0, 4, 0], [0, 5, 5, 5, 0, 2, 0, 0, 4, 0], [0, 0, 0, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0], [0, 0, 1, 0, 0, 2, 0, 0, 4, 0]], "output": [[1, 2, 4], [1, 2, 4], [1, 2, 4]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 0, 5, 5, 5, 5, 0, 2], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0], [8, 8, 8, 8, 8, 0, 5, 5, 5, 5, 0, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2], [8, 8, 8, 8], [4, 4, 4, 4], [1, 1, 1, 1]]}], "test": [{"input": [[2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 0, 5, 5, 5, 5, 5, 5, 5, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 0, 5, 5, 5, 5, 5, 5, 5, 0, 8, 8, 8], [0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7]], "output": [[2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 8, 8, 8], [4, 4, 4, 4, 4, 4, 4], [6, 6, 6, 6, 6, 6, 6], [1, 1, 1, 1, 1, 1, 1], [7, 7, 7, 7, 7, 7, 7]]}]}