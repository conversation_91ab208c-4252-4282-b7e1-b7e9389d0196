{"train": [{"input": [[0, 0, 0, 1, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 2, 0, 0, 0, 1], [0, 0, 1, 0, 0, 0, 0, 0, 0, 5], [0, 0, 8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 1, 0, 1, 0, 0, 0, 0, 0], [0, 8, 1, 0, 0, 0, 1, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [2, 4, 2, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[2, 7, 7, 1, 0, 3, 0, 0, 0, 3], [0, 0, 0, 9, 0, 0, 0, 0, 3, 7], [0, 0, 0, 1, 0, 0, 0, 6, 0, 9], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0], [9, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 3, 0], [0, 5, 0, 7, 3, 0, 0, 0, 1, 0], [4, 4, 0, 0, 0, 1, 0, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 5, 3, 0], [0, 0, 0, 0, 4, 5, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 2, 6, 2, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[6, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 8], [0, 7, 0, 0, 2, 0, 5, 0, 2, 0], [0, 9, 0, 1, 0, 0, 0, 0, 0, 0], [0, 9, 0, 0, 0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 0, 6, 0, 0, 0, 0], [0, 1, 0, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 2, 3, 2, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 2, 5, 7, 0, 0, 0], [0, 0, 0, 5, 6, 0, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 8, 0, 3, 0, 0, 0, 0, 8], [7, 4, 7, 7, 4, 0, 0, 0, 0, 4], [0, 0, 0, 8, 0, 0, 7, 0, 0, 0], [0, 0, 0, 0, 0, 9, 0, 4, 0, 0], [5, 5, 0, 3, 0, 0, 6, 7, 0, 7], [0, 0, 3, 0, 0, 0, 0, 0, 0, 2], [1, 0, 1, 0, 0, 0, 0, 0, 6, 7]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 2, 9, 2, 0, 0, 0], [0, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}