{"test": [{"input": [[1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [1, 1, 3, 1, 3, 1, 3, 1, 1, 3, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1]]}], "train": [{"input": [[5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5], [5, 5, 5, 3, 5, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 3, 5, 5]], "output": [[5, 5, 5, 5], [5, 5, 5, 5], [5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5], [5, 5, 5, 5, 5, 5, 8, 5, 5, 5, 5, 5, 5, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 5, 5, 8, 5]], "output": [[5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5]]}, {"input": [[4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1], [4, 1, 4, 1, 1, 1, 1, 1, 4, 1]], "output": [[1, 1, 1], [1, 1, 1]]}]}