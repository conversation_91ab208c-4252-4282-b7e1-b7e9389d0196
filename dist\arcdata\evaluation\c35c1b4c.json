{"train": [{"input": [[6, 6, 8, 8, 8, 0, 8, 0, 6, 0], [0, 8, 0, 0, 6, 6, 6, 6, 8, 0], [6, 6, 0, 1, 1, 1, 1, 0, 6, 6], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0], [8, 1, 1, 1, 1, 1, 1, 1, 0, 0], [6, 1, 1, 1, 1, 1, 1, 1, 6, 0], [6, 1, 1, 1, 1, 1, 1, 1, 6, 8], [0, 8, 1, 1, 1, 8, 6, 8, 0, 0], [6, 8, 6, 0, 6, 0, 8, 0, 6, 8], [8, 6, 0, 6, 0, 6, 6, 8, 0, 8]], "output": [[6, 6, 8, 8, 8, 0, 8, 0, 6, 0], [0, 8, 0, 0, 6, 6, 6, 6, 8, 0], [6, 6, 0, 1, 1, 1, 1, 0, 6, 6], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0], [8, 1, 1, 1, 1, 1, 1, 1, 1, 0], [6, 1, 1, 1, 1, 1, 1, 1, 1, 0], [6, 1, 1, 1, 1, 1, 1, 1, 1, 8], [0, 8, 1, 1, 1, 1, 1, 1, 0, 0], [6, 8, 6, 0, 6, 0, 8, 0, 6, 8], [8, 6, 0, 6, 0, 6, 6, 8, 0, 8]]}, {"input": [[9, 0, 0, 0, 0, 7, 7, 0, 9, 0], [0, 0, 9, 0, 0, 0, 9, 9, 9, 0], [7, 7, 0, 3, 3, 3, 3, 7, 9, 7], [0, 3, 7, 3, 3, 3, 3, 9, 3, 7], [0, 3, 9, 3, 3, 0, 0, 0, 3, 9], [9, 3, 3, 3, 3, 0, 0, 9, 3, 0], [3, 3, 3, 3, 3, 9, 0, 0, 3, 7], [3, 3, 3, 3, 3, 0, 9, 9, 3, 0], [0, 9, 0, 3, 3, 3, 9, 9, 9, 9], [7, 9, 7, 9, 0, 0, 7, 7, 0, 0]], "output": [[9, 0, 0, 0, 0, 7, 7, 0, 9, 0], [0, 0, 9, 0, 0, 0, 9, 9, 9, 0], [7, 7, 0, 3, 3, 3, 3, 7, 9, 7], [0, 3, 7, 3, 3, 3, 3, 9, 3, 7], [0, 3, 9, 3, 3, 3, 3, 0, 3, 9], [9, 3, 3, 3, 3, 3, 3, 3, 3, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 9, 0, 3, 3, 3, 3, 9, 9, 9], [7, 9, 7, 9, 0, 0, 7, 7, 0, 0]]}, {"input": [[1, 1, 0, 1, 1, 0, 0, 0, 4, 1], [4, 4, 0, 4, 2, 2, 1, 4, 4, 4], [4, 0, 2, 2, 2, 2, 2, 2, 1, 0], [0, 4, 2, 2, 2, 0, 0, 1, 1, 0], [0, 0, 1, 2, 2, 2, 1, 0, 1, 0], [0, 4, 0, 2, 2, 0, 2, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [4, 1, 4, 1, 2, 2, 4, 4, 1, 4], [0, 4, 4, 4, 2, 1, 1, 4, 4, 1], [4, 0, 4, 4, 0, 4, 1, 1, 4, 0]], "output": [[1, 1, 0, 1, 1, 0, 0, 0, 4, 1], [4, 4, 0, 4, 2, 2, 1, 4, 4, 4], [4, 0, 2, 2, 2, 2, 2, 2, 1, 0], [0, 4, 2, 2, 2, 2, 2, 2, 1, 0], [0, 0, 1, 2, 2, 2, 2, 0, 1, 0], [0, 4, 0, 2, 2, 2, 2, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [4, 1, 4, 1, 2, 2, 4, 4, 1, 4], [0, 4, 4, 4, 2, 2, 1, 4, 4, 1], [4, 0, 4, 4, 0, 4, 1, 1, 4, 0]]}], "test": [{"input": [[0, 0, 6, 6, 6, 6, 0, 6, 6, 0], [2, 6, 0, 6, 9, 0, 6, 0, 2, 6], [2, 6, 6, 9, 9, 9, 9, 0, 6, 6], [2, 0, 0, 9, 9, 0, 9, 6, 0, 2], [9, 9, 9, 9, 9, 9, 6, 0, 0, 0], [9, 9, 9, 9, 9, 9, 9, 9, 0, 0], [0, 0, 9, 9, 9, 9, 6, 6, 0, 0], [2, 9, 9, 9, 9, 9, 9, 6, 2, 6], [0, 0, 2, 9, 0, 6, 9, 0, 2, 6], [6, 0, 0, 2, 0, 6, 0, 6, 6, 2]], "output": [[0, 0, 6, 6, 6, 6, 0, 6, 6, 0], [2, 6, 0, 6, 9, 9, 6, 0, 2, 6], [2, 6, 6, 9, 9, 9, 9, 0, 6, 6], [2, 0, 0, 9, 9, 9, 9, 6, 0, 2], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [0, 0, 9, 9, 9, 9, 9, 9, 0, 0], [2, 9, 9, 9, 9, 9, 9, 9, 9, 6], [0, 0, 2, 9, 0, 6, 9, 0, 2, 6], [6, 0, 0, 2, 0, 6, 0, 6, 6, 2]]}], "name": "c35c1b4c"}