{"test": [{"input": [[8, 8, 5, 9, 8, 9, 5, 8, 5, 6, 5, 5, 2, 2, 5, 6, 6, 5, 2, 2, 5, 5, 6, 5, 8, 5, 9, 8, 9, 5], [8, 9, 9, 8, 8, 9, 8, 5, 6, 6, 5, 6, 2, 5, 2, 6, 6, 2, 5, 2, 6, 5, 6, 6, 5, 8, 9, 8, 8, 9], [5, 9, 9, 9, 9, 9, 8, 5, 5, 5, 2, 5, 6, 2, 2, 2, 2, 2, 2, 6, 5, 2, 5, 5, 5, 8, 9, 9, 9, 9], [9, 8, 9, 9, 9, 8, 9, 8, 5, 6, 5, 6, 6, 2, 2, 2, 2, 2, 2, 6, 6, 5, 6, 5, 8, 9, 8, 9, 9, 9], [8, 8, 9, 9, 8, 8, 8, 9, 2, 2, 6, 6, 2, 5, 6, 2, 2, 6, 5, 2, 6, 6, 2, 2, 9, 8, 8, 8, 9, 9], [9, 9, 9, 8, 8, 5, 8, 9, 2, 5, 2, 2, 5, 2, 5, 6, 6, 5, 2, 5, 2, 2, 5, 2, 9, 8, 5, 8, 8, 9], [5, 8, 8, 9, 8, 8, 5, 5, 5, 2, 2, 2, 6, 5, 2, 6, 6, 2, 5, 6, 2, 2, 2, 5, 5, 5, 8, 8, 9, 8], [8, 5, 5, 8, 9, 9, 5, 9, 6, 6, 2, 2, 2, 6, 6, 6, 6, 6, 6, 2, 2, 2, 6, 6, 9, 5, 9, 9, 8, 5], [5, 6, 5, 5, 2, 2, 5, 6, 1, 7, 2, 2, 1, 1, 7, 2, 2, 7, 1, 1, 2, 2, 7, 1, 6, 5, 2, 2, 5, 5], [6, 6, 5, 6, 2, 5, 2, 6, 7, 1, 1, 7, 1, 1, 7, 2, 2, 7, 1, 1, 7, 1, 1, 7, 6, 2, 5, 2, 6, 5], [5, 5, 2, 5, 6, 2, 2, 2, 2, 1, 2, 7, 7, 1, 1, 7, 7, 1, 1, 7, 7, 2, 1, 2, 2, 2, 2, 6, 5, 2], [5, 6, 5, 6, 6, 2, 2, 2, 2, 7, 7, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 7, 7, 2, 2, 2, 2, 6, 6, 5], [2, 2, 6, 6, 2, 5, 6, 2, 1, 1, 7, 2, 1, 7, 7, 7, 7, 7, 7, 1, 2, 7, 1, 1, 2, 6, 5, 2, 6, 6], [2, 5, 2, 2, 5, 2, 5, 6, 1, 1, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 1, 1, 6, 5, 2, 5, 2, 2], [5, 2, 2, 2, 6, 5, 2, 6, 7, 7, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 7, 7, 6, 2, 5, 6, 2, 2], [6, 6, 2, 2, 2, 6, 6, 6, 2, 2, 7, 2, 7, 2, 2, 1, 1, 2, 2, 7, 2, 7, 2, 2, 6, 6, 6, 2, 2, 2], [6, 6, 2, 2, 2, 6, 6, 6, 2, 2, 7, 2, 7, 2, 2, 1, 1, 2, 2, 7, 2, 7, 2, 2, 6, 6, 6, 2, 2, 2], [5, 2, 2, 2, 6, 5, 2, 6, 7, 7, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 7, 7, 0, 0, 5, 6, 2, 2], [2, 5, 2, 2, 5, 2, 5, 6, 1, 1, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 1, 1, 0, 0, 2, 5, 2, 2], [2, 2, 6, 6, 2, 5, 6, 2, 1, 1, 7, 2, 1, 7, 7, 7, 7, 7, 7, 1, 2, 7, 1, 1, 0, 0, 5, 2, 6, 6], [5, 6, 5, 6, 6, 2, 2, 2, 2, 7, 7, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 7, 7, 2, 0, 0, 2, 6, 6, 5], [5, 5, 2, 5, 6, 2, 2, 2, 2, 1, 2, 7, 7, 1, 1, 7, 7, 1, 1, 7, 7, 2, 1, 2, 0, 0, 2, 6, 5, 2], [6, 6, 5, 6, 2, 5, 2, 6, 7, 1, 1, 7, 1, 1, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 5, 2, 6, 5], [5, 6, 5, 5, 2, 2, 5, 6, 1, 7, 2, 2, 1, 1, 7, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 2, 2, 5, 5], [8, 5, 5, 8, 9, 9, 5, 9, 6, 6, 2, 2, 2, 6, 6, 6, 6, 6, 6, 2, 2, 2, 6, 6, 0, 0, 9, 9, 8, 5], [5, 8, 8, 9, 8, 8, 5, 5, 5, 2, 2, 2, 6, 5, 2, 6, 6, 2, 5, 6, 2, 2, 2, 5, 5, 5, 8, 8, 9, 8], [9, 9, 9, 8, 8, 5, 8, 9, 2, 5, 2, 2, 5, 2, 5, 6, 6, 5, 2, 5, 2, 2, 5, 2, 9, 8, 5, 8, 8, 9], [8, 8, 9, 9, 8, 8, 8, 9, 2, 2, 6, 6, 2, 5, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 9, 8, 8, 8, 9, 9], [9, 8, 9, 9, 9, 8, 9, 8, 5, 6, 5, 6, 6, 2, 0, 0, 0, 0, 0, 0, 0, 0, 6, 5, 8, 9, 8, 9, 9, 9], [5, 9, 9, 9, 9, 9, 8, 5, 5, 5, 2, 5, 6, 2, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 8, 9, 9, 9, 9]], "output": [[8, 8, 5, 9, 8, 9, 5, 8, 5, 6, 5, 5, 2, 2, 5, 6, 6, 5, 2, 2, 5, 5, 6, 5, 8, 5, 9, 8, 9, 5], [8, 9, 9, 8, 8, 9, 8, 5, 6, 6, 5, 6, 2, 5, 2, 6, 6, 2, 5, 2, 6, 5, 6, 6, 5, 8, 9, 8, 8, 9], [5, 9, 9, 9, 9, 9, 8, 5, 5, 5, 2, 5, 6, 2, 2, 2, 2, 2, 2, 6, 5, 2, 5, 5, 5, 8, 9, 9, 9, 9], [9, 8, 9, 9, 9, 8, 9, 8, 5, 6, 5, 6, 6, 2, 2, 2, 2, 2, 2, 6, 6, 5, 6, 5, 8, 9, 8, 9, 9, 9], [8, 8, 9, 9, 8, 8, 8, 9, 2, 2, 6, 6, 2, 5, 6, 2, 2, 6, 5, 2, 6, 6, 2, 2, 9, 8, 8, 8, 9, 9], [9, 9, 9, 8, 8, 5, 8, 9, 2, 5, 2, 2, 5, 2, 5, 6, 6, 5, 2, 5, 2, 2, 5, 2, 9, 8, 5, 8, 8, 9], [5, 8, 8, 9, 8, 8, 5, 5, 5, 2, 2, 2, 6, 5, 2, 6, 6, 2, 5, 6, 2, 2, 2, 5, 5, 5, 8, 8, 9, 8], [8, 5, 5, 8, 9, 9, 5, 9, 6, 6, 2, 2, 2, 6, 6, 6, 6, 6, 6, 2, 2, 2, 6, 6, 9, 5, 9, 9, 8, 5], [5, 6, 5, 5, 2, 2, 5, 6, 1, 7, 2, 2, 1, 1, 7, 2, 2, 7, 1, 1, 2, 2, 7, 1, 6, 5, 2, 2, 5, 5], [6, 6, 5, 6, 2, 5, 2, 6, 7, 1, 1, 7, 1, 1, 7, 2, 2, 7, 1, 1, 7, 1, 1, 7, 6, 2, 5, 2, 6, 5], [5, 5, 2, 5, 6, 2, 2, 2, 2, 1, 2, 7, 7, 1, 1, 7, 7, 1, 1, 7, 7, 2, 1, 2, 2, 2, 2, 6, 5, 2], [5, 6, 5, 6, 6, 2, 2, 2, 2, 7, 7, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 7, 7, 2, 2, 2, 2, 6, 6, 5], [2, 2, 6, 6, 2, 5, 6, 2, 1, 1, 7, 2, 1, 7, 7, 7, 7, 7, 7, 1, 2, 7, 1, 1, 2, 6, 5, 2, 6, 6], [2, 5, 2, 2, 5, 2, 5, 6, 1, 1, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 1, 1, 6, 5, 2, 5, 2, 2], [5, 2, 2, 2, 6, 5, 2, 6, 7, 7, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 7, 7, 6, 2, 5, 6, 2, 2], [6, 6, 2, 2, 2, 6, 6, 6, 2, 2, 7, 2, 7, 2, 2, 1, 1, 2, 2, 7, 2, 7, 2, 2, 6, 6, 6, 2, 2, 2], [6, 6, 2, 2, 2, 6, 6, 6, 2, 2, 7, 2, 7, 2, 2, 1, 1, 2, 2, 7, 2, 7, 2, 2, 6, 6, 6, 2, 2, 2], [5, 2, 2, 2, 6, 5, 2, 6, 7, 7, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 7, 7, 6, 2, 5, 6, 2, 2], [2, 5, 2, 2, 5, 2, 5, 6, 1, 1, 1, 7, 7, 1, 1, 2, 2, 1, 1, 7, 7, 1, 1, 1, 6, 5, 2, 5, 2, 2], [2, 2, 6, 6, 2, 5, 6, 2, 1, 1, 7, 2, 1, 7, 7, 7, 7, 7, 7, 1, 2, 7, 1, 1, 2, 6, 5, 2, 6, 6], [5, 6, 5, 6, 6, 2, 2, 2, 2, 7, 7, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 7, 7, 2, 2, 2, 2, 6, 6, 5], [5, 5, 2, 5, 6, 2, 2, 2, 2, 1, 2, 7, 7, 1, 1, 7, 7, 1, 1, 7, 7, 2, 1, 2, 2, 2, 2, 6, 5, 2], [6, 6, 5, 6, 2, 5, 2, 6, 7, 1, 1, 7, 1, 1, 7, 2, 2, 7, 1, 1, 7, 1, 1, 7, 6, 2, 5, 2, 6, 5], [5, 6, 5, 5, 2, 2, 5, 6, 1, 7, 2, 2, 1, 1, 7, 2, 2, 7, 1, 1, 2, 2, 7, 1, 6, 5, 2, 2, 5, 5], [8, 5, 5, 8, 9, 9, 5, 9, 6, 6, 2, 2, 2, 6, 6, 6, 6, 6, 6, 2, 2, 2, 6, 6, 9, 5, 9, 9, 8, 5], [5, 8, 8, 9, 8, 8, 5, 5, 5, 2, 2, 2, 6, 5, 2, 6, 6, 2, 5, 6, 2, 2, 2, 5, 5, 5, 8, 8, 9, 8], [9, 9, 9, 8, 8, 5, 8, 9, 2, 5, 2, 2, 5, 2, 5, 6, 6, 5, 2, 5, 2, 2, 5, 2, 9, 8, 5, 8, 8, 9], [8, 8, 9, 9, 8, 8, 8, 9, 2, 2, 6, 6, 2, 5, 6, 2, 2, 6, 5, 2, 6, 6, 2, 2, 9, 8, 8, 8, 9, 9], [9, 8, 9, 9, 9, 8, 9, 8, 5, 6, 5, 6, 6, 2, 2, 2, 2, 2, 2, 6, 6, 5, 6, 5, 8, 9, 8, 9, 9, 9], [5, 9, 9, 9, 9, 9, 8, 5, 5, 5, 2, 5, 6, 2, 2, 2, 2, 2, 2, 6, 5, 2, 5, 5, 5, 8, 9, 9, 9, 9]]}], "train": [{"input": [[3, 9, 9, 9, 4, 4, 9, 4, 3, 4, 4, 4, 4, 2, 4, 4, 4, 4, 2, 4, 4, 4, 4, 3, 4, 9, 4, 4, 9, 9], [9, 9, 9, 4, 9, 3, 9, 3, 4, 4, 3, 2, 2, 3, 3, 2, 2, 3, 3, 2, 2, 3, 4, 4, 3, 9, 3, 9, 4, 9], [9, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 4, 3, 2, 2, 3, 4, 4, 4, 2, 3, 4, 4, 9, 3, 3, 4, 9], [9, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 2, 4, 3, 3, 4, 2, 4, 4, 4, 2, 4, 4, 3, 4, 3, 3, 4], [4, 9, 3, 0, 0, 0, 0, 0, 0, 0, 0, 4, 3, 3, 3, 4, 4, 3, 3, 3, 4, 4, 2, 4, 3, 9, 4, 4, 3, 3], [4, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 3, 3, 4, 4, 3, 3, 3, 2, 4, 3, 2, 9, 9, 4, 4, 4, 3], [9, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 4, 3, 3, 4, 4, 4, 4, 3, 3, 4, 3, 3, 4, 9, 9, 9, 9, 3, 9], [4, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 4, 4, 3, 3, 4, 4, 4, 3, 2, 2, 4, 9, 9, 9, 3, 4, 4], [3, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 2, 0, 0, 0, 0, 0, 0, 0, 3, 7, 3, 4, 4, 2, 4, 4, 4], [4, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 2, 0, 0, 0, 0, 0, 0, 0, 7, 2, 7, 2, 3, 3, 2, 2, 3], [4, 3, 2, 4, 4, 4, 3, 2, 3, 7, 3, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 3, 0, 0, 2, 3, 4, 4, 4, 2], [4, 2, 4, 4, 4, 2, 4, 3, 7, 7, 7, 2, 2, 3, 3, 2, 2, 3, 3, 2, 2, 7, 0, 0, 3, 4, 2, 4, 4, 4], [4, 2, 4, 4, 3, 3, 3, 4, 7, 7, 2, 2, 3, 2, 7, 7, 7, 7, 2, 3, 2, 2, 0, 0, 4, 3, 3, 3, 4, 4], [2, 3, 4, 2, 3, 3, 3, 4, 2, 2, 7, 3, 2, 2, 7, 7, 7, 7, 2, 2, 3, 7, 0, 0, 4, 3, 3, 3, 2, 4], [4, 3, 3, 4, 3, 3, 4, 4, 2, 7, 7, 3, 7, 7, 2, 3, 3, 2, 7, 7, 3, 7, 0, 0, 4, 4, 3, 3, 4, 3], [4, 2, 2, 3, 4, 4, 4, 3, 3, 3, 2, 2, 7, 7, 3, 2, 2, 3, 7, 7, 2, 2, 3, 3, 3, 4, 4, 4, 3, 2], [4, 2, 2, 3, 4, 4, 4, 3, 3, 3, 2, 2, 7, 7, 3, 2, 2, 3, 7, 7, 2, 2, 3, 3, 3, 4, 4, 4, 3, 2], [4, 3, 3, 4, 3, 3, 4, 4, 2, 7, 7, 3, 7, 7, 2, 3, 3, 2, 7, 7, 3, 7, 7, 2, 4, 4, 3, 3, 4, 3], [2, 3, 4, 2, 3, 3, 3, 4, 2, 2, 7, 3, 2, 2, 7, 7, 7, 7, 2, 2, 3, 7, 2, 2, 4, 3, 3, 3, 2, 4], [4, 2, 4, 4, 3, 3, 3, 4, 7, 7, 2, 2, 3, 2, 7, 0, 0, 0, 0, 3, 2, 2, 7, 7, 4, 3, 3, 3, 4, 4], [4, 2, 4, 4, 4, 2, 4, 3, 7, 7, 7, 2, 2, 3, 3, 0, 0, 0, 0, 2, 2, 7, 7, 7, 3, 4, 2, 4, 4, 4], [4, 3, 2, 4, 4, 4, 3, 2, 3, 7, 3, 7, 2, 7, 7, 0, 0, 0, 0, 2, 7, 3, 7, 3, 2, 3, 4, 4, 4, 2], [4, 4, 3, 2, 2, 3, 3, 2, 7, 2, 7, 7, 7, 2, 7, 3, 3, 7, 2, 7, 7, 7, 2, 7, 2, 3, 3, 2, 2, 3], [3, 4, 4, 4, 4, 2, 4, 4, 3, 7, 3, 7, 7, 2, 2, 3, 3, 2, 2, 7, 7, 3, 7, 3, 4, 4, 2, 4, 4, 4], [4, 3, 4, 4, 3, 9, 9, 9, 4, 2, 2, 3, 4, 4, 4, 3, 3, 4, 4, 4, 3, 2, 2, 4, 9, 9, 9, 3, 4, 4], [9, 9, 9, 3, 9, 9, 9, 9, 4, 3, 3, 4, 3, 3, 4, 4, 4, 4, 3, 3, 4, 3, 3, 4, 9, 9, 9, 9, 3, 9], [4, 3, 3, 4, 4, 4, 9, 9, 2, 3, 4, 2, 3, 3, 3, 4, 4, 3, 3, 3, 2, 4, 3, 2, 9, 9, 4, 4, 4, 3], [4, 9, 3, 3, 4, 4, 9, 3, 4, 2, 4, 4, 3, 3, 3, 4, 4, 3, 3, 3, 4, 4, 2, 4, 3, 9, 4, 4, 3, 3], [9, 4, 4, 3, 3, 4, 3, 4, 4, 2, 4, 4, 4, 2, 4, 3, 3, 4, 2, 4, 4, 4, 2, 4, 4, 3, 4, 3, 3, 4], [9, 9, 9, 4, 3, 3, 9, 4, 4, 3, 2, 4, 4, 4, 3, 2, 2, 3, 4, 4, 4, 2, 3, 4, 4, 9, 3, 3, 4, 9]], "output": [[3, 9, 9, 9, 4, 4, 9, 4, 3, 4, 4, 4, 4, 2, 4, 4, 4, 4, 2, 4, 4, 4, 4, 3, 4, 9, 4, 4, 9, 9], [9, 9, 9, 4, 9, 3, 9, 3, 4, 4, 3, 2, 2, 3, 3, 2, 2, 3, 3, 2, 2, 3, 4, 4, 3, 9, 3, 9, 4, 9], [9, 9, 9, 4, 3, 3, 9, 4, 4, 3, 2, 4, 4, 4, 3, 2, 2, 3, 4, 4, 4, 2, 3, 4, 4, 9, 3, 3, 4, 9], [9, 4, 4, 3, 3, 4, 3, 4, 4, 2, 4, 4, 4, 2, 4, 3, 3, 4, 2, 4, 4, 4, 2, 4, 4, 3, 4, 3, 3, 4], [4, 9, 3, 3, 4, 4, 9, 3, 4, 2, 4, 4, 3, 3, 3, 4, 4, 3, 3, 3, 4, 4, 2, 4, 3, 9, 4, 4, 3, 3], [4, 3, 3, 4, 4, 4, 9, 9, 2, 3, 4, 2, 3, 3, 3, 4, 4, 3, 3, 3, 2, 4, 3, 2, 9, 9, 4, 4, 4, 3], [9, 9, 9, 3, 9, 9, 9, 9, 4, 3, 3, 4, 3, 3, 4, 4, 4, 4, 3, 3, 4, 3, 3, 4, 9, 9, 9, 9, 3, 9], [4, 3, 4, 4, 3, 9, 9, 9, 4, 2, 2, 3, 4, 4, 4, 3, 3, 4, 4, 4, 3, 2, 2, 4, 9, 9, 9, 3, 4, 4], [3, 4, 4, 4, 4, 2, 4, 4, 3, 7, 3, 7, 7, 2, 2, 3, 3, 2, 2, 7, 7, 3, 7, 3, 4, 4, 2, 4, 4, 4], [4, 4, 3, 2, 2, 3, 3, 2, 7, 2, 7, 7, 7, 2, 7, 3, 3, 7, 2, 7, 7, 7, 2, 7, 2, 3, 3, 2, 2, 3], [4, 3, 2, 4, 4, 4, 3, 2, 3, 7, 3, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 3, 7, 3, 2, 3, 4, 4, 4, 2], [4, 2, 4, 4, 4, 2, 4, 3, 7, 7, 7, 2, 2, 3, 3, 2, 2, 3, 3, 2, 2, 7, 7, 7, 3, 4, 2, 4, 4, 4], [4, 2, 4, 4, 3, 3, 3, 4, 7, 7, 2, 2, 3, 2, 7, 7, 7, 7, 2, 3, 2, 2, 7, 7, 4, 3, 3, 3, 4, 4], [2, 3, 4, 2, 3, 3, 3, 4, 2, 2, 7, 3, 2, 2, 7, 7, 7, 7, 2, 2, 3, 7, 2, 2, 4, 3, 3, 3, 2, 4], [4, 3, 3, 4, 3, 3, 4, 4, 2, 7, 7, 3, 7, 7, 2, 3, 3, 2, 7, 7, 3, 7, 7, 2, 4, 4, 3, 3, 4, 3], [4, 2, 2, 3, 4, 4, 4, 3, 3, 3, 2, 2, 7, 7, 3, 2, 2, 3, 7, 7, 2, 2, 3, 3, 3, 4, 4, 4, 3, 2], [4, 2, 2, 3, 4, 4, 4, 3, 3, 3, 2, 2, 7, 7, 3, 2, 2, 3, 7, 7, 2, 2, 3, 3, 3, 4, 4, 4, 3, 2], [4, 3, 3, 4, 3, 3, 4, 4, 2, 7, 7, 3, 7, 7, 2, 3, 3, 2, 7, 7, 3, 7, 7, 2, 4, 4, 3, 3, 4, 3], [2, 3, 4, 2, 3, 3, 3, 4, 2, 2, 7, 3, 2, 2, 7, 7, 7, 7, 2, 2, 3, 7, 2, 2, 4, 3, 3, 3, 2, 4], [4, 2, 4, 4, 3, 3, 3, 4, 7, 7, 2, 2, 3, 2, 7, 7, 7, 7, 2, 3, 2, 2, 7, 7, 4, 3, 3, 3, 4, 4], [4, 2, 4, 4, 4, 2, 4, 3, 7, 7, 7, 2, 2, 3, 3, 2, 2, 3, 3, 2, 2, 7, 7, 7, 3, 4, 2, 4, 4, 4], [4, 3, 2, 4, 4, 4, 3, 2, 3, 7, 3, 7, 2, 7, 7, 2, 2, 7, 7, 2, 7, 3, 7, 3, 2, 3, 4, 4, 4, 2], [4, 4, 3, 2, 2, 3, 3, 2, 7, 2, 7, 7, 7, 2, 7, 3, 3, 7, 2, 7, 7, 7, 2, 7, 2, 3, 3, 2, 2, 3], [3, 4, 4, 4, 4, 2, 4, 4, 3, 7, 3, 7, 7, 2, 2, 3, 3, 2, 2, 7, 7, 3, 7, 3, 4, 4, 2, 4, 4, 4], [4, 3, 4, 4, 3, 9, 9, 9, 4, 2, 2, 3, 4, 4, 4, 3, 3, 4, 4, 4, 3, 2, 2, 4, 9, 9, 9, 3, 4, 4], [9, 9, 9, 3, 9, 9, 9, 9, 4, 3, 3, 4, 3, 3, 4, 4, 4, 4, 3, 3, 4, 3, 3, 4, 9, 9, 9, 9, 3, 9], [4, 3, 3, 4, 4, 4, 9, 9, 2, 3, 4, 2, 3, 3, 3, 4, 4, 3, 3, 3, 2, 4, 3, 2, 9, 9, 4, 4, 4, 3], [4, 9, 3, 3, 4, 4, 9, 3, 4, 2, 4, 4, 3, 3, 3, 4, 4, 3, 3, 3, 4, 4, 2, 4, 3, 9, 4, 4, 3, 3], [9, 4, 4, 3, 3, 4, 3, 4, 4, 2, 4, 4, 4, 2, 4, 3, 3, 4, 2, 4, 4, 4, 2, 4, 4, 3, 4, 3, 3, 4], [9, 9, 9, 4, 3, 3, 9, 4, 4, 3, 2, 4, 4, 4, 3, 2, 2, 3, 4, 4, 4, 2, 3, 4, 4, 9, 3, 3, 4, 9]]}, {"input": [[9, 4, 9, 9, 9, 9, 9, 4, 7, 8, 5, 8, 7, 8, 7, 5, 5, 7, 8, 7, 8, 5, 8, 7, 4, 9, 9, 9, 9, 9], [4, 6, 9, 4, 9, 9, 9, 9, 8, 5, 7, 8, 7, 8, 8, 5, 5, 8, 8, 7, 8, 7, 5, 8, 9, 9, 9, 9, 4, 9], [9, 9, 6, 9, 4, 9, 4, 4, 5, 7, 7, 7, 8, 5, 8, 5, 5, 8, 5, 8, 7, 7, 7, 5, 4, 4, 9, 4, 9, 6], [9, 4, 9, 4, 6, 6, 9, 9, 8, 8, 7, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 7, 8, 8, 9, 9, 6, 6, 4, 9], [9, 9, 4, 6, 6, 6, 4, 6, 7, 7, 8, 5, 8, 8, 8, 7, 7, 8, 8, 8, 5, 8, 7, 7, 6, 4, 6, 6, 6, 4], [9, 9, 9, 6, 6, 6, 9, 9, 8, 8, 5, 5, 8, 7, 8, 8, 8, 8, 7, 8, 5, 5, 8, 8, 9, 9, 6, 6, 6, 9], [9, 9, 4, 9, 4, 9, 4, 6, 7, 8, 8, 8, 8, 8, 5, 8, 8, 5, 8, 8, 8, 8, 8, 7, 6, 4, 9, 4, 9, 4], [4, 9, 4, 9, 6, 9, 6, 4, 5, 5, 5, 5, 7, 8, 8, 5, 5, 8, 8, 7, 0, 0, 0, 0, 4, 6, 9, 6, 9, 4], [7, 8, 5, 8, 7, 8, 7, 5, 7, 5, 8, 8, 7, 7, 7, 8, 8, 7, 7, 7, 0, 0, 0, 0, 5, 7, 8, 7, 8, 5], [8, 5, 7, 8, 7, 8, 8, 5, 5, 7, 8, 8, 5, 8, 5, 7, 7, 5, 8, 5, 0, 0, 0, 0, 5, 8, 8, 7, 8, 7], [5, 7, 7, 7, 8, 5, 8, 5, 8, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 5, 0, 0, 0, 0, 5, 8, 5, 8, 7, 7], [8, 8, 7, 5, 5, 5, 8, 5, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 0, 0, 0, 0, 5, 8, 5, 5, 5, 7], [7, 7, 8, 5, 8, 8, 8, 7, 7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 7, 5, 0, 0, 0, 0, 7, 8, 8, 8, 5, 8], [8, 8, 5, 5, 8, 7, 8, 8, 7, 8, 7, 7, 7, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 8, 8, 7, 8, 5, 5], [7, 8, 8, 8, 8, 8, 5, 8, 7, 5, 7, 7, 7, 0, 0, 0, 0, 0, 0, 7, 7, 7, 5, 7, 8, 5, 8, 8, 8, 8], [5, 5, 5, 5, 7, 8, 8, 5, 8, 7, 7, 7, 7, 8, 7, 8, 8, 7, 8, 7, 7, 7, 7, 8, 5, 8, 8, 7, 5, 5], [5, 5, 5, 5, 7, 8, 8, 5, 8, 7, 7, 7, 7, 8, 7, 8, 8, 7, 8, 7, 7, 7, 7, 8, 5, 8, 8, 7, 5, 5], [7, 8, 8, 8, 8, 8, 5, 8, 7, 5, 7, 7, 7, 7, 8, 7, 7, 8, 7, 7, 7, 7, 5, 7, 8, 5, 8, 8, 8, 8], [8, 8, 5, 5, 8, 7, 8, 8, 7, 8, 7, 7, 7, 5, 7, 8, 8, 7, 5, 7, 7, 7, 8, 7, 8, 8, 7, 8, 5, 5], [7, 7, 8, 5, 8, 8, 8, 7, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 5, 7, 5, 5, 7, 7, 8, 8, 8, 5, 8], [8, 8, 7, 5, 5, 5, 8, 5, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7, 5, 7, 8, 8, 5, 8, 5, 5, 5, 7], [5, 7, 7, 7, 8, 5, 8, 5, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 5, 7, 8, 8, 8, 5, 8, 5, 8, 7, 7], [8, 5, 7, 8, 7, 8, 8, 5, 0, 0, 0, 0, 0, 0, 0, 7, 7, 5, 8, 5, 8, 8, 7, 5, 5, 8, 8, 7, 8, 7], [7, 8, 5, 8, 7, 8, 7, 5, 0, 0, 0, 0, 0, 0, 0, 8, 8, 7, 7, 7, 8, 8, 5, 7, 5, 7, 8, 7, 8, 5], [4, 9, 4, 9, 6, 9, 6, 4, 0, 0, 0, 0, 0, 0, 0, 5, 5, 8, 8, 7, 5, 5, 5, 5, 4, 6, 9, 6, 9, 4], [9, 9, 4, 9, 4, 9, 4, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 7, 6, 4, 9, 4, 9, 4], [9, 9, 9, 6, 6, 6, 9, 9, 8, 8, 5, 5, 8, 0, 0, 0, 0, 0, 0, 8, 5, 5, 8, 8, 9, 9, 6, 6, 6, 9], [9, 9, 4, 6, 6, 6, 4, 6, 7, 7, 8, 5, 8, 0, 0, 0, 0, 0, 0, 8, 5, 8, 7, 7, 6, 4, 6, 6, 6, 4], [9, 4, 9, 4, 6, 6, 9, 9, 8, 8, 7, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 7, 8, 8, 9, 9, 6, 6, 4, 9], [9, 9, 6, 9, 4, 9, 4, 4, 5, 7, 7, 7, 8, 0, 0, 0, 0, 0, 0, 8, 7, 7, 7, 5, 4, 4, 9, 4, 9, 6]], "output": [[9, 4, 9, 9, 9, 9, 9, 4, 7, 8, 5, 8, 7, 8, 7, 5, 5, 7, 8, 7, 8, 5, 8, 7, 4, 9, 9, 9, 9, 9], [4, 6, 9, 4, 9, 9, 9, 9, 8, 5, 7, 8, 7, 8, 8, 5, 5, 8, 8, 7, 8, 7, 5, 8, 9, 9, 9, 9, 4, 9], [9, 9, 6, 9, 4, 9, 4, 4, 5, 7, 7, 7, 8, 5, 8, 5, 5, 8, 5, 8, 7, 7, 7, 5, 4, 4, 9, 4, 9, 6], [9, 4, 9, 4, 6, 6, 9, 9, 8, 8, 7, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 7, 8, 8, 9, 9, 6, 6, 4, 9], [9, 9, 4, 6, 6, 6, 4, 6, 7, 7, 8, 5, 8, 8, 8, 7, 7, 8, 8, 8, 5, 8, 7, 7, 6, 4, 6, 6, 6, 4], [9, 9, 9, 6, 6, 6, 9, 9, 8, 8, 5, 5, 8, 7, 8, 8, 8, 8, 7, 8, 5, 5, 8, 8, 9, 9, 6, 6, 6, 9], [9, 9, 4, 9, 4, 9, 4, 6, 7, 8, 8, 8, 8, 8, 5, 8, 8, 5, 8, 8, 8, 8, 8, 7, 6, 4, 9, 4, 9, 4], [4, 9, 4, 9, 6, 9, 6, 4, 5, 5, 5, 5, 7, 8, 8, 5, 5, 8, 8, 7, 5, 5, 5, 5, 4, 6, 9, 6, 9, 4], [7, 8, 5, 8, 7, 8, 7, 5, 7, 5, 8, 8, 7, 7, 7, 8, 8, 7, 7, 7, 8, 8, 5, 7, 5, 7, 8, 7, 8, 5], [8, 5, 7, 8, 7, 8, 8, 5, 5, 7, 8, 8, 5, 8, 5, 7, 7, 5, 8, 5, 8, 8, 7, 5, 5, 8, 8, 7, 8, 7], [5, 7, 7, 7, 8, 5, 8, 5, 8, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 5, 7, 8, 8, 8, 5, 8, 5, 8, 7, 7], [8, 8, 7, 5, 5, 5, 8, 5, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7, 8, 8, 5, 8, 5, 5, 5, 7], [7, 7, 8, 5, 8, 8, 8, 7, 7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 7, 5, 7, 5, 5, 7, 7, 8, 8, 8, 5, 8], [8, 8, 5, 5, 8, 7, 8, 8, 7, 8, 7, 7, 7, 5, 7, 8, 8, 7, 5, 7, 7, 7, 8, 7, 8, 8, 7, 8, 5, 5], [7, 8, 8, 8, 8, 8, 5, 8, 7, 5, 7, 7, 7, 7, 8, 7, 7, 8, 7, 7, 7, 7, 5, 7, 8, 5, 8, 8, 8, 8], [5, 5, 5, 5, 7, 8, 8, 5, 8, 7, 7, 7, 7, 8, 7, 8, 8, 7, 8, 7, 7, 7, 7, 8, 5, 8, 8, 7, 5, 5], [5, 5, 5, 5, 7, 8, 8, 5, 8, 7, 7, 7, 7, 8, 7, 8, 8, 7, 8, 7, 7, 7, 7, 8, 5, 8, 8, 7, 5, 5], [7, 8, 8, 8, 8, 8, 5, 8, 7, 5, 7, 7, 7, 7, 8, 7, 7, 8, 7, 7, 7, 7, 5, 7, 8, 5, 8, 8, 8, 8], [8, 8, 5, 5, 8, 7, 8, 8, 7, 8, 7, 7, 7, 5, 7, 8, 8, 7, 5, 7, 7, 7, 8, 7, 8, 8, 7, 8, 5, 5], [7, 7, 8, 5, 8, 8, 8, 7, 7, 5, 5, 7, 5, 7, 7, 7, 7, 7, 7, 5, 7, 5, 5, 7, 7, 8, 8, 8, 5, 8], [8, 8, 7, 5, 5, 5, 8, 5, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 7, 7, 5, 7, 8, 8, 5, 8, 5, 5, 5, 7], [5, 7, 7, 7, 8, 5, 8, 5, 8, 8, 8, 7, 5, 7, 7, 7, 7, 7, 7, 5, 7, 8, 8, 8, 5, 8, 5, 8, 7, 7], [8, 5, 7, 8, 7, 8, 8, 5, 5, 7, 8, 8, 5, 8, 5, 7, 7, 5, 8, 5, 8, 8, 7, 5, 5, 8, 8, 7, 8, 7], [7, 8, 5, 8, 7, 8, 7, 5, 7, 5, 8, 8, 7, 7, 7, 8, 8, 7, 7, 7, 8, 8, 5, 7, 5, 7, 8, 7, 8, 5], [4, 9, 4, 9, 6, 9, 6, 4, 5, 5, 5, 5, 7, 8, 8, 5, 5, 8, 8, 7, 5, 5, 5, 5, 4, 6, 9, 6, 9, 4], [9, 9, 4, 9, 4, 9, 4, 6, 7, 8, 8, 8, 8, 8, 5, 8, 8, 5, 8, 8, 8, 8, 8, 7, 6, 4, 9, 4, 9, 4], [9, 9, 9, 6, 6, 6, 9, 9, 8, 8, 5, 5, 8, 7, 8, 8, 8, 8, 7, 8, 5, 5, 8, 8, 9, 9, 6, 6, 6, 9], [9, 9, 4, 6, 6, 6, 4, 6, 7, 7, 8, 5, 8, 8, 8, 7, 7, 8, 8, 8, 5, 8, 7, 7, 6, 4, 6, 6, 6, 4], [9, 4, 9, 4, 6, 6, 9, 9, 8, 8, 7, 5, 5, 5, 8, 5, 5, 8, 5, 5, 5, 7, 8, 8, 9, 9, 6, 6, 4, 9], [9, 9, 6, 9, 4, 9, 4, 4, 5, 7, 7, 7, 8, 5, 8, 5, 5, 8, 5, 8, 7, 7, 7, 5, 4, 4, 9, 4, 9, 6]]}, {"input": [[6, 4, 4, 6, 3, 6, 4, 4, 8, 6, 8, 6, 8, 8, 4, 8, 8, 4, 8, 8, 6, 8, 6, 8, 4, 4, 6, 3, 6, 4], [4, 6, 6, 6, 3, 4, 4, 3, 6, 4, 8, 8, 8, 4, 8, 8, 8, 8, 4, 8, 8, 8, 4, 6, 3, 4, 4, 3, 6, 6], [4, 6, 4, 6, 3, 4, 6, 4, 8, 8, 8, 4, 4, 4, 4, 6, 6, 4, 4, 4, 4, 8, 8, 8, 4, 6, 4, 3, 6, 4], [6, 6, 6, 4, 3, 3, 6, 6, 6, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 6, 6, 6, 3, 3, 4, 6], [3, 3, 3, 3, 6, 6, 4, 4, 8, 8, 4, 8, 6, 8, 8, 6, 6, 8, 8, 6, 8, 4, 8, 8, 4, 4, 6, 6, 3, 3], [0, 0, 0, 3, 6, 4, 6, 4, 8, 4, 4, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 4, 4, 8, 4, 6, 4, 6, 3, 4], [0, 0, 0, 6, 4, 6, 4, 6, 4, 8, 4, 8, 8, 8, 8, 8, 0, 0, 8, 8, 8, 4, 8, 4, 6, 4, 6, 4, 6, 6], [0, 0, 0, 6, 4, 4, 6, 3, 8, 8, 6, 8, 6, 8, 8, 4, 0, 0, 8, 6, 8, 6, 8, 8, 3, 6, 4, 4, 6, 4], [8, 6, 8, 6, 8, 8, 4, 8, 1, 6, 6, 1, 5, 5, 6, 1, 0, 0, 5, 5, 1, 6, 6, 1, 8, 4, 8, 8, 6, 8], [6, 4, 8, 8, 8, 4, 8, 8, 6, 1, 6, 5, 1, 1, 5, 6, 0, 0, 1, 1, 5, 6, 1, 6, 8, 8, 4, 8, 8, 8], [8, 8, 8, 4, 4, 4, 4, 6, 6, 6, 5, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 5, 6, 6, 6, 4, 4, 4, 4, 8], [6, 8, 4, 8, 8, 8, 8, 8, 1, 5, 6, 5, 6, 1, 6, 1, 1, 6, 1, 6, 5, 6, 5, 1, 8, 8, 8, 8, 8, 4], [8, 8, 4, 8, 6, 8, 8, 6, 5, 1, 6, 6, 6, 6, 6, 5, 5, 6, 6, 6, 6, 6, 1, 5, 6, 8, 8, 6, 8, 4], [8, 4, 4, 8, 8, 8, 8, 8, 5, 1, 6, 1, 6, 1, 5, 6, 6, 5, 1, 6, 1, 6, 1, 5, 8, 8, 8, 8, 8, 4], [4, 8, 4, 8, 8, 8, 8, 8, 6, 5, 1, 6, 6, 5, 1, 6, 6, 1, 5, 6, 6, 1, 5, 6, 8, 8, 8, 8, 8, 4], [8, 8, 6, 8, 6, 8, 8, 4, 1, 6, 1, 1, 5, 6, 6, 1, 1, 6, 6, 5, 1, 1, 6, 1, 4, 8, 8, 6, 8, 6], [8, 8, 6, 8, 6, 8, 8, 4, 1, 6, 1, 1, 5, 6, 6, 1, 1, 6, 6, 5, 1, 1, 6, 1, 4, 8, 8, 6, 8, 6], [4, 8, 4, 8, 8, 8, 8, 8, 6, 5, 1, 6, 6, 5, 1, 6, 6, 1, 5, 6, 6, 1, 5, 6, 8, 8, 8, 8, 8, 4], [8, 4, 4, 8, 8, 8, 8, 8, 5, 1, 6, 1, 6, 1, 5, 6, 6, 5, 1, 6, 1, 6, 1, 5, 8, 8, 8, 8, 8, 4], [8, 8, 4, 8, 6, 8, 8, 6, 5, 1, 6, 6, 6, 6, 6, 5, 5, 6, 6, 6, 6, 6, 1, 5, 6, 8, 8, 6, 8, 4], [6, 8, 4, 8, 8, 8, 8, 8, 1, 5, 6, 5, 6, 1, 6, 1, 1, 6, 1, 6, 5, 6, 5, 1, 8, 8, 8, 8, 8, 4], [8, 8, 8, 4, 4, 4, 4, 6, 6, 6, 5, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 5, 6, 6, 6, 4, 4, 4, 4, 8], [6, 4, 8, 8, 8, 4, 8, 8, 6, 1, 6, 5, 1, 1, 5, 6, 6, 5, 1, 1, 5, 6, 1, 6, 8, 8, 4, 8, 8, 8], [8, 6, 8, 6, 8, 8, 4, 8, 1, 6, 6, 1, 5, 5, 6, 1, 1, 6, 5, 5, 1, 6, 6, 1, 8, 4, 8, 8, 6, 8], [4, 3, 4, 6, 4, 4, 6, 3, 8, 8, 6, 8, 6, 8, 8, 4, 4, 8, 8, 6, 8, 6, 8, 8, 3, 6, 0, 0, 0, 4], [4, 4, 6, 6, 4, 6, 4, 6, 4, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 4, 6, 4, 0, 0, 0, 6], [6, 4, 4, 3, 6, 4, 6, 4, 8, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 8, 4, 6, 0, 0, 0, 4], [3, 3, 3, 3, 6, 6, 4, 4, 8, 8, 4, 8, 6, 8, 8, 6, 6, 8, 8, 6, 8, 4, 8, 8, 4, 4, 0, 0, 0, 3], [6, 6, 6, 4, 3, 3, 6, 6, 6, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 6, 6, 6, 0, 0, 0, 6], [4, 6, 4, 6, 3, 4, 6, 4, 8, 8, 8, 4, 4, 4, 4, 6, 6, 4, 4, 4, 4, 8, 8, 8, 4, 6, 4, 3, 6, 4]], "output": [[6, 4, 4, 6, 3, 6, 4, 4, 8, 6, 8, 6, 8, 8, 4, 8, 8, 4, 8, 8, 6, 8, 6, 8, 4, 4, 6, 3, 6, 4], [4, 6, 6, 6, 3, 4, 4, 3, 6, 4, 8, 8, 8, 4, 8, 8, 8, 8, 4, 8, 8, 8, 4, 6, 3, 4, 4, 3, 6, 6], [4, 6, 4, 6, 3, 4, 6, 4, 8, 8, 8, 4, 4, 4, 4, 6, 6, 4, 4, 4, 4, 8, 8, 8, 4, 6, 4, 3, 6, 4], [6, 6, 6, 4, 3, 3, 6, 6, 6, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 6, 6, 6, 3, 3, 4, 6], [3, 3, 3, 3, 6, 6, 4, 4, 8, 8, 4, 8, 6, 8, 8, 6, 6, 8, 8, 6, 8, 4, 8, 8, 4, 4, 6, 6, 3, 3], [6, 4, 4, 3, 6, 4, 6, 4, 8, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 8, 4, 6, 4, 6, 3, 4], [4, 4, 6, 6, 4, 6, 4, 6, 4, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 4, 6, 4, 6, 4, 6, 6], [4, 3, 4, 6, 4, 4, 6, 3, 8, 8, 6, 8, 6, 8, 8, 4, 4, 8, 8, 6, 8, 6, 8, 8, 3, 6, 4, 4, 6, 4], [8, 6, 8, 6, 8, 8, 4, 8, 1, 6, 6, 1, 5, 5, 6, 1, 1, 6, 5, 5, 1, 6, 6, 1, 8, 4, 8, 8, 6, 8], [6, 4, 8, 8, 8, 4, 8, 8, 6, 1, 6, 5, 1, 1, 5, 6, 6, 5, 1, 1, 5, 6, 1, 6, 8, 8, 4, 8, 8, 8], [8, 8, 8, 4, 4, 4, 4, 6, 6, 6, 5, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 5, 6, 6, 6, 4, 4, 4, 4, 8], [6, 8, 4, 8, 8, 8, 8, 8, 1, 5, 6, 5, 6, 1, 6, 1, 1, 6, 1, 6, 5, 6, 5, 1, 8, 8, 8, 8, 8, 4], [8, 8, 4, 8, 6, 8, 8, 6, 5, 1, 6, 6, 6, 6, 6, 5, 5, 6, 6, 6, 6, 6, 1, 5, 6, 8, 8, 6, 8, 4], [8, 4, 4, 8, 8, 8, 8, 8, 5, 1, 6, 1, 6, 1, 5, 6, 6, 5, 1, 6, 1, 6, 1, 5, 8, 8, 8, 8, 8, 4], [4, 8, 4, 8, 8, 8, 8, 8, 6, 5, 1, 6, 6, 5, 1, 6, 6, 1, 5, 6, 6, 1, 5, 6, 8, 8, 8, 8, 8, 4], [8, 8, 6, 8, 6, 8, 8, 4, 1, 6, 1, 1, 5, 6, 6, 1, 1, 6, 6, 5, 1, 1, 6, 1, 4, 8, 8, 6, 8, 6], [8, 8, 6, 8, 6, 8, 8, 4, 1, 6, 1, 1, 5, 6, 6, 1, 1, 6, 6, 5, 1, 1, 6, 1, 4, 8, 8, 6, 8, 6], [4, 8, 4, 8, 8, 8, 8, 8, 6, 5, 1, 6, 6, 5, 1, 6, 6, 1, 5, 6, 6, 1, 5, 6, 8, 8, 8, 8, 8, 4], [8, 4, 4, 8, 8, 8, 8, 8, 5, 1, 6, 1, 6, 1, 5, 6, 6, 5, 1, 6, 1, 6, 1, 5, 8, 8, 8, 8, 8, 4], [8, 8, 4, 8, 6, 8, 8, 6, 5, 1, 6, 6, 6, 6, 6, 5, 5, 6, 6, 6, 6, 6, 1, 5, 6, 8, 8, 6, 8, 4], [6, 8, 4, 8, 8, 8, 8, 8, 1, 5, 6, 5, 6, 1, 6, 1, 1, 6, 1, 6, 5, 6, 5, 1, 8, 8, 8, 8, 8, 4], [8, 8, 8, 4, 4, 4, 4, 6, 6, 6, 5, 6, 6, 6, 1, 1, 1, 1, 6, 6, 6, 5, 6, 6, 6, 4, 4, 4, 4, 8], [6, 4, 8, 8, 8, 4, 8, 8, 6, 1, 6, 5, 1, 1, 5, 6, 6, 5, 1, 1, 5, 6, 1, 6, 8, 8, 4, 8, 8, 8], [8, 6, 8, 6, 8, 8, 4, 8, 1, 6, 6, 1, 5, 5, 6, 1, 1, 6, 5, 5, 1, 6, 6, 1, 8, 4, 8, 8, 6, 8], [4, 3, 4, 6, 4, 4, 6, 3, 8, 8, 6, 8, 6, 8, 8, 4, 4, 8, 8, 6, 8, 6, 8, 8, 3, 6, 4, 4, 6, 4], [4, 4, 6, 6, 4, 6, 4, 6, 4, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 4, 6, 4, 6, 4, 6, 6], [6, 4, 4, 3, 6, 4, 6, 4, 8, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 4, 8, 4, 6, 4, 6, 3, 4], [3, 3, 3, 3, 6, 6, 4, 4, 8, 8, 4, 8, 6, 8, 8, 6, 6, 8, 8, 6, 8, 4, 8, 8, 4, 4, 6, 6, 3, 3], [6, 6, 6, 4, 3, 3, 6, 6, 6, 8, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 4, 8, 6, 6, 6, 3, 3, 4, 6], [4, 6, 4, 6, 3, 4, 6, 4, 8, 8, 8, 4, 4, 4, 4, 6, 6, 4, 4, 4, 4, 8, 8, 8, 4, 6, 4, 3, 6, 4]]}, {"input": [[8, 7, 8, 8, 7, 7, 8, 7, 9, 5, 5, 9, 9, 9, 5, 9, 9, 5, 9, 9, 9, 5, 5, 9, 7, 8, 7, 7, 8, 8], [7, 8, 8, 7, 7, 5, 7, 7, 5, 9, 9, 9, 5, 3, 9, 3, 3, 9, 3, 5, 9, 9, 9, 5, 7, 7, 5, 7, 7, 8], [8, 8, 5, 8, 7, 5, 7, 8, 5, 9, 3, 9, 3, 3, 9, 3, 3, 9, 3, 3, 9, 3, 9, 5, 8, 7, 5, 7, 8, 5], [8, 7, 8, 5, 7, 8, 8, 8, 9, 9, 9, 3, 5, 5, 9, 9, 9, 9, 5, 5, 3, 9, 9, 9, 8, 8, 8, 7, 5, 8], [7, 7, 7, 7, 5, 7, 8, 8, 9, 5, 3, 5, 3, 5, 9, 9, 9, 9, 5, 3, 5, 3, 5, 9, 8, 8, 7, 5, 7, 7], [7, 5, 5, 8, 7, 7, 8, 7, 9, 3, 3, 5, 5, 5, 3, 9, 9, 3, 5, 5, 5, 3, 3, 9, 7, 8, 7, 7, 8, 5], [8, 7, 7, 8, 8, 8, 8, 7, 5, 9, 9, 9, 9, 3, 9, 3, 3, 9, 3, 9, 9, 9, 9, 5, 7, 8, 8, 8, 8, 7], [7, 7, 8, 8, 8, 7, 7, 8, 9, 3, 3, 9, 9, 9, 3, 9, 9, 3, 9, 9, 9, 3, 3, 9, 8, 7, 7, 8, 8, 8], [9, 5, 5, 9, 9, 9, 5, 9, 3, 8, 8, 8, 8, 3, 7, 7, 7, 7, 3, 8, 8, 8, 8, 3, 9, 5, 9, 9, 9, 5], [5, 9, 9, 9, 5, 3, 9, 3, 8, 3, 8, 8, 7, 3, 8, 8, 8, 8, 3, 7, 8, 8, 3, 8, 3, 9, 3, 5, 9, 9], [5, 9, 3, 9, 3, 3, 9, 3, 8, 8, 7, 8, 7, 3, 7, 7, 7, 7, 3, 7, 8, 7, 8, 8, 3, 9, 3, 3, 9, 3], [9, 9, 9, 3, 5, 5, 9, 9, 8, 8, 8, 7, 8, 3, 3, 7, 7, 3, 3, 8, 7, 8, 8, 8, 9, 9, 5, 5, 3, 9], [9, 5, 3, 5, 3, 5, 9, 9, 8, 7, 7, 8, 3, 8, 8, 8, 8, 8, 8, 3, 8, 7, 7, 8, 9, 9, 5, 3, 5, 3], [9, 3, 3, 5, 5, 5, 3, 9, 3, 3, 3, 3, 8, 7, 3, 7, 7, 3, 7, 8, 3, 3, 3, 3, 9, 3, 5, 5, 5, 3], [5, 9, 9, 9, 9, 3, 9, 3, 7, 8, 7, 3, 8, 3, 8, 8, 8, 8, 3, 8, 3, 7, 8, 0, 0, 0, 0, 0, 9, 9], [9, 3, 3, 9, 9, 9, 3, 9, 7, 8, 7, 7, 8, 7, 8, 8, 8, 8, 7, 8, 7, 7, 8, 0, 0, 0, 0, 0, 9, 3], [9, 3, 3, 9, 9, 9, 3, 9, 7, 8, 7, 7, 8, 7, 8, 8, 8, 8, 7, 8, 7, 7, 8, 0, 0, 0, 0, 0, 9, 3], [5, 9, 9, 9, 9, 3, 9, 3, 7, 8, 7, 3, 8, 3, 8, 8, 8, 8, 3, 8, 3, 7, 8, 0, 0, 0, 0, 0, 9, 9], [9, 3, 3, 5, 5, 5, 3, 9, 3, 0, 0, 0, 0, 0, 3, 7, 7, 3, 7, 8, 3, 3, 3, 0, 0, 0, 0, 0, 5, 3], [9, 5, 3, 5, 3, 5, 9, 9, 8, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 3, 8, 7, 7, 0, 0, 0, 0, 0, 5, 3], [9, 9, 9, 3, 5, 5, 9, 9, 8, 8, 8, 7, 8, 3, 3, 7, 7, 3, 3, 8, 7, 8, 8, 0, 0, 0, 0, 0, 3, 9], [5, 9, 3, 9, 3, 3, 9, 3, 8, 8, 7, 8, 7, 3, 7, 7, 7, 7, 3, 7, 8, 7, 8, 8, 3, 9, 3, 3, 9, 3], [5, 9, 9, 9, 5, 3, 9, 3, 8, 3, 8, 8, 7, 0, 0, 8, 8, 8, 3, 7, 8, 8, 3, 8, 3, 9, 3, 5, 9, 9], [9, 5, 5, 9, 9, 9, 5, 9, 3, 8, 8, 8, 8, 0, 0, 7, 7, 7, 3, 8, 8, 8, 8, 3, 9, 5, 9, 9, 9, 5], [7, 7, 8, 8, 8, 7, 7, 8, 9, 3, 3, 9, 9, 0, 0, 9, 9, 3, 9, 9, 9, 3, 3, 9, 8, 7, 7, 8, 8, 8], [8, 7, 7, 8, 8, 8, 8, 7, 5, 9, 9, 9, 9, 0, 0, 3, 3, 9, 3, 9, 9, 9, 9, 5, 7, 8, 8, 8, 8, 7], [7, 5, 5, 8, 7, 7, 8, 7, 9, 3, 3, 5, 5, 0, 0, 9, 9, 3, 5, 5, 5, 3, 3, 9, 7, 8, 7, 7, 8, 5], [7, 7, 7, 7, 5, 7, 8, 8, 9, 5, 3, 5, 3, 0, 0, 9, 9, 9, 5, 3, 5, 3, 5, 9, 8, 8, 7, 5, 7, 7], [8, 7, 8, 5, 7, 8, 8, 8, 9, 9, 9, 3, 5, 0, 0, 9, 9, 9, 5, 5, 3, 9, 9, 9, 8, 8, 8, 7, 5, 8], [8, 8, 5, 8, 7, 5, 7, 8, 5, 9, 3, 9, 3, 3, 9, 3, 3, 9, 3, 3, 9, 3, 9, 5, 8, 7, 5, 7, 8, 5]], "output": [[8, 7, 8, 8, 7, 7, 8, 7, 9, 5, 5, 9, 9, 9, 5, 9, 9, 5, 9, 9, 9, 5, 5, 9, 7, 8, 7, 7, 8, 8], [7, 8, 8, 7, 7, 5, 7, 7, 5, 9, 9, 9, 5, 3, 9, 3, 3, 9, 3, 5, 9, 9, 9, 5, 7, 7, 5, 7, 7, 8], [8, 8, 5, 8, 7, 5, 7, 8, 5, 9, 3, 9, 3, 3, 9, 3, 3, 9, 3, 3, 9, 3, 9, 5, 8, 7, 5, 7, 8, 5], [8, 7, 8, 5, 7, 8, 8, 8, 9, 9, 9, 3, 5, 5, 9, 9, 9, 9, 5, 5, 3, 9, 9, 9, 8, 8, 8, 7, 5, 8], [7, 7, 7, 7, 5, 7, 8, 8, 9, 5, 3, 5, 3, 5, 9, 9, 9, 9, 5, 3, 5, 3, 5, 9, 8, 8, 7, 5, 7, 7], [7, 5, 5, 8, 7, 7, 8, 7, 9, 3, 3, 5, 5, 5, 3, 9, 9, 3, 5, 5, 5, 3, 3, 9, 7, 8, 7, 7, 8, 5], [8, 7, 7, 8, 8, 8, 8, 7, 5, 9, 9, 9, 9, 3, 9, 3, 3, 9, 3, 9, 9, 9, 9, 5, 7, 8, 8, 8, 8, 7], [7, 7, 8, 8, 8, 7, 7, 8, 9, 3, 3, 9, 9, 9, 3, 9, 9, 3, 9, 9, 9, 3, 3, 9, 8, 7, 7, 8, 8, 8], [9, 5, 5, 9, 9, 9, 5, 9, 3, 8, 8, 8, 8, 3, 7, 7, 7, 7, 3, 8, 8, 8, 8, 3, 9, 5, 9, 9, 9, 5], [5, 9, 9, 9, 5, 3, 9, 3, 8, 3, 8, 8, 7, 3, 8, 8, 8, 8, 3, 7, 8, 8, 3, 8, 3, 9, 3, 5, 9, 9], [5, 9, 3, 9, 3, 3, 9, 3, 8, 8, 7, 8, 7, 3, 7, 7, 7, 7, 3, 7, 8, 7, 8, 8, 3, 9, 3, 3, 9, 3], [9, 9, 9, 3, 5, 5, 9, 9, 8, 8, 8, 7, 8, 3, 3, 7, 7, 3, 3, 8, 7, 8, 8, 8, 9, 9, 5, 5, 3, 9], [9, 5, 3, 5, 3, 5, 9, 9, 8, 7, 7, 8, 3, 8, 8, 8, 8, 8, 8, 3, 8, 7, 7, 8, 9, 9, 5, 3, 5, 3], [9, 3, 3, 5, 5, 5, 3, 9, 3, 3, 3, 3, 8, 7, 3, 7, 7, 3, 7, 8, 3, 3, 3, 3, 9, 3, 5, 5, 5, 3], [5, 9, 9, 9, 9, 3, 9, 3, 7, 8, 7, 3, 8, 3, 8, 8, 8, 8, 3, 8, 3, 7, 8, 7, 3, 9, 3, 9, 9, 9], [9, 3, 3, 9, 9, 9, 3, 9, 7, 8, 7, 7, 8, 7, 8, 8, 8, 8, 7, 8, 7, 7, 8, 7, 9, 3, 9, 9, 9, 3], [9, 3, 3, 9, 9, 9, 3, 9, 7, 8, 7, 7, 8, 7, 8, 8, 8, 8, 7, 8, 7, 7, 8, 7, 9, 3, 9, 9, 9, 3], [5, 9, 9, 9, 9, 3, 9, 3, 7, 8, 7, 3, 8, 3, 8, 8, 8, 8, 3, 8, 3, 7, 8, 7, 3, 9, 3, 9, 9, 9], [9, 3, 3, 5, 5, 5, 3, 9, 3, 3, 3, 3, 8, 7, 3, 7, 7, 3, 7, 8, 3, 3, 3, 3, 9, 3, 5, 5, 5, 3], [9, 5, 3, 5, 3, 5, 9, 9, 8, 7, 7, 8, 3, 8, 8, 8, 8, 8, 8, 3, 8, 7, 7, 8, 9, 9, 5, 3, 5, 3], [9, 9, 9, 3, 5, 5, 9, 9, 8, 8, 8, 7, 8, 3, 3, 7, 7, 3, 3, 8, 7, 8, 8, 8, 9, 9, 5, 5, 3, 9], [5, 9, 3, 9, 3, 3, 9, 3, 8, 8, 7, 8, 7, 3, 7, 7, 7, 7, 3, 7, 8, 7, 8, 8, 3, 9, 3, 3, 9, 3], [5, 9, 9, 9, 5, 3, 9, 3, 8, 3, 8, 8, 7, 3, 8, 8, 8, 8, 3, 7, 8, 8, 3, 8, 3, 9, 3, 5, 9, 9], [9, 5, 5, 9, 9, 9, 5, 9, 3, 8, 8, 8, 8, 3, 7, 7, 7, 7, 3, 8, 8, 8, 8, 3, 9, 5, 9, 9, 9, 5], [7, 7, 8, 8, 8, 7, 7, 8, 9, 3, 3, 9, 9, 9, 3, 9, 9, 3, 9, 9, 9, 3, 3, 9, 8, 7, 7, 8, 8, 8], [8, 7, 7, 8, 8, 8, 8, 7, 5, 9, 9, 9, 9, 3, 9, 3, 3, 9, 3, 9, 9, 9, 9, 5, 7, 8, 8, 8, 8, 7], [7, 5, 5, 8, 7, 7, 8, 7, 9, 3, 3, 5, 5, 5, 3, 9, 9, 3, 5, 5, 5, 3, 3, 9, 7, 8, 7, 7, 8, 5], [7, 7, 7, 7, 5, 7, 8, 8, 9, 5, 3, 5, 3, 5, 9, 9, 9, 9, 5, 3, 5, 3, 5, 9, 8, 8, 7, 5, 7, 7], [8, 7, 8, 5, 7, 8, 8, 8, 9, 9, 9, 3, 5, 5, 9, 9, 9, 9, 5, 5, 3, 9, 9, 9, 8, 8, 8, 7, 5, 8], [8, 8, 5, 8, 7, 5, 7, 8, 5, 9, 3, 9, 3, 3, 9, 3, 3, 9, 3, 3, 9, 3, 9, 5, 8, 7, 5, 7, 8, 5]]}]}