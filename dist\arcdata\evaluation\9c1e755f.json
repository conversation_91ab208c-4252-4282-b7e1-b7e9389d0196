{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 6, 6, 6, 9, 9, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 6, 6, 6, 9, 9, 9, 0], [0, 0, 5, 6, 6, 6, 9, 9, 9, 0], [0, 0, 5, 6, 6, 6, 9, 9, 9, 0], [0, 0, 5, 6, 6, 6, 9, 9, 9, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 4, 3, 3, 4, 4, 4, 0, 0, 0], [5, 7, 3, 7, 7, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 4, 3, 3, 4, 4, 4, 0, 0, 0], [5, 7, 3, 7, 7, 3, 3, 0, 0, 0], [5, 4, 3, 3, 4, 4, 4, 0, 0, 0], [5, 7, 3, 7, 7, 3, 3, 0, 0, 0], [5, 4, 3, 3, 4, 4, 4, 0, 0, 0], [5, 7, 3, 7, 7, 3, 3, 0, 0, 0], [5, 4, 3, 3, 4, 4, 4, 0, 0, 0], [5, 7, 3, 7, 7, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 5, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 0, 0, 0], [0, 6, 6, 6, 6, 6, 6, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 1, 1, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 3, 0, 0, 0], [5, 0, 0, 0, 0, 0, 3, 0, 0, 0], [5, 0, 0, 0, 0, 0, 7, 0, 0, 0], [5, 0, 0, 0, 0, 0, 3, 0, 0, 0], [5, 0, 0, 0, 0, 0, 7, 0, 0, 0], [5, 0, 0, 0, 0, 0, 7, 0, 0, 0], [5, 6, 3, 6, 3, 0, 0, 0, 0, 0], [5, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [5, 6, 3, 6, 3, 0, 3, 3, 3, 3], [5, 2, 2, 2, 2, 0, 3, 3, 3, 3], [5, 6, 3, 6, 3, 0, 7, 7, 7, 7], [5, 2, 2, 2, 2, 0, 3, 3, 3, 3], [5, 6, 3, 6, 3, 0, 7, 7, 7, 7], [5, 2, 2, 2, 2, 0, 7, 7, 7, 7], [5, 6, 3, 6, 3, 0, 0, 0, 0, 0], [5, 2, 2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 5, 0, 0, 0], [4, 0, 0, 0, 0, 0, 5, 0, 0, 0], [2, 0, 0, 0, 0, 0, 5, 0, 0, 0], [2, 0, 0, 0, 0, 0, 5, 0, 0, 0], [2, 0, 0, 0, 0, 0, 5, 0, 0, 0], [1, 0, 0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 8, 6, 8], [0, 0, 0, 0, 0, 0, 5, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 5, 8, 6, 8], [4, 4, 4, 4, 4, 0, 5, 3, 3, 3], [2, 2, 2, 2, 2, 0, 5, 8, 6, 8], [2, 2, 2, 2, 2, 0, 5, 3, 3, 3], [2, 2, 2, 2, 2, 0, 5, 8, 6, 8], [1, 1, 1, 1, 1, 0, 5, 3, 3, 3], [0, 0, 0, 0, 0, 0, 5, 8, 6, 8], [0, 0, 0, 0, 0, 0, 5, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}