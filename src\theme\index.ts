import { createTheme, ThemeOptions } from '@mui/material/styles';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useMemo } from 'react';

// Couleurs officielles ARC
export const ARC_COLORS = {
  0: '#000000', // Noir
  1: '#0074D9', // Bleu
  2: '#FF4136', // Rouge
  3: '#2ECC40', // Vert
  4: '#FFDC00', // Jaune
  5: '#AAAAAA', // Gris
  6: '#F012BE', // Magenta
  7: '#FF851B', // Orange
  8: '#7FDBFF', // Cyan
  9: '#870C25', // Marron
};

// Fonction pour détecter le thème système
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// Thème de base
const baseTheme: ThemeOptions = {
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontWeight: 600 },
    h2: { fontWeight: 600 },
    h3: { fontWeight: 600 },
    h4: { fontWeight: 600 },
    h5: { fontWeight: 600 },
    h6: { fontWeight: 600 },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          // Définir les couleurs ARC comme variables CSS
          ...Object.entries(ARC_COLORS).reduce((acc, [key, value]) => {
            acc[`--arc-color-${key}` as any] = value;
            return acc;
          }, {} as any),
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
};

// Thème clair
const lightTheme = createTheme({
  ...baseTheme,
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#9c27b0',
      light: '#ba68c8',
      dark: '#7b1fa2',
    },
    background: {
      default: '#fafafa',
      paper: '#ffffff',
    },
    text: {
      primary: '#212121',
      secondary: '#757575',
    },
  },
});

// Thème sombre
const darkTheme = createTheme({
  ...baseTheme,
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
      light: '#e3f2fd',
      dark: '#42a5f5',
    },
    secondary: {
      main: '#ce93d8',
      light: '#f3e5f5',
      dark: '#ab47bc',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0b0b0',
    },
  },
});

// Hook pour utiliser le thème approprié
export const useAppTheme = () => {
  const themeMode = useSelector((state: RootState) => state.settings.theme);

  return useMemo(() => {
    let mode: 'light' | 'dark';

    if (themeMode === 'system') {
      mode = getSystemTheme();
    } else {
      mode = themeMode;
    }

    return mode === 'dark' ? darkTheme : lightTheme;
  }, [themeMode]);
};

// Thème par défaut (pour les cas où Redux n'est pas encore initialisé)
export const theme = lightTheme;
export default theme;