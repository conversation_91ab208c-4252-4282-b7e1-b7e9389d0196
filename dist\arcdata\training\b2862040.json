{"train": [{"input": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 1, 1, 1, 9, 9, 9, 1, 9, 9, 9], [9, 1, 9, 1, 9, 9, 9, 1, 9, 9, 9], [9, 1, 9, 1, 9, 9, 1, 1, 1, 1, 9], [9, 1, 1, 1, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9]], "output": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 8, 8, 8, 9, 9, 9, 1, 9, 9, 9], [9, 8, 9, 8, 9, 9, 9, 1, 9, 9, 9], [9, 8, 9, 8, 9, 9, 1, 1, 1, 1, 9], [9, 8, 8, 8, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9]]}, {"input": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 1, 1, 1, 1, 1, 9, 9, 1, 9, 9], [9, 1, 9, 9, 9, 1, 9, 9, 1, 9, 1], [9, 1, 1, 1, 1, 1, 9, 9, 1, 1, 1], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9], [9, 9, 1, 1, 1, 1, 1, 9, 9, 9, 9], [9, 9, 9, 1, 9, 1, 9, 9, 9, 9, 9], [9, 9, 9, 1, 1, 1, 9, 9, 1, 1, 1], [9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 1], [1, 1, 9, 9, 9, 9, 9, 9, 1, 1, 1]], "output": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 8, 8, 8, 8, 8, 9, 9, 1, 9, 9], [9, 8, 9, 9, 9, 8, 9, 9, 1, 9, 1], [9, 8, 8, 8, 8, 8, 9, 9, 1, 1, 1], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9], [9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9], [9, 9, 9, 8, 9, 8, 9, 9, 9, 9, 9], [9, 9, 9, 8, 8, 8, 9, 9, 8, 8, 8], [9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8], [1, 1, 9, 9, 9, 9, 9, 9, 8, 8, 8]]}, {"input": [[9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9, 9], [9, 9, 1, 9, 9, 9, 9, 1, 1, 1, 1, 9, 9], [9, 1, 1, 1, 1, 9, 9, 9, 1, 9, 9, 9, 9], [9, 1, 9, 9, 1, 9, 9, 9, 1, 9, 9, 9, 9], [9, 1, 1, 1, 1, 9, 9, 9, 1, 1, 1, 9, 9], [9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9], [9, 1, 9, 9, 9, 9, 9, 1, 1, 1, 9, 9, 9], [1, 1, 1, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 1, 9, 9, 9, 9, 1, 9, 1, 1, 9, 9, 9], [1, 1, 9, 9, 9, 9, 1, 1, 1, 9, 9, 9, 9]], "output": [[9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9, 9], [9, 9, 8, 9, 9, 9, 9, 1, 1, 1, 1, 9, 9], [9, 8, 8, 8, 8, 9, 9, 9, 1, 9, 9, 9, 9], [9, 8, 9, 9, 8, 9, 9, 9, 1, 9, 9, 9, 9], [9, 8, 8, 8, 8, 9, 9, 9, 1, 1, 1, 9, 9], [9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9], [9, 1, 9, 9, 9, 9, 9, 1, 1, 1, 9, 9, 9], [1, 1, 1, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 1, 9, 9, 9, 9, 1, 9, 1, 1, 9, 9, 9], [1, 1, 9, 9, 9, 9, 1, 1, 1, 9, 9, 9, 9]]}, {"input": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 1, 1, 1, 1, 1, 1, 9, 9, 9, 9, 1, 1, 1, 1], [9, 9, 1, 9, 9, 9, 1, 9, 9, 9, 9, 1, 9, 9, 1], [9, 9, 1, 1, 1, 9, 1, 9, 9, 9, 1, 1, 1, 9, 1], [9, 9, 9, 9, 1, 1, 1, 9, 9, 9, 9, 9, 9, 9, 1], [9, 9, 9, 9, 1, 9, 9, 9, 1, 1, 1, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 1, 1, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 1, 1, 1, 9, 9, 9, 9], [1, 1, 1, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [1, 9, 9, 1, 9, 9, 9, 1, 9, 1, 9, 9, 9, 9, 9], [1, 1, 1, 1, 9, 9, 9, 1, 1, 1, 1, 1, 9, 9, 9], [1, 9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 1, 1, 9, 9, 9, 9, 9, 9, 1, 1, 9]], "output": [[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 1, 1, 1, 1], [9, 9, 8, 9, 9, 9, 8, 9, 9, 9, 9, 1, 9, 9, 1], [9, 9, 8, 8, 8, 9, 8, 9, 9, 9, 1, 1, 1, 9, 1], [9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 1], [9, 9, 9, 9, 8, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 8, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9], [8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [8, 9, 9, 8, 9, 9, 9, 1, 9, 1, 9, 9, 9, 9, 9], [8, 8, 8, 8, 9, 9, 9, 1, 1, 1, 1, 1, 9, 9, 9], [8, 9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 1, 1, 9, 9, 9, 9, 9, 9, 1, 1, 9]]}], "test": [{"input": [[1, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 1, 1, 1, 1, 1, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 1, 9, 9, 1, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 1, 9, 9, 1, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 1, 1, 1, 1, 9, 9, 9, 1, 9, 9, 1], [9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9, 1, 1, 1, 1], [1, 1, 1, 1, 9, 9, 9, 1, 9, 9, 9, 1, 9, 9, 1], [1, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1], [1, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1, 1], [1, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 1, 1, 1, 1, 1, 1, 9, 9, 9, 1, 1, 9], [9, 9, 9, 1, 9, 9, 9, 9, 1, 9, 9, 9, 9, 1, 9], [9, 9, 9, 1, 9, 9, 9, 9, 1, 9, 9, 9, 9, 1, 9], [9, 9, 9, 1, 1, 1, 1, 1, 1, 1, 9, 9, 9, 1, 9]], "output": [[1, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 8, 9, 9, 8, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 8, 9, 9, 8, 9, 9, 9, 1, 9, 9, 9], [9, 9, 9, 9, 8, 8, 8, 8, 9, 9, 9, 1, 9, 9, 1], [9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 1, 1, 1, 1], [1, 1, 1, 1, 9, 9, 9, 8, 9, 9, 9, 1, 9, 9, 1], [1, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1], [1, 9, 9, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 1, 1], [1, 1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9], [9, 9, 9, 8, 8, 8, 8, 8, 8, 9, 9, 9, 1, 1, 9], [9, 9, 9, 8, 9, 9, 9, 9, 8, 9, 9, 9, 9, 1, 9], [9, 9, 9, 8, 9, 9, 9, 9, 8, 9, 9, 9, 9, 1, 9], [9, 9, 9, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 1, 9]]}]}