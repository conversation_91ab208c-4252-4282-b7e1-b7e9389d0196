{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 1, 2, 1, 0, 0, 0, 8, 0], [0, 1, 1, 2, 1, 0, 0, 0, 0, 0], [0, 2, 2, 2, 1, 0, 0, 0, 0, 0], [0, 1, 1, 1, 1, 3, 0, 0, 0, 0], [0, 0, 0, 0, 7, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 1, 2, 1, 3, 8, 3, 8, 0], [0, 1, 1, 2, 1, 3, 8, 3, 3, 0], [0, 2, 2, 2, 1, 3, 8, 8, 8, 0], [0, 1, 1, 1, 1, 3, 3, 3, 3, 0], [0, 7, 7, 7, 7, 0, 0, 0, 0, 0], [0, 4, 4, 4, 7, 0, 5, 5, 5, 0], [0, 7, 7, 4, 7, 0, 5, 0, 0, 0], [0, 4, 7, 4, 7, 0, 5, 0, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 9, 0, 0, 0, 0], [0, 3, 3, 8, 8, 7, 0, 0, 0, 0], [0, 3, 8, 3, 8, 0, 0, 0, 0, 0], [0, 3, 8, 8, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 5, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 3, 3, 3, 9, 9, 9, 9, 0], [0, 3, 3, 8, 8, 7, 7, 9, 9, 0], [0, 3, 8, 3, 8, 7, 9, 7, 9, 0], [0, 3, 8, 8, 3, 9, 7, 7, 9, 0], [0, 4, 1, 1, 4, 2, 5, 5, 2, 0], [0, 4, 1, 4, 1, 5, 2, 5, 2, 0], [0, 4, 4, 1, 1, 5, 5, 2, 2, 0], [0, 4, 4, 4, 4, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 2, 8, 8, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 6, 0, 0, 0, 0], [0, 8, 2, 8, 8, 0, 0, 0, 0, 0], [0, 8, 2, 8, 8, 0, 1, 0, 0, 0], [0, 0, 5, 4, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 2, 8, 8, 1, 1, 6, 1, 0], [0, 2, 2, 2, 2, 6, 6, 6, 6, 0], [0, 8, 2, 8, 8, 1, 1, 6, 1, 0], [0, 8, 2, 8, 8, 1, 1, 6, 1, 0], [0, 4, 5, 4, 4, 3, 3, 1, 3, 0], [0, 4, 5, 4, 4, 3, 3, 1, 3, 0], [0, 5, 5, 5, 5, 1, 1, 1, 1, 0], [0, 4, 5, 4, 4, 3, 3, 1, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 1, 1, 8, 0, 0, 0, 0], [0, 4, 1, 1, 1, 0, 0, 0, 0, 0], [0, 1, 1, 1, 4, 0, 0, 0, 0, 0], [0, 1, 1, 4, 4, 5, 0, 0, 0, 0], [0, 0, 0, 6, 0, 0, 0, 0, 0, 0], [0, 7, 0, 0, 0, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 1, 1, 8, 8, 5, 5, 0], [0, 4, 1, 1, 1, 8, 8, 8, 5, 0], [0, 1, 1, 1, 4, 5, 8, 8, 8, 0], [0, 1, 1, 4, 4, 5, 5, 8, 8, 0], [0, 7, 7, 6, 6, 0, 0, 3, 3, 0], [0, 7, 7, 7, 6, 0, 3, 3, 3, 0], [0, 6, 7, 7, 7, 3, 3, 3, 0, 0], [0, 6, 6, 7, 7, 3, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}