{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 4, 0, 4, 0, 0, 0, 2, 0, 2, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 3, 0, 3, 0, 0, 0], [8, 0, 0, 8, 0, 0, 0, 3, 3, 3, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 4, 5, 4, 0, 0, 0, 2, 5, 2, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 7, 7, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 3, 5, 3, 0, 0, 0], [8, 7, 7, 8, 0, 0, 0, 3, 3, 3, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0], [0, 8, 0, 8, 0, 0], [0, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0], [0, 8, 8, 8, 0, 0], [0, 8, 5, 8, 0, 0], [0, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0], [4, 4, 4, 0, 0, 0], [4, 0, 4, 0, 0, 0], [4, 0, 4, 0, 0, 0], [4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0], [4, 4, 4, 0, 0, 0], [4, 7, 4, 0, 0, 0], [4, 7, 4, 0, 0, 0], [4, 4, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 4, 0, 0, 3, 3, 3, 0, 0], [0, 4, 4, 4, 4, 0, 0, 3, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 3, 0, 3, 0, 0, 2, 0, 0, 2], [0, 0, 0, 3, 3, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0], [0, 4, 7, 7, 4, 0, 0, 3, 3, 3, 0, 0], [0, 4, 4, 4, 4, 0, 0, 3, 7, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 7, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 3, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 3, 5, 3, 0, 0, 2, 7, 7, 2], [0, 0, 0, 3, 3, 3, 0, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 5, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 4, 0, 0, 2, 2, 2, 2, 0, 0], [0, 4, 4, 4, 0, 0, 2, 0, 0, 2, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 0, 2, 2, 2, 0, 0, 0, 0, 0], [3, 0, 3, 0, 2, 0, 2, 0, 0, 0, 0, 0], [3, 0, 3, 0, 2, 2, 2, 0, 0, 3, 3, 3], [3, 3, 3, 0, 0, 0, 0, 0, 0, 3, 0, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 5, 4, 0, 0, 2, 2, 2, 2, 0, 0], [0, 4, 4, 4, 0, 0, 2, 7, 7, 2, 0, 0], [0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 0, 2, 2, 2, 0, 0, 0, 0, 0], [3, 7, 3, 0, 2, 5, 2, 0, 0, 0, 0, 0], [3, 7, 3, 0, 2, 2, 2, 0, 0, 3, 3, 3], [3, 3, 3, 0, 0, 0, 0, 0, 0, 3, 5, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}