import { useState, useCallback } from 'react';

export const useExpandedSections = (defaultExpanded: string[] = []) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(defaultExpanded);

  const handleAccordionChange = useCallback((section: string) => 
    (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedSections(prev =>
        isExpanded
          ? [...prev, section]
          : prev.filter(s => s !== section)
      );
    }, []
  );

  const isExpanded = useCallback((section: string) => 
    expandedSections.includes(section), [expandedSections]
  );

  return {
    expandedSections,
    handleAccordionChange,
    isExpanded
  };
};