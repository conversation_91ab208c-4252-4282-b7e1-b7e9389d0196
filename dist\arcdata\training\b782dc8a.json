{"train": [{"input": [[8, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0], [0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 8, 0, 0, 8, 0, 8, 0], [8, 8, 8, 0, 8, 0, 8, 8, 8, 8, 0, 8, 8, 8, 0, 8, 0, 8, 8, 8, 8, 0, 8, 0], [8, 0, 0, 0, 8, 0, 8, 0, 0, 8, 0, 0, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 8, 0], [8, 0, 8, 8, 8, 0, 8, 8, 0, 8, 0, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8, 8, 8, 0], [8, 0, 8, 0, 0, 0, 0, 8, 0, 8, 0, 8, 0, 0, 0, 0, 8, 0, 8, 0, 0, 0, 0, 0], [8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 3, 2, 3, 0, 0, 0, 8, 0], [8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 0, 8, 0], [0, 8, 0, 8, 0, 8, 0, 8, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 8, 0, 8, 0, 8, 0], [0, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 0]], "output": [[8, 3, 2, 3, 2, 3, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 2, 8, 8, 0, 8, 8, 8, 0], [3, 2, 8, 8, 8, 2, 3, 2, 3, 2, 3, 8, 0, 0, 0, 8, 3, 8, 0, 0, 8, 2, 8, 0], [8, 8, 8, 0, 8, 3, 8, 8, 8, 8, 2, 8, 8, 8, 0, 8, 2, 8, 8, 8, 8, 3, 8, 0], [8, 0, 0, 0, 8, 2, 8, 0, 0, 8, 3, 2, 3, 8, 0, 8, 3, 2, 3, 2, 3, 2, 8, 0], [8, 0, 8, 8, 8, 3, 8, 8, 0, 8, 2, 8, 8, 8, 0, 8, 8, 3, 8, 8, 8, 8, 8, 0], [8, 0, 8, 2, 3, 2, 3, 8, 0, 8, 3, 8, 0, 0, 0, 0, 8, 2, 8, 0, 0, 0, 0, 0], [8, 0, 8, 8, 8, 8, 8, 8, 0, 8, 2, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 8, 8, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 8, 0], [8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 3, 8, 8, 8, 3, 8, 0], [0, 8, 0, 8, 0, 8, 0, 8, 3, 2, 3, 8, 0, 0, 0, 0, 8, 2, 8, 0, 8, 2, 8, 0], [0, 8, 8, 8, 0, 8, 8, 8, 2, 8, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 0]]}, {"input": [[0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0, 0, 0, 8], [8, 8, 0, 8, 8, 8, 0, 8, 0, 8, 8, 8, 0, 8], [0, 8, 0, 0, 0, 8, 0, 8, 0, 8, 0, 8, 8, 8], [0, 8, 8, 8, 8, 8, 0, 8, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 0, 8, 8, 8, 0, 8], [8, 8, 8, 8, 8, 8, 0, 8, 0, 0, 0, 8, 0, 8], [8, 0, 0, 0, 0, 8, 0, 8, 8, 8, 0, 8, 0, 8], [8, 8, 8, 8, 0, 8, 0, 0, 0, 8, 0, 8, 0, 0], [0, 0, 0, 8, 1, 8, 8, 8, 8, 8, 0, 8, 8, 0], [8, 8, 0, 8, 4, 1, 0, 0, 0, 0, 0, 0, 8, 0], [0, 8, 0, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 0], [0, 8, 8, 8, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8]], "output": [[0, 0, 0, 8, 0, 0, 0, 8, 1, 4, 1, 4, 1, 8], [8, 8, 0, 8, 8, 8, 0, 8, 4, 8, 8, 8, 4, 8], [0, 8, 0, 0, 0, 8, 0, 8, 1, 8, 0, 8, 8, 8], [0, 8, 8, 8, 8, 8, 0, 8, 4, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 1, 8, 8, 8, 0, 8], [8, 8, 8, 8, 8, 8, 0, 8, 4, 1, 4, 8, 0, 8], [8, 4, 1, 4, 1, 8, 0, 8, 8, 8, 1, 8, 0, 8], [8, 8, 8, 8, 4, 8, 0, 0, 0, 8, 4, 8, 0, 0], [0, 0, 0, 8, 1, 8, 8, 8, 8, 8, 1, 8, 8, 0], [8, 8, 0, 8, 4, 1, 4, 1, 4, 1, 4, 1, 8, 0], [1, 8, 0, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 0], [4, 8, 8, 8, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0], [1, 4, 1, 4, 1, 8, 0, 8, 8, 8, 8, 8, 8, 8]]}], "test": [{"input": [[8, 8, 0, 8, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 8, 8, 8, 8, 4, 8, 8, 8, 8, 8, 8, 8], [0, 8, 0, 0, 0, 0, 4, 3, 8, 0, 0, 0, 0, 0, 8], [0, 8, 8, 8, 8, 8, 8, 4, 8, 8, 8, 0, 8, 8, 8], [0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 8, 0, 0], [8, 8, 8, 8, 8, 0, 8, 8, 8, 0, 8, 0, 8, 0, 8], [0, 0, 0, 0, 8, 0, 0, 0, 8, 0, 8, 0, 8, 0, 8], [8, 8, 8, 0, 8, 8, 8, 0, 8, 0, 8, 0, 8, 8, 8], [0, 0, 8, 0, 0, 0, 8, 0, 8, 0, 8, 0, 0, 0, 0], [8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 8, 8, 8, 0, 8], [8, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0, 8], [8, 8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 0, 8], [0, 0, 8, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 0, 8], [8, 0, 8, 8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8], [8, 0, 0, 0, 0, 0, 8, 0, 8, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 4, 8, 0, 0, 8, 3, 4, 3, 4, 3, 4, 3, 4], [0, 8, 3, 8, 8, 8, 8, 4, 8, 8, 8, 8, 8, 8, 8], [0, 8, 4, 3, 4, 3, 4, 3, 8, 0, 0, 0, 0, 0, 8], [0, 8, 8, 8, 8, 8, 8, 4, 8, 8, 8, 0, 8, 8, 8], [0, 0, 0, 0, 0, 0, 8, 3, 4, 3, 8, 0, 8, 0, 0], [8, 8, 8, 8, 8, 0, 8, 8, 8, 4, 8, 0, 8, 0, 8], [4, 3, 4, 3, 8, 0, 0, 0, 8, 3, 8, 0, 8, 0, 8], [8, 8, 8, 4, 8, 8, 8, 0, 8, 4, 8, 0, 8, 8, 8], [0, 0, 8, 3, 4, 3, 8, 0, 8, 3, 8, 0, 0, 0, 0], [8, 0, 8, 8, 8, 4, 8, 8, 8, 4, 8, 8, 8, 0, 8], [8, 0, 0, 0, 8, 3, 4, 3, 4, 3, 4, 3, 8, 0, 8], [8, 8, 8, 0, 8, 4, 8, 8, 8, 8, 8, 8, 8, 0, 8], [4, 3, 8, 0, 8, 3, 8, 0, 0, 0, 0, 0, 0, 0, 8], [8, 4, 8, 8, 8, 4, 8, 0, 8, 8, 8, 8, 8, 8, 8], [8, 3, 4, 3, 4, 3, 8, 0, 8, 0, 0, 0, 0, 0, 0]]}]}