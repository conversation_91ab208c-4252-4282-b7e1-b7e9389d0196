{"train": [{"input": [[0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 5, 6, 6, 0], [8, 8, 8, 5, 6, 0, 0], [8, 8, 8, 5, 6, 0, 0], [8, 8, 8, 5, 0, 0, 0], [8, 8, 8, 5, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 5, 6, 0, 0, 0, 0], [8, 8, 5, 6, 0, 0, 0, 0], [8, 8, 5, 0, 0, 0, 0, 0], [8, 8, 5, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 8, 5, 6, 6, 6], [8, 8, 8, 8, 5, 6, 6, 0], [8, 8, 8, 8, 5, 6, 6, 0], [8, 8, 8, 8, 5, 6, 0, 0], [8, 8, 8, 8, 5, 6, 0, 0], [8, 8, 8, 8, 5, 0, 0, 0], [8, 8, 8, 8, 5, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 5, 6, 6, 6, 0, 0], [8, 8, 8, 5, 6, 6, 0, 0, 0], [8, 8, 8, 5, 6, 6, 0, 0, 0], [8, 8, 8, 5, 6, 0, 0, 0, 0], [8, 8, 8, 5, 6, 0, 0, 0, 0], [8, 8, 8, 5, 0, 0, 0, 0, 0], [8, 8, 8, 5, 0, 0, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0, 0, 0], [8, 8, 8, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0, 0], [8, 8, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}