{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8], [8, 8, 8, 6, 6, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8], [8, 8, 8, 6, 6, 8, 8, 8, 6, 8, 8, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 3, 6, 6, 6, 6, 3, 8, 8], [8, 8, 3, 3, 3, 3, 8, 3, 6, 4, 4, 6, 3, 8, 8], [8, 8, 3, 6, 6, 3, 8, 3, 6, 4, 4, 6, 3, 8, 8], [8, 8, 3, 6, 6, 3, 8, 3, 6, 4, 4, 6, 3, 8, 8], [8, 8, 3, 3, 3, 3, 8, 3, 6, 6, 6, 6, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 6, 6, 6, 6, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 6, 6, 6, 6, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 6, 6, 6, 6, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 6, 6, 6, 6, 3, 8, 8, 8, 8, 8], [8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 6, 8, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 8, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 8, 8, 8, 8, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6, 6, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 3, 6, 6, 6, 3, 8, 8, 8], [8, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 3, 8, 8, 8], [8, 3, 6, 6, 6, 6, 3, 3, 6, 6, 6, 3, 8, 8, 8], [8, 3, 6, 4, 6, 6, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 3, 6, 4, 6, 6, 3, 8, 8, 8, 8, 8, 8, 8, 8], [8, 3, 6, 6, 6, 6, 3, 8, 8, 8, 8, 8, 8, 8, 8], [8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 6, 6, 6, 6, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 4, 4, 4, 4, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 4, 4, 4, 4, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 4, 4, 4, 4, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 4, 4, 4, 4, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 6, 6, 6, 6, 6, 6, 3], [8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 8, 6, 6, 6, 8, 8, 8], [8, 8, 6, 8, 8, 6, 8, 8, 8, 6, 8, 6, 8, 8, 8], [8, 8, 6, 8, 8, 6, 8, 8, 8, 6, 8, 6, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 8, 6, 8, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8, 8], [8, 8, 8, 8, 6, 6, 8, 8, 6, 6, 6, 8, 8, 8, 8], [8, 8, 8, 8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 8, 8], [8, 3, 3, 3, 3, 3, 3, 8, 3, 6, 6, 6, 3, 8, 8], [8, 3, 6, 6, 6, 6, 3, 8, 3, 6, 6, 6, 3, 8, 8], [8, 3, 6, 4, 4, 6, 3, 8, 3, 6, 4, 6, 3, 8, 8], [8, 3, 6, 4, 4, 6, 3, 8, 3, 6, 4, 6, 3, 8, 8], [8, 3, 6, 6, 6, 6, 3, 8, 3, 6, 4, 6, 3, 8, 8], [8, 3, 3, 3, 3, 3, 3, 8, 3, 6, 6, 6, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 8, 3, 6, 6, 6, 6, 6, 6, 6, 3, 8, 8, 8], [8, 8, 8, 3, 6, 6, 4, 4, 6, 6, 6, 3, 8, 8, 8], [8, 8, 8, 3, 6, 6, 6, 6, 6, 6, 6, 3, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8]]}]}