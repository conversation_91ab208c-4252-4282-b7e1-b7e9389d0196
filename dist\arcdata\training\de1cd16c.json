{"train": [{"input": [[4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 6, 4, 4, 4, 4, 0, 0, 6, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 6, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 6, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 6, 1], [8, 8, 6, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 6, 8, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 1, 1]], "output": [[8]]}, {"input": [[3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 1, 2, 2, 1, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 1, 2, 2, 2, 2, 2], [3, 3, 1, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 1, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 8, 8, 8], [8, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[2]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 4, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 1, 4, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5], [1, 1, 4, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 4, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 4, 6, 6, 6, 6, 4, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 4, 6, 6, 6], [0, 0, 0, 0, 0, 0, 0, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6]], "output": [[6]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 2, 4, 4, 4, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 2, 4, 4, 4, 4, 4, 4], [1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]], "output": [[4]]}], "test": [{"input": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 4, 2, 2, 2, 2, 2, 4, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 4, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 4, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 4, 2, 2, 8, 8, 8, 8, 8, 4, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 2, 8, 8, 8, 4, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 4, 8, 8, 8, 8], [1, 1, 1, 1, 4, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 4, 1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[2]]}]}