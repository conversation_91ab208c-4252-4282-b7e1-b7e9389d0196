{"train": [{"input": [[0, 2, 0, 0, 0, 0, 4, 5, 0, 0, 1, 0, 6, 5, 0, 0, 0], [9, 0, 4, 3, 0, 0, 9, 0, 4, 7, 9, 4, 6, 0, 2, 7, 0], [0, 7, 3, 0, 0, 0, 9, 0, 0, 9, 0, 0, 9, 9, 9, 5, 0], [0, 5, 5, 3, 0, 3, 0, 6, 0, 4, 7, 2, 3, 2, 0, 3, 0], [0, 8, 8, 0, 0, 0, 7, 0, 8, 8, 9, 0, 0, 6, 0, 0, 4], [0, 8, 8, 6, 4, 3, 1, 9, 8, 8, 0, 0, 0, 0, 0, 0, 7], [9, 0, 0, 9, 5, 2, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 1], [0, 2, 9, 9, 0, 0, 9, 0, 7, 1, 0, 0, 0, 9, 0, 0, 0], [0, 7, 0, 8, 8, 0, 4, 0, 6, 0, 8, 8, 9, 0, 0, 0, 0], [0, 2, 4, 8, 8, 0, 3, 0, 0, 6, 8, 8, 6, 5, 7, 9, 0], [0, 0, 9, 2, 0, 2, 0, 0, 0, 7, 9, 0, 0, 0, 5, 7, 1], [1, 0, 0, 3, 0, 1, 0, 4, 1, 4, 0, 0, 0, 0, 1, 0, 9], [1, 0, 6, 2, 1, 4, 6, 0, 0, 1, 9, 0, 3, 0, 1, 4, 0]], "output": [[0, 0, 0, 7, 0], [6, 4, 3, 1, 9], [0, 4, 0, 6, 0], [0, 3, 0, 0, 6]]}, {"input": [[0, 4, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 7, 9, 0, 7, 7, 0, 0, 1, 3, 0], [2, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 8, 8, 3, 5, 9, 1, 8, 8, 0, 2, 0], [0, 0, 0, 0, 8, 8, 1, 0, 0, 6, 8, 8, 3, 0, 0], [2, 0, 0, 0, 5, 0, 0, 0, 0, 0, 9, 2, 0, 0, 2], [0, 0, 9, 0, 4, 9, 9, 9, 0, 2, 9, 6, 1, 4, 0], [0, 0, 0, 0, 0, 0, 9, 4, 0, 0, 0, 0, 0, 0, 5], [1, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 5, 0, 6, 0], [2, 1, 0, 0, 6, 0, 6, 2, 7, 0, 4, 0, 0, 0, 7], [0, 9, 0, 0, 2, 0, 5, 0, 1, 0, 0, 0, 0, 5, 3], [4, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0]], "output": [[3, 5, 9, 1], [1, 0, 0, 6]]}, {"input": [[9, 0, 0, 5, 0, 0, 0, 0, 4, 4], [9, 4, 0, 0, 0, 0, 0, 0, 5, 0], [2, 2, 0, 6, 0, 0, 5, 0, 5, 3], [2, 9, 0, 2, 6, 4, 0, 1, 0, 0], [0, 0, 2, 9, 0, 4, 9, 1, 1, 3], [8, 8, 1, 0, 9, 7, 7, 0, 8, 8], [8, 8, 4, 0, 0, 5, 6, 4, 8, 8], [0, 5, 9, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 1, 0, 5, 0, 0, 3, 0], [0, 9, 0, 0, 0, 0, 0, 7, 0, 9], [0, 0, 5, 1, 7, 0, 0, 0, 9, 9], [0, 0, 9, 0, 0, 1, 0, 0, 0, 7]], "output": [[1, 0, 9, 7, 7, 0], [4, 0, 0, 5, 6, 4]]}, {"input": [[9, 2, 1, 5, 3, 4, 3, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 8, 8, 3, 0, 7, 0, 7, 8, 8, 4, 0, 7, 2, 0, 0, 0], [1, 8, 8, 0, 2, 0, 0, 6, 8, 8, 0, 0, 0, 0, 0, 7, 0], [1, 0, 0, 0, 0, 4, 1, 3, 9, 1, 0, 7, 5, 9, 4, 7, 0], [0, 0, 3, 2, 2, 0, 2, 6, 0, 4, 9, 2, 4, 0, 3, 0, 5], [0, 6, 8, 8, 3, 0, 1, 9, 2, 8, 8, 0, 3, 0, 4, 0, 0], [0, 0, 8, 8, 0, 7, 9, 2, 9, 8, 8, 0, 9, 3, 0, 0, 9], [0, 0, 0, 4, 0, 7, 5, 7, 5, 0, 1, 3, 0, 2, 0, 0, 0], [0, 0, 9, 9, 3, 6, 4, 0, 4, 7, 2, 0, 9, 0, 0, 9, 0], [9, 1, 9, 0, 0, 7, 1, 5, 7, 1, 0, 5, 0, 5, 9, 6, 9], [0, 0, 3, 7, 2, 0, 8, 8, 9, 0, 0, 0, 0, 8, 8, 1, 0], [6, 7, 0, 4, 0, 4, 8, 8, 0, 4, 0, 2, 0, 8, 8, 5, 0]], "output": [[3, 0, 7, 0, 7], [0, 2, 0, 0, 6], [3, 0, 1, 9, 2], [0, 7, 9, 2, 9], [9, 0, 0, 0, 0], [0, 4, 0, 2, 0]]}, {"input": [[0, 7, 2, 7, 0, 2, 0, 0, 0, 4, 0, 0, 1, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 6, 0, 0, 2, 0, 0, 7, 3, 1], [0, 0, 8, 8, 6, 5, 2, 8, 8, 1, 0, 2, 4, 5, 0, 0], [0, 0, 8, 8, 0, 0, 2, 8, 8, 0, 0, 7, 1, 0, 0, 7], [0, 0, 0, 0, 4, 0, 0, 0, 9, 0, 7, 0, 0, 0, 0, 0], [8, 8, 1, 3, 0, 8, 8, 0, 0, 0, 0, 9, 0, 3, 0, 1], [8, 8, 0, 0, 9, 8, 8, 0, 0, 0, 0, 0, 3, 0, 9, 2], [0, 0, 7, 0, 0, 0, 0, 0, 0, 9, 3, 4, 0, 0, 0, 0], [4, 0, 0, 9, 0, 9, 0, 0, 7, 3, 0, 6, 0, 4, 0, 5], [6, 0, 0, 0, 4, 0, 0, 3, 0, 0, 2, 0, 5, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 1, 2, 0, 4, 0, 0, 0, 0], [4, 5, 0, 0, 6, 0, 4, 0, 0, 0, 0, 0, 5, 2, 0, 2], [0, 9, 0, 6, 0, 0, 0, 7, 2, 0, 9, 3, 0, 0, 0, 6]], "output": [[6, 5, 2], [0, 0, 2], [1, 3, 0], [0, 0, 9]]}], "test": [{"input": [[0, 0, 6, 9, 0, 0, 0, 9, 0, 0, 7, 0, 9, 0, 0, 9, 0], [0, 0, 0, 0, 0, 0, 0, 6, 7, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 4, 0, 9, 0, 0, 0, 0, 0, 2, 0, 1, 0, 5, 1], [2, 1, 0, 8, 8, 4, 1, 5, 0, 8, 8, 0, 1, 0, 4, 0, 0], [0, 7, 3, 8, 8, 0, 9, 0, 0, 8, 8, 0, 6, 0, 4, 7, 2], [2, 5, 0, 4, 0, 0, 0, 0, 7, 9, 0, 9, 5, 0, 4, 0, 1], [8, 8, 5, 9, 0, 4, 8, 8, 4, 0, 3, 7, 0, 0, 0, 0, 5], [8, 8, 7, 7, 0, 0, 8, 8, 6, 4, 7, 0, 6, 0, 0, 0, 4], [0, 6, 9, 0, 4, 0, 0, 3, 0, 9, 0, 3, 0, 0, 0, 3, 4], [0, 5, 2, 0, 0, 0, 0, 2, 9, 0, 0, 6, 0, 4, 5, 0, 0], [0, 7, 0, 3, 8, 8, 4, 5, 4, 3, 8, 8, 9, 5, 0, 3, 0], [0, 0, 0, 0, 8, 8, 0, 0, 7, 0, 8, 8, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 0, 3, 5, 0], [0, 9, 2, 0, 0, 0, 9, 8, 8, 0, 0, 6, 0, 8, 8, 0, 6], [0, 0, 0, 9, 0, 0, 0, 8, 8, 0, 7, 0, 4, 8, 8, 0, 0]], "output": [[4, 1, 5, 0], [0, 9, 0, 0], [5, 9, 0, 4], [7, 7, 0, 0], [4, 5, 4, 3], [0, 0, 7, 0], [0, 0, 6, 0], [0, 7, 0, 4]]}]}