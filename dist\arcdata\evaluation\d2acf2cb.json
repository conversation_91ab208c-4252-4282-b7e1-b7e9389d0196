{"train": [{"input": [[0, 6, 0, 0, 0, 6, 6, 0, 0], [6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 6, 6, 6, 6, 0, 0, 0, 0], [6, 6, 0, 0, 0, 6, 6, 0, 0], [0, 6, 6, 6, 0, 0, 6, 0, 6], [4, 0, 0, 6, 6, 6, 6, 0, 4], [0, 6, 6, 6, 0, 6, 6, 0, 0]], "output": [[0, 6, 0, 0, 0, 6, 6, 0, 0], [6, 6, 6, 6, 6, 6, 6, 6, 6], [0, 6, 6, 6, 6, 0, 0, 0, 0], [6, 6, 0, 0, 0, 6, 6, 0, 0], [0, 6, 6, 6, 0, 0, 6, 0, 6], [4, 8, 8, 7, 7, 7, 7, 8, 4], [0, 6, 6, 6, 0, 6, 6, 0, 0]]}, {"input": [[0, 6, 0, 6, 6, 0, 6, 0, 6], [4, 7, 8, 7, 8, 8, 8, 8, 4], [0, 6, 6, 6, 6, 6, 6, 6, 0], [0, 0, 6, 0, 6, 6, 0, 0, 6], [4, 8, 7, 7, 7, 7, 8, 8, 4], [0, 0, 0, 0, 6, 0, 0, 0, 6], [6, 0, 6, 0, 6, 0, 0, 6, 0], [4, 7, 8, 8, 7, 8, 7, 7, 4], [6, 6, 0, 6, 0, 6, 6, 0, 0]], "output": [[0, 6, 0, 6, 6, 0, 6, 0, 6], [4, 6, 0, 6, 0, 0, 0, 0, 4], [0, 6, 6, 6, 6, 6, 6, 6, 0], [0, 0, 6, 0, 6, 6, 0, 0, 6], [4, 0, 6, 6, 6, 6, 0, 0, 4], [0, 0, 0, 0, 6, 0, 0, 0, 6], [6, 0, 6, 0, 6, 0, 0, 6, 0], [4, 6, 0, 0, 6, 0, 6, 6, 4], [6, 6, 0, 6, 0, 6, 6, 0, 0]]}, {"input": [[6, 0, 6, 4, 6, 0, 0, 4, 6], [6, 0, 6, 0, 0, 6, 0, 0, 6], [0, 6, 6, 0, 0, 0, 0, 6, 0], [6, 6, 6, 0, 0, 0, 0, 6, 6], [6, 0, 0, 6, 6, 0, 0, 0, 6], [6, 6, 6, 4, 0, 6, 6, 4, 0]], "output": [[6, 0, 6, 4, 6, 0, 0, 4, 6], [6, 0, 6, 8, 0, 6, 0, 8, 6], [0, 6, 6, 8, 0, 0, 0, 7, 0], [6, 6, 6, 8, 0, 0, 0, 7, 6], [6, 0, 0, 7, 6, 0, 0, 8, 6], [6, 6, 6, 4, 0, 6, 6, 4, 0]]}], "test": [{"input": [[0, 4, 6, 6, 0, 4, 6, 4, 0], [0, 6, 0, 0, 0, 6, 6, 6, 0], [0, 0, 0, 6, 0, 0, 6, 6, 6], [6, 6, 6, 0, 0, 0, 6, 0, 0], [0, 6, 0, 6, 0, 0, 6, 0, 0], [0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 6, 6, 0, 6, 0, 6, 6], [0, 6, 0, 6, 6, 6, 6, 6, 6], [6, 0, 0, 0, 6, 0, 0, 6, 0], [0, 4, 0, 0, 6, 4, 6, 4, 0]], "output": [[0, 4, 6, 6, 0, 4, 6, 4, 0], [0, 7, 0, 0, 0, 7, 6, 7, 0], [0, 8, 0, 6, 0, 8, 6, 7, 6], [6, 7, 6, 0, 0, 8, 6, 8, 0], [0, 7, 0, 6, 0, 8, 6, 8, 0], [0, 7, 6, 0, 6, 7, 0, 7, 6], [6, 7, 6, 6, 0, 7, 0, 7, 6], [0, 7, 0, 6, 6, 7, 6, 7, 6], [6, 8, 0, 0, 6, 8, 0, 7, 0], [0, 4, 0, 0, 6, 4, 6, 4, 0]]}]}