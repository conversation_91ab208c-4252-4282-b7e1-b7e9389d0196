{"train": [{"input": [[6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6]], "output": [[6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6]]}, {"input": [[7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 0, 0, 7, 7, 0, 7, 0, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 0], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 7, 0, 7, 0, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 0, 0, 7, 7, 0, 7, 0, 0, 7, 0], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7]], "output": [[7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 7, 0, 7, 0], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 0, 0, 7, 0, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 7, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 0, 0, 7, 0, 0, 7, 7, 0, 7, 0, 0, 7, 7], [7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0, 7, 7]]}, {"input": [[1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1]], "output": [[1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1]]}, {"input": [[1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1]], "output": [[1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1]]}], "test": [{"input": [[6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6]], "output": [[6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 0], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6], [6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 0, 0, 6, 6], [6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0, 6, 6]]}]}