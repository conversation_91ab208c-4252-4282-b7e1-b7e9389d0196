{"train": [{"input": [[0, 0, 0, 0, 8, 0, 5, 5, 0, 8, 0, 0, 0, 0], [0, 2, 2, 0, 8, 0, 0, 5, 0, 8, 0, 0, 0, 0], [0, 2, 2, 0, 8, 5, 5, 5, 5, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 5, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 3, 3, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 3, 3, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 1, 1, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 1, 1, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0]], "output": [[0, 2, 2, 0, 8, 0, 2, 2, 0, 8, 0, 2, 2, 0], [0, 0, 2, 0, 8, 0, 0, 2, 0, 8, 0, 0, 2, 0], [2, 2, 2, 2, 8, 2, 2, 2, 2, 8, 2, 2, 2, 2], [0, 2, 0, 0, 8, 0, 2, 0, 0, 8, 0, 2, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 3, 3, 0, 8, 0, 3, 3, 0, 8, 0, 3, 3, 0], [0, 0, 3, 0, 8, 0, 0, 3, 0, 8, 0, 0, 3, 0], [3, 3, 3, 3, 8, 3, 3, 3, 3, 8, 3, 3, 3, 3], [0, 3, 0, 0, 8, 0, 3, 0, 0, 8, 0, 3, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 1, 1, 0, 8, 0, 1, 1, 0, 8, 0, 1, 1, 0], [0, 0, 1, 0, 8, 0, 0, 1, 0, 8, 0, 0, 1, 0], [1, 1, 1, 1, 8, 1, 1, 1, 1, 8, 1, 1, 1, 1], [0, 1, 0, 0, 8, 0, 1, 0, 0, 8, 0, 1, 0, 0]]}, {"input": [[0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 4, 4, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 4, 4, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 2, 2, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 2, 2, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 8, 8, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 8, 8, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 1, 1, 0, 3, 6, 6, 0, 6, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 1, 1, 0, 3, 0, 6, 6, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 0, 6, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [4, 4, 0, 4, 3, 4, 4, 0, 4, 3, 4, 4, 0, 4, 3, 4, 4, 0, 4], [0, 4, 4, 0, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0], [0, 0, 4, 0, 3, 0, 0, 4, 0, 3, 0, 0, 4, 0, 3, 0, 0, 4, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [2, 2, 0, 2, 3, 2, 2, 0, 2, 3, 2, 2, 0, 2, 3, 2, 2, 0, 2], [0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 0, 2, 2, 0], [0, 0, 2, 0, 3, 0, 0, 2, 0, 3, 0, 0, 2, 0, 3, 0, 0, 2, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [8, 8, 0, 8, 3, 8, 8, 0, 8, 3, 8, 8, 0, 8, 3, 8, 8, 0, 8], [0, 8, 8, 0, 3, 0, 8, 8, 0, 3, 0, 8, 8, 0, 3, 0, 8, 8, 0], [0, 0, 8, 0, 3, 0, 0, 8, 0, 3, 0, 0, 8, 0, 3, 0, 0, 8, 0], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0, 0, 0], [1, 1, 0, 1, 3, 1, 1, 0, 1, 3, 1, 1, 0, 1, 3, 1, 1, 0, 1], [0, 1, 1, 0, 3, 0, 1, 1, 0, 3, 0, 1, 1, 0, 3, 0, 1, 1, 0], [0, 0, 1, 0, 3, 0, 0, 1, 0, 3, 0, 0, 1, 0, 3, 0, 0, 1, 0]]}], "test": [{"input": [[0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 2, 2, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 2, 2, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 1, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 3, 3, 0, 5, 0, 0, 0, 0, 5, 1, 1, 1, 1, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 3, 3, 0, 5, 0, 0, 0, 0, 5, 0, 1, 1, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 1, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 4, 4, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 4, 4, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 6, 6, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 6, 6, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 7, 7, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 7, 7, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0]], "output": [[0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0], [2, 2, 2, 2, 5, 2, 2, 2, 2, 5, 2, 2, 2, 2, 5, 2, 2, 2, 2, 5, 2, 2, 2, 2], [0, 2, 2, 0, 5, 0, 2, 2, 0, 5, 0, 2, 2, 0, 5, 0, 2, 2, 0, 5, 0, 2, 2, 0], [0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0, 5, 0, 0, 2, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0], [3, 3, 3, 3, 5, 3, 3, 3, 3, 5, 3, 3, 3, 3, 5, 3, 3, 3, 3, 5, 3, 3, 3, 3], [0, 3, 3, 0, 5, 0, 3, 3, 0, 5, 0, 3, 3, 0, 5, 0, 3, 3, 0, 5, 0, 3, 3, 0], [0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0, 5, 0, 0, 3, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0], [4, 4, 4, 4, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4], [0, 4, 4, 0, 5, 0, 4, 4, 0, 5, 0, 4, 4, 0, 5, 0, 4, 4, 0, 5, 0, 4, 4, 0], [0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0, 5, 0, 0, 4, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0], [6, 6, 6, 6, 5, 6, 6, 6, 6, 5, 6, 6, 6, 6, 5, 6, 6, 6, 6, 5, 6, 6, 6, 6], [0, 6, 6, 0, 5, 0, 6, 6, 0, 5, 0, 6, 6, 0, 5, 0, 6, 6, 0, 5, 0, 6, 6, 0], [0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0, 5, 0, 0, 6, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0], [7, 7, 7, 7, 5, 7, 7, 7, 7, 5, 7, 7, 7, 7, 5, 7, 7, 7, 7, 5, 7, 7, 7, 7], [0, 7, 7, 0, 5, 0, 7, 7, 0, 5, 0, 7, 7, 0, 5, 0, 7, 7, 0, 5, 0, 7, 7, 0], [0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0, 5, 0, 0, 7, 0]]}]}