import React from 'react';
import { Stack, Typography, Alert } from '@mui/material';
import { Psychology as PsychologyIcon } from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { Level3PanelProps } from '../types/AnalysisPanelTypes';

export const Level3Panel: React.FC<Level3PanelProps> = ({ analysis }) => {
  return (
    <Stack spacing={2}>
      <LevelHeader
        level={3}
        title="Niveau 3 : Synthèse par Entraînement"
        description="Synthèse des patterns récurrents à travers tous les exemples d'entraînement."
        icon={<PsychologyIcon />}
        hasData={!!analysis.level_3}
      />

      {analysis.level_3 ? (
        <Alert severity="success" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Synthèse disponible (implémentation détaillée à venir)
          </Typography>
        </Alert>
      ) : (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Le Niveau 3 (synthèse par entraînement) nécessite plusieurs exemples d'entraînement pour fonctionner.
          </Typography>
        </Alert>
      )}
    </Stack>
  );
};