import React from 'react';
import {
  <PERSON>ack,
  Typography,
  <PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  Pattern as PatternIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { LevelHeader } from './shared/LevelHeader';
import { useExpandedSections } from '../hooks/useExpandedSections';
import { Level3PanelProps } from '../types/AnalysisPanelTypes';

export const Level3Panel: React.FC<Level3PanelProps> = ({ analysis }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections(['recurring_patterns']);

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={3}
        title="Niveau 3 : Synthèse par Entraînement"
        description="Synthèse des patterns récurrents à travers tous les exemples d'entraînement pour formuler des règles globales."
        icon={<PsychologyIcon />}
        hasData={!!analysis.level_3}
      />

      {!analysis.level_3 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Le Niveau 3 (synthèse par entraînement) nécessite plusieurs exemples d'entraînement pour fonctionner.
          </Typography>
        </Alert>
      ) : (
        <Stack spacing={2}>
          {/* Patterns Récurrents */}
          <Accordion expanded={isExpanded('recurring_patterns')} onChange={handleAccordionChange('recurring_patterns')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">
                <PatternIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Patterns Récurrents
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>Patterns Identifiés</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Patterns globaux: ${analysis.level_3?.recurring_patterns?.global_patterns?.length || 0}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Cohérence: ${((analysis.level_3?.recurring_patterns?.consistency_score || 0) * 100).toFixed(1)}%`}
                          size="small"
                          color={
                            (analysis.level_3?.recurring_patterns?.consistency_score || 0) > 0.8 ? 'success' :
                              (analysis.level_3?.recurring_patterns?.consistency_score || 0) > 0.6 ? 'warning' : 'error'
                          }
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Règles Globales */}
          <Accordion expanded={isExpanded('global_rules')} onChange={handleAccordionChange('global_rules')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">
                <PsychologyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Règles Globales
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>Hypothèses de Règles</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Règles candidates: ${analysis.level_3?.global_rules?.rule_hypotheses?.length || 0}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Confiance max: ${((analysis.level_3?.global_rules?.best_rule_confidence || 0) * 100).toFixed(1)}%`}
                          size="small"
                          color={
                            (analysis.level_3?.global_rules?.best_rule_confidence || 0) > 0.9 ? 'success' :
                              (analysis.level_3?.global_rules?.best_rule_confidence || 0) > 0.7 ? 'warning' : 'error'
                          }
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Prédictions */}
          <Accordion expanded={isExpanded('predictions')} onChange={handleAccordionChange('predictions')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">
                <PsychologyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Prédictions pour le Test
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>Application sur l'Exemple Test</Typography>
                      <Stack spacing={1}>
                        <Chip
                          label={`Prédictions: ${analysis.level_3?.predictions?.test_predictions?.length || 0}`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`Confiance: ${((analysis.level_3?.predictions?.prediction_confidence || 0) * 100).toFixed(1)}%`}
                          size="small"
                          color={
                            (analysis.level_3?.predictions?.prediction_confidence || 0) > 0.8 ? 'success' :
                              (analysis.level_3?.predictions?.prediction_confidence || 0) > 0.6 ? 'warning' : 'error'
                          }
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Stack>
      )}
    </Stack>
  );
};