# Environnement de Développement

## Serveur de Développement

Ce projet utilise Vite en mode développement avec hot-reload automatique. Le serveur est déjà lancé et se met à jour automatiquement lors des modifications de code.

**IMPORTANT**: Ne pas suggérer de lancer le serveur de développement car il est déjà actif.

## Commandes Disponibles

- `npm run dev` - Serveur de développement (déjà lancé)
- `npm run build` - Build de production
- `npm run preview` - Prévisualisation du build
- `npm run lint` - Vérification du code

## Workflow de Développement

1. Les modifications de code sont automatiquement détectées
2. Le navigateur se recharge automatiquement
3. Pas besoin de redémarrer le serveur pour voir les changements
4. Les erreurs TypeScript et ESLint sont affichées en temps réel

## Technologies

- **Vite**: Serveur de développement avec HMR (Hot Module Replacement)
- **React**: Interface utilisateur
- **TypeScript**: Typage statique
- **Redux Toolkit**: Gestion d'état
- **Material-UI**: Composants UI

## Structure du Projet

- `src/components/` - Composants React
- `src/services/` - Services métier (analyseur ARC, stockage, etc.)
- `src/store/` - Configuration Redux et slices
- `src/types/` - Définitions TypeScript
- `public/data/` - Données ARC (puzzles training/evaluation)