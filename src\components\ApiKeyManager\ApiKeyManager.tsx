import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Stack,
  Typography,
  Box,
  Chip,
  IconButton,
  Alert,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { AI_PROVIDERS } from '../../types';
import { aiProviderService } from '../../services/aiProviderService';

interface ApiKeyManagerProps {
  open: boolean;
  onClose: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const ApiKeyManager: React.FC<ApiKeyManagerProps> = ({ open, onClose }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, boolean | null>>({});
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (open) {
      // Charger les clés API existantes
      const keys: Record<string, string> = {};
      Object.keys(AI_PROVIDERS).forEach(providerId => {
        const key = aiProviderService.getApiKey(providerId);
        if (key) {
          keys[providerId] = key;
        }
      });
      setApiKeys(keys);
    }
  }, [open]);

  const handleApiKeyChange = (providerId: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [providerId]: value,
    }));
  };

  const handleSaveApiKey = (providerId: string) => {
    const key = apiKeys[providerId];
    if (key) {
      aiProviderService.setApiKey(providerId, key);
    }
  };

  const handleTestConnection = async (providerId: string) => {
    const key = apiKeys[providerId];
    if (!key) return;

    setIsLoading(prev => ({ ...prev, [providerId]: true }));
    
    try {
      const result = await aiProviderService.testConnection(providerId, key);
      setTestResults(prev => ({ ...prev, [providerId]: result }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [providerId]: false }));
    } finally {
      setIsLoading(prev => ({ ...prev, [providerId]: false }));
    }
  };

  const toggleShowKey = (providerId: string) => {
    setShowKeys(prev => ({
      ...prev,
      [providerId]: !prev[providerId],
    }));
  };

  const localProviders = Object.values(AI_PROVIDERS).filter(p => p.category === 'local');
  const apiProviders = Object.values(AI_PROVIDERS).filter(p => p.category === 'api');

  const renderProviderConfig = (provider: any) => (
    <Accordion key={provider.id}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Stack direction="row" alignItems="center" spacing={2} sx={{ width: '100%' }}>
          <Typography variant="subtitle1">{provider.name}</Typography>
          {provider.requiresKey && (
            <Chip
              label={apiKeys[provider.id] ? 'Configuré' : 'Non configuré'}
              color={apiKeys[provider.id] ? 'success' : 'default'}
              size="small"
            />
          )}
          {testResults[provider.id] !== undefined && (
            <Chip
              icon={testResults[provider.id] ? <CheckIcon /> : <CloseIcon />}
              label={testResults[provider.id] ? 'Connecté' : 'Échec'}
              color={testResults[provider.id] ? 'success' : 'error'}
              size="small"
            />
          )}
        </Stack>
      </AccordionSummary>
      <AccordionDetails>
        <Stack spacing={2}>
          <Typography variant="body2" color="text.secondary">
            Endpoint: {provider.endpoint}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Modèles: {provider.models.join(', ')}
          </Typography>
          
          {provider.requiresKey ? (
            <Stack spacing={2}>
              <TextField
                fullWidth
                label="Clé API"
                type={showKeys[provider.id] ? 'text' : 'password'}
                value={apiKeys[provider.id] || ''}
                onChange={(e) => handleApiKeyChange(provider.id, e.target.value)}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => toggleShowKey(provider.id)}
                      edge="end"
                    >
                      {showKeys[provider.id] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
              />
              <Stack direction="row" spacing={1}>
                <Button
                  variant="outlined"
                  onClick={() => handleSaveApiKey(provider.id)}
                  disabled={!apiKeys[provider.id]}
                >
                  Sauvegarder
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => handleTestConnection(provider.id)}
                  disabled={!apiKeys[provider.id] || isLoading[provider.id]}
                >
                  {isLoading[provider.id] ? 'Test...' : 'Tester'}
                </Button>
              </Stack>
            </Stack>
          ) : (
            <Stack spacing={2}>
              <Alert severity="info">
                Ce fournisseur ne nécessite pas de clé API. Assurez-vous que le service local est en cours d'exécution.
              </Alert>
              <Button
                variant="outlined"
                onClick={() => handleTestConnection(provider.id)}
                disabled={isLoading[provider.id]}
              >
                {isLoading[provider.id] ? 'Test...' : 'Tester la connexion'}
              </Button>
            </Stack>
          )}
        </Stack>
      </AccordionDetails>
    </Accordion>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Configuration des fournisseurs IA</DialogTitle>
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
            <Tab label="Fournisseurs API" />
            <Tab label="Fournisseurs locaux" />
            <Tab label="Aide" />
          </Tabs>
        </Box>

        <TabPanel value={currentTab} index={0}>
          <Stack spacing={1}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Configurez vos clés API pour les fournisseurs cloud. Les clés sont stockées localement dans votre navigateur.
            </Typography>
            {apiProviders.map(renderProviderConfig)}
          </Stack>
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <Stack spacing={1}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Fournisseurs locaux qui s'exécutent sur votre machine. Aucune clé API requise.
            </Typography>
            {localProviders.map(renderProviderConfig)}
          </Stack>
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <Stack spacing={3}>
            <Box>
              <Typography variant="h6" gutterBottom>
                Comment obtenir les clés API
              </Typography>
              <Stack spacing={2}>
                <Box>
                  <Typography variant="subtitle2">OpenAI</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Visitez https://platform.openai.com/api-keys pour créer une clé API
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2">Groq</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Visitez https://console.groq.com/keys pour obtenir votre clé API
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2">OpenRouter</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Visitez https://openrouter.ai/keys pour créer une clé API
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2">Perplexity</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Visitez https://www.perplexity.ai/settings/api pour obtenir votre clé
                  </Typography>
                </Box>
              </Stack>
            </Box>

            <Box>
              <Typography variant="h6" gutterBottom>
                Fournisseurs locaux
              </Typography>
              <Stack spacing={2}>
                <Box>
                  <Typography variant="subtitle2">Ollama</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Installez Ollama depuis https://ollama.ai et lancez un modèle avec `ollama run llama2`
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2">LM Studio</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Téléchargez LM Studio depuis https://lmstudio.ai et démarrez le serveur local
                  </Typography>
                </Box>
              </Stack>
            </Box>

            <Alert severity="warning">
              <Typography variant="body2">
                <strong>Sécurité:</strong> Les clés API sont stockées dans le localStorage de votre navigateur. 
                Ne partagez jamais vos clés API et révoquezles si vous soupçonnez qu'elles ont été compromises.
              </Typography>
            </Alert>
          </Stack>
        </TabPanel>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Fermer</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ApiKeyManager;
