{"train": [{"input": [[2, 2, 0, 7, 0, 3, 2, 7, 0, 2, 7, 0, 3, 2, 7, 0, 0, 3], [2, 2, 0, 0, 2, 3, 3, 4, 0, 0, 7, 0, 0, 0, 0, 0, 0, 7], [4, 2, 7, 2, 7, 0, 4, 0, 0, 7, 2, 0, 3, 0, 7, 3, 2, 0], [3, 7, 2, 2, 7, 0, 0, 3, 0, 2, 4, 0, 2, 4, 0, 4, 3, 3], [2, 4, 3, 2, 0, 4, 3, 2, 3, 2, 0, 0, 8, 8, 8, 7, 0, 2], [2, 7, 3, 2, 7, 0, 0, 2, 3, 3, 3, 7, 0, 8, 2, 2, 2, 0], [0, 2, 2, 2, 0, 3, 2, 7, 3, 3, 7, 0, 0, 8, 0, 0, 0, 0], [4, 2, 7, 3, 0, 3, 0, 7, 2, 7, 2, 0, 4, 2, 7, 7, 0, 0], [0, 0, 2, 0, 2, 0, 4, 7, 4, 0, 0, 2, 2, 2, 3, 3, 3, 0], [2, 0, 4, 7, 0, 7, 0, 3, 2, 4, 2, 0, 0, 2, 0, 0, 2, 7], [7, 4, 2, 7, 4, 3, 3, 7, 2, 2, 0, 0, 7, 7, 0, 7, 0, 4]], "output": [[2, 2, 0, 7, 0, 3, 2, 7, 0, 2, 7, 0, 3, 2, 7, 0, 0, 3], [2, 2, 0, 0, 2, 3, 3, 4, 0, 0, 7, 0, 0, 0, 0, 0, 0, 7], [4, 2, 7, 2, 7, 0, 4, 0, 0, 7, 2, 0, 3, 0, 7, 3, 2, 0], [3, 7, 2, 2, 7, 0, 0, 3, 0, 2, 4, 0, 2, 4, 0, 4, 3, 3], [2, 4, 3, 2, 0, 4, 3, 2, 8, 2, 0, 0, 8, 8, 8, 7, 0, 2], [2, 7, 3, 2, 7, 0, 0, 2, 8, 8, 8, 7, 0, 8, 2, 2, 2, 0], [0, 2, 2, 2, 0, 3, 2, 7, 8, 3, 7, 0, 0, 8, 0, 0, 0, 0], [4, 2, 7, 3, 0, 3, 0, 7, 2, 7, 2, 0, 4, 2, 7, 7, 0, 0], [0, 0, 2, 0, 2, 0, 4, 7, 4, 0, 0, 2, 2, 2, 3, 3, 3, 0], [2, 0, 4, 7, 0, 7, 0, 3, 2, 4, 2, 0, 0, 2, 0, 0, 2, 7], [7, 4, 2, 7, 4, 3, 3, 7, 2, 2, 0, 0, 7, 7, 0, 7, 0, 4]]}, {"input": [[2, 7, 7, 0, 0, 3, 3, 2, 2, 0, 0, 2, 3, 3, 7, 0, 0], [0, 3, 7, 2, 2, 4, 2, 7, 4, 2, 7, 2, 2, 7, 0, 7, 2], [2, 3, 0, 3, 7, 3, 0, 2, 7, 2, 0, 2, 2, 3, 2, 3, 2], [2, 4, 7, 3, 0, 0, 4, 2, 4, 2, 4, 0, 7, 0, 3, 3, 0], [7, 3, 2, 4, 3, 2, 0, 0, 7, 2, 0, 3, 2, 2, 3, 0, 2], [2, 7, 3, 7, 2, 2, 2, 0, 2, 2, 7, 4, 2, 2, 3, 0, 3], [0, 3, 0, 0, 2, 3, 0, 2, 2, 0, 7, 7, 3, 2, 0, 0, 0], [2, 0, 0, 4, 0, 2, 2, 2, 0, 4, 4, 0, 7, 0, 0, 3, 2], [3, 2, 7, 0, 7, 8, 0, 8, 0, 4, 2, 2, 2, 2, 0, 0, 0], [7, 2, 3, 4, 3, 2, 8, 8, 2, 0, 4, 0, 3, 7, 0, 3, 2], [7, 7, 2, 2, 0, 7, 7, 4, 2, 3, 2, 7, 2, 2, 7, 2, 3], [0, 0, 0, 4, 2, 3, 0, 4, 7, 7, 3, 0, 7, 2, 0, 3, 0], [0, 0, 7, 2, 3, 0, 2, 3, 0, 3, 4, 2, 3, 3, 3, 2, 3], [7, 2, 2, 2, 2, 3, 0, 3, 0, 3, 3, 0, 7, 0, 3, 3, 2]], "output": [[2, 7, 7, 0, 0, 3, 3, 2, 2, 0, 0, 2, 3, 3, 7, 0, 0], [0, 3, 7, 2, 2, 4, 2, 7, 4, 2, 7, 2, 2, 7, 0, 7, 2], [2, 3, 0, 3, 7, 3, 0, 2, 7, 2, 0, 2, 2, 8, 2, 8, 2], [2, 4, 7, 3, 0, 0, 4, 2, 4, 2, 4, 0, 7, 0, 8, 8, 0], [7, 3, 2, 4, 3, 2, 0, 0, 7, 2, 0, 3, 2, 2, 3, 0, 2], [2, 7, 3, 7, 2, 2, 2, 0, 2, 2, 7, 4, 2, 2, 3, 0, 3], [0, 3, 0, 0, 2, 3, 0, 2, 2, 0, 7, 7, 3, 2, 0, 0, 0], [2, 0, 0, 4, 0, 2, 2, 2, 0, 4, 4, 0, 7, 0, 0, 3, 2], [3, 2, 7, 0, 7, 8, 0, 8, 0, 4, 2, 2, 2, 2, 0, 0, 0], [7, 2, 3, 4, 3, 2, 8, 8, 2, 0, 4, 0, 3, 7, 0, 3, 2], [7, 7, 2, 2, 0, 7, 7, 4, 2, 3, 2, 7, 2, 2, 7, 2, 3], [0, 0, 0, 4, 2, 3, 0, 4, 7, 7, 8, 0, 7, 2, 0, 8, 0], [0, 0, 7, 2, 3, 0, 2, 3, 0, 8, 4, 2, 3, 3, 8, 2, 3], [7, 2, 2, 2, 2, 3, 0, 3, 0, 8, 8, 0, 7, 0, 8, 8, 2]]}, {"input": [[3, 0, 0, 7, 3, 3, 3, 2, 2, 3, 3, 2, 0, 2, 4, 7, 2, 0], [0, 2, 4, 0, 2, 0, 7, 0, 0, 2, 0, 3, 2, 2, 2, 2, 2, 7], [7, 0, 2, 3, 0, 4, 4, 7, 2, 7, 7, 0, 4, 0, 4, 3, 0, 3], [7, 3, 0, 2, 4, 3, 7, 2, 0, 2, 0, 3, 3, 2, 2, 7, 4, 0], [0, 3, 4, 3, 2, 4, 3, 8, 0, 2, 3, 3, 4, 0, 3, 0, 3, 0], [0, 2, 2, 0, 7, 3, 8, 8, 8, 4, 3, 0, 7, 3, 4, 2, 2, 2], [2, 3, 2, 4, 7, 0, 7, 2, 0, 4, 0, 0, 0, 0, 7, 0, 4, 7], [3, 4, 7, 7, 0, 3, 2, 0, 0, 7, 3, 0, 2, 7, 4, 2, 0, 3], [2, 3, 0, 3, 3, 0, 0, 2, 2, 0, 7, 7, 3, 0, 2, 2, 2, 3], [0, 3, 3, 4, 0, 3, 0, 0, 2, 7, 3, 0, 0, 0, 2, 3, 7, 3], [0, 3, 4, 3, 0, 7, 2, 0, 3, 0, 3, 3, 0, 4, 0, 2, 3, 3], [3, 2, 0, 4, 0, 2, 7, 3, 7, 0, 3, 3, 2, 0, 0, 2, 2, 7], [2, 2, 3, 3, 4, 3, 7, 7, 2, 2, 4, 0, 0, 0, 4, 2, 3, 2], [0, 2, 0, 2, 0, 0, 4, 0, 0, 3, 7, 0, 0, 0, 4, 3, 4, 2], [0, 4, 3, 0, 0, 3, 0, 0, 7, 0, 0, 0, 2, 3, 3, 7, 4, 3]], "output": [[3, 0, 0, 7, 3, 3, 3, 2, 2, 3, 3, 2, 0, 2, 4, 7, 2, 0], [0, 2, 4, 0, 2, 0, 7, 0, 0, 2, 0, 3, 2, 2, 2, 2, 2, 7], [7, 0, 2, 3, 0, 4, 4, 7, 2, 7, 7, 0, 4, 0, 4, 3, 0, 3], [7, 3, 0, 2, 4, 3, 7, 2, 0, 2, 0, 3, 3, 2, 2, 7, 4, 0], [0, 3, 4, 3, 2, 4, 3, 8, 0, 2, 3, 3, 4, 0, 3, 0, 3, 0], [0, 2, 2, 0, 7, 3, 8, 8, 8, 4, 3, 0, 7, 3, 4, 2, 2, 2], [2, 3, 2, 4, 7, 0, 7, 2, 0, 4, 0, 0, 0, 0, 7, 0, 4, 7], [3, 4, 7, 7, 0, 3, 2, 0, 0, 7, 3, 0, 2, 7, 4, 2, 0, 3], [2, 8, 0, 3, 3, 0, 0, 2, 2, 0, 7, 7, 3, 0, 2, 2, 2, 3], [0, 8, 8, 4, 0, 3, 0, 0, 2, 7, 8, 0, 0, 0, 2, 3, 7, 3], [0, 8, 4, 3, 0, 7, 2, 0, 3, 0, 8, 8, 0, 4, 0, 2, 3, 3], [3, 2, 0, 4, 0, 2, 7, 3, 7, 0, 8, 3, 2, 0, 0, 2, 2, 7], [2, 2, 3, 3, 4, 3, 7, 7, 2, 2, 4, 0, 0, 0, 4, 2, 3, 2], [0, 2, 0, 2, 0, 0, 4, 0, 0, 3, 7, 0, 0, 0, 4, 3, 4, 2], [0, 4, 3, 0, 0, 3, 0, 0, 7, 0, 0, 0, 2, 3, 3, 7, 4, 3]]}, {"input": [[2, 3, 2, 8, 4, 4, 0, 2, 3, 0, 2, 4, 7, 7, 3, 7, 3], [3, 4, 8, 8, 8, 0, 0, 2, 0, 2, 0, 2, 7, 7, 7, 3, 7], [7, 3, 0, 8, 0, 2, 2, 0, 2, 2, 0, 7, 3, 0, 3, 3, 3], [2, 0, 2, 0, 2, 0, 3, 2, 0, 7, 0, 7, 0, 0, 2, 3, 0], [7, 7, 4, 3, 7, 2, 0, 2, 3, 0, 3, 4, 7, 2, 0, 3, 7], [2, 4, 0, 7, 0, 0, 3, 4, 4, 0, 3, 4, 4, 3, 3, 4, 0], [3, 2, 7, 3, 7, 3, 7, 2, 0, 2, 3, 2, 3, 3, 3, 4, 4], [3, 7, 4, 0, 2, 0, 2, 0, 3, 7, 2, 3, 3, 3, 3, 0, 2], [3, 2, 3, 2, 0, 2, 0, 2, 0, 7, 2, 0, 2, 4, 4, 7, 3], [4, 3, 4, 2, 0, 7, 0, 0, 7, 0, 0, 0, 0, 3, 0, 0, 3], [2, 3, 0, 0, 4, 0, 2, 0, 3, 3, 2, 0, 4, 0, 0, 2, 2], [3, 3, 4, 3, 2, 7, 2, 4, 3, 0, 7, 3, 3, 4, 2, 0, 3], [2, 0, 7, 7, 0, 3, 7, 4, 3, 7, 0, 2, 0, 3, 7, 0, 2], [2, 3, 0, 0, 2, 3, 0, 7, 0, 7, 3, 7, 0, 4, 0, 3, 7], [2, 0, 2, 2, 7, 2, 0, 0, 2, 2, 3, 0, 0, 3, 7, 0, 3], [7, 2, 4, 0, 3, 0, 0, 2, 2, 7, 4, 0, 0, 2, 2, 0, 4], [0, 0, 3, 0, 4, 4, 7, 7, 4, 2, 0, 0, 3, 7, 0, 2, 0], [2, 3, 4, 0, 3, 0, 3, 3, 2, 3, 4, 7, 7, 0, 2, 0, 3]], "output": [[2, 3, 2, 8, 4, 4, 0, 2, 3, 0, 2, 4, 7, 7, 3, 7, 3], [3, 4, 8, 8, 8, 0, 0, 2, 0, 2, 0, 2, 7, 7, 7, 8, 7], [7, 3, 0, 8, 0, 2, 2, 0, 2, 2, 0, 7, 3, 0, 8, 8, 8], [2, 0, 2, 0, 2, 0, 3, 2, 0, 7, 0, 7, 0, 0, 2, 8, 0], [7, 7, 4, 3, 7, 2, 0, 2, 3, 0, 3, 4, 7, 2, 0, 3, 7], [2, 4, 0, 7, 0, 0, 3, 4, 4, 0, 3, 4, 4, 8, 3, 4, 0], [3, 2, 7, 3, 7, 3, 7, 2, 0, 2, 3, 2, 8, 8, 8, 4, 4], [3, 7, 4, 0, 2, 0, 2, 0, 3, 7, 2, 3, 3, 8, 3, 0, 2], [3, 2, 3, 2, 0, 2, 0, 2, 0, 7, 2, 0, 2, 4, 4, 7, 3], [4, 3, 4, 2, 0, 7, 0, 0, 7, 0, 0, 0, 0, 3, 0, 0, 3], [2, 3, 0, 0, 4, 0, 2, 0, 3, 3, 2, 0, 4, 0, 0, 2, 2], [3, 3, 4, 3, 2, 7, 2, 4, 3, 0, 7, 3, 3, 4, 2, 0, 3], [2, 0, 7, 7, 0, 3, 7, 4, 3, 7, 0, 2, 0, 3, 7, 0, 2], [2, 3, 0, 0, 2, 3, 0, 7, 0, 7, 3, 7, 0, 4, 0, 3, 7], [2, 0, 2, 2, 7, 2, 0, 0, 2, 2, 3, 0, 0, 3, 7, 0, 3], [7, 2, 4, 0, 3, 0, 0, 2, 2, 7, 4, 0, 0, 2, 2, 0, 4], [0, 0, 3, 0, 4, 4, 7, 7, 4, 2, 0, 0, 3, 7, 0, 2, 0], [2, 3, 4, 0, 3, 0, 3, 3, 2, 3, 4, 7, 7, 0, 2, 0, 3]]}], "test": [{"input": [[7, 3, 2, 2, 4, 3, 7, 2, 7, 0, 7, 3, 4, 0, 3, 2, 4], [0, 2, 2, 2, 2, 3, 0, 3, 3, 0, 3, 2, 0, 0, 3, 0, 7], [3, 2, 0, 3, 7, 0, 2, 2, 2, 3, 7, 0, 3, 3, 0, 2, 2], [4, 2, 7, 7, 0, 0, 2, 0, 0, 0, 7, 4, 3, 2, 3, 7, 2], [7, 0, 8, 3, 0, 7, 3, 3, 0, 2, 3, 0, 4, 0, 0, 7, 0], [3, 4, 8, 8, 3, 2, 0, 0, 3, 4, 2, 4, 0, 3, 3, 2, 4], [2, 3, 0, 8, 0, 0, 2, 4, 0, 4, 4, 0, 0, 7, 2, 3, 0], [2, 4, 0, 3, 0, 0, 2, 2, 3, 2, 7, 3, 3, 7, 0, 4, 0], [2, 7, 7, 4, 0, 3, 0, 2, 7, 7, 0, 4, 7, 0, 7, 3, 3], [0, 0, 7, 3, 4, 0, 2, 3, 0, 7, 4, 3, 2, 3, 0, 7, 3], [2, 7, 0, 3, 4, 7, 3, 4, 0, 4, 0, 0, 0, 3, 3, 7, 2], [4, 3, 0, 2, 3, 0, 4, 0, 4, 3, 3, 2, 2, 3, 3, 0, 3], [4, 7, 0, 7, 3, 3, 2, 3, 3, 0, 7, 0, 0, 0, 0, 4, 7], [3, 3, 3, 3, 0, 4, 3, 4, 4, 7, 3, 7, 0, 0, 0, 3, 0], [0, 2, 2, 3, 3, 3, 0, 7, 3, 2, 7, 3, 4, 3, 3, 3, 3], [3, 3, 3, 4, 7, 4, 3, 3, 3, 0, 0, 4, 0, 7, 0, 4, 3]], "output": [[7, 3, 2, 2, 4, 3, 7, 2, 7, 0, 7, 3, 4, 0, 3, 2, 4], [0, 2, 2, 2, 2, 3, 0, 3, 3, 0, 3, 2, 0, 0, 3, 0, 7], [3, 2, 0, 3, 7, 0, 2, 2, 2, 3, 7, 0, 3, 3, 0, 2, 2], [4, 2, 7, 7, 0, 0, 2, 0, 0, 0, 7, 4, 3, 2, 3, 7, 2], [7, 0, 8, 3, 0, 7, 3, 3, 0, 2, 3, 0, 4, 0, 0, 7, 0], [3, 4, 8, 8, 3, 2, 0, 0, 3, 4, 2, 4, 0, 3, 3, 2, 4], [2, 3, 0, 8, 0, 0, 2, 4, 0, 4, 4, 0, 0, 7, 2, 3, 0], [2, 4, 0, 3, 0, 0, 2, 2, 3, 2, 7, 3, 3, 7, 0, 4, 0], [2, 7, 7, 4, 0, 3, 0, 2, 7, 7, 0, 4, 7, 0, 7, 3, 3], [0, 0, 7, 3, 4, 0, 2, 3, 0, 7, 4, 3, 2, 8, 0, 7, 3], [2, 7, 0, 3, 4, 7, 3, 4, 0, 4, 0, 0, 0, 8, 8, 7, 2], [4, 3, 0, 2, 3, 0, 4, 0, 4, 3, 3, 2, 2, 3, 8, 0, 3], [4, 7, 0, 7, 3, 3, 2, 3, 3, 0, 7, 0, 0, 0, 0, 4, 7], [3, 3, 8, 8, 0, 4, 3, 4, 4, 7, 3, 7, 0, 0, 0, 8, 0], [0, 2, 2, 8, 8, 3, 0, 7, 3, 2, 7, 3, 4, 3, 3, 8, 8], [3, 3, 3, 4, 7, 4, 3, 3, 3, 0, 0, 4, 0, 7, 0, 4, 8]]}]}