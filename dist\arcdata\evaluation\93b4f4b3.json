{"train": [{"input": [[1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], [1, 1, 0, 0, 1, 1, 0, 0, 2, 2, 0, 0], [1, 0, 0, 0, 0, 1, 0, 0, 2, 2, 0, 0], [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 1, 0, 0, 3, 3, 0, 0], [1, 1, 0, 0, 1, 1, 0, 3, 3, 3, 3, 0], [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], [1, 1, 0, 0, 1, 1, 0, 6, 6, 6, 6, 0], [1, 1, 0, 0, 1, 1, 0, 0, 6, 6, 0, 0], [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0]], "output": [[1, 1, 1, 1, 1, 1], [1, 1, 3, 3, 1, 1], [1, 3, 3, 3, 3, 1], [1, 1, 1, 1, 1, 1], [1, 6, 6, 6, 6, 1], [1, 1, 6, 6, 1, 1], [1, 1, 1, 1, 1, 1], [1, 1, 2, 2, 1, 1], [1, 1, 2, 2, 1, 1], [1, 1, 1, 1, 1, 1]]}, {"input": [[5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [5, 5, 0, 0, 0, 5, 0, 3, 0, 0, 3, 0], [5, 5, 5, 0, 0, 5, 0, 3, 0, 0, 3, 0], [5, 5, 5, 5, 0, 5, 0, 3, 3, 3, 3, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 5, 5, 0, 2, 2, 2, 0, 0], [5, 0, 0, 5, 5, 5, 0, 2, 2, 0, 0, 0], [5, 0, 5, 5, 5, 5, 0, 2, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0], [5, 0, 5, 5, 0, 5, 0, 0, 1, 1, 1, 0], [5, 0, 5, 5, 0, 5, 0, 0, 0, 1, 1, 0], [5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 1, 0], [5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0]], "output": [[5, 5, 5, 5, 5, 5], [5, 5, 1, 1, 1, 5], [5, 5, 5, 1, 1, 5], [5, 5, 5, 5, 1, 5], [5, 5, 5, 5, 5, 5], [5, 2, 2, 2, 5, 5], [5, 2, 2, 5, 5, 5], [5, 2, 5, 5, 5, 5], [5, 5, 5, 5, 5, 5], [5, 3, 5, 5, 3, 5], [5, 3, 5, 5, 3, 5], [5, 3, 3, 3, 3, 5], [5, 5, 5, 5, 5, 5]]}], "test": [{"input": [[8, 8, 8, 8, 8, 0, 0, 0, 0, 0], [8, 0, 0, 0, 8, 0, 2, 2, 2, 0], [8, 8, 0, 8, 8, 0, 0, 0, 2, 0], [8, 0, 0, 0, 8, 0, 0, 0, 2, 0], [8, 8, 8, 8, 8, 0, 0, 0, 0, 0], [8, 0, 0, 0, 8, 0, 4, 0, 4, 0], [8, 8, 8, 0, 8, 0, 4, 0, 4, 0], [8, 8, 8, 0, 8, 0, 4, 4, 4, 0], [8, 8, 8, 8, 8, 0, 0, 0, 0, 0], [8, 0, 8, 0, 8, 0, 3, 3, 3, 0], [8, 0, 8, 0, 8, 0, 0, 3, 0, 0], [8, 0, 0, 0, 8, 0, 3, 3, 3, 0], [8, 8, 8, 8, 8, 0, 0, 0, 0, 0], [8, 8, 0, 0, 8, 0, 0, 7, 7, 0], [8, 0, 0, 0, 8, 0, 7, 7, 7, 0], [8, 0, 0, 8, 8, 0, 7, 7, 0, 0], [8, 8, 8, 8, 8, 0, 0, 0, 0, 0]], "output": [[8, 8, 8, 8, 8], [8, 3, 3, 3, 8], [8, 8, 3, 8, 8], [8, 3, 3, 3, 8], [8, 8, 8, 8, 8], [8, 2, 2, 2, 8], [8, 8, 8, 2, 8], [8, 8, 8, 2, 8], [8, 8, 8, 8, 8], [8, 4, 8, 4, 8], [8, 4, 8, 4, 8], [8, 4, 4, 4, 8], [8, 8, 8, 8, 8], [8, 8, 7, 7, 8], [8, 7, 7, 7, 8], [8, 7, 7, 8, 8], [8, 8, 8, 8, 8]]}]}