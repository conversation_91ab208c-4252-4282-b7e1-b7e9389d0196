{"test": [{"input": [[4, 0, 4, 0, 0, 3, 0, 3, 3, 0], [4, 0, 0, 0, 0, 3, 3, 0, 0, 3], [0, 0, 4, 4, 4, 0, 0, 0, 3, 0], [0, 0, 4, 0, 4, 3, 3, 3, 3, 0], [4, 4, 4, 4, 0, 3, 0, 0, 0, 0], [0, 0, 0, 9, 9, 0, 0, 8, 0, 8], [0, 9, 0, 9, 9, 8, 0, 0, 0, 8], [0, 0, 0, 9, 9, 0, 0, 8, 8, 0], [0, 0, 9, 9, 9, 8, 0, 0, 0, 0], [9, 0, 9, 0, 0, 0, 0, 8, 8, 0]], "output": [[3, 0, 3, 3, 9], [3, 3, 0, 9, 3], [0, 0, 8, 3, 9], [3, 3, 3, 3, 9], [3, 4, 9, 8, 0]]}], "train": [{"input": [[4, 4, 4, 4, 4, 3, 3, 0, 3, 3], [4, 0, 4, 4, 0, 0, 0, 3, 0, 3], [0, 0, 4, 0, 4, 0, 0, 0, 3, 0], [4, 4, 4, 0, 0, 3, 0, 0, 3, 3], [4, 4, 4, 4, 0, 3, 0, 3, 0, 3], [9, 9, 9, 0, 9, 0, 0, 8, 8, 8], [9, 9, 0, 0, 9, 8, 0, 0, 0, 0], [0, 0, 0, 9, 0, 0, 0, 0, 0, 0], [0, 9, 0, 0, 0, 8, 0, 8, 0, 0], [0, 0, 0, 0, 9, 0, 8, 0, 8, 0]], "output": [[3, 3, 9, 3, 3], [9, 9, 3, 4, 3], [0, 0, 4, 3, 4], [3, 9, 8, 3, 3], [3, 8, 3, 8, 3]]}, {"input": [[0, 4, 0, 0, 4, 0, 0, 0, 0, 3], [0, 4, 4, 4, 4, 3, 3, 3, 3, 3], [0, 4, 0, 0, 0, 0, 3, 3, 3, 0], [4, 4, 0, 0, 0, 3, 3, 3, 0, 3], [0, 0, 4, 4, 0, 3, 3, 0, 0, 0], [9, 0, 9, 0, 9, 0, 0, 8, 8, 0], [0, 0, 0, 9, 0, 0, 0, 0, 8, 0], [9, 9, 0, 9, 0, 0, 8, 8, 8, 0], [0, 0, 9, 9, 9, 0, 0, 0, 0, 0], [9, 9, 0, 9, 0, 8, 8, 8, 8, 0]], "output": [[9, 4, 9, 8, 3], [3, 3, 3, 3, 3], [9, 3, 3, 3, 0], [3, 3, 3, 9, 3], [3, 3, 8, 9, 0]]}, {"input": [[4, 0, 0, 0, 0, 3, 0, 0, 0, 3], [0, 0, 4, 4, 4, 3, 3, 3, 3, 3], [4, 4, 0, 4, 0, 3, 3, 3, 3, 3], [4, 4, 4, 0, 0, 3, 0, 0, 0, 0], [0, 0, 4, 0, 4, 3, 3, 0, 0, 0], [0, 0, 0, 0, 9, 0, 8, 0, 8, 8], [9, 0, 9, 0, 9, 8, 0, 8, 0, 0], [0, 0, 9, 0, 0, 8, 0, 8, 8, 0], [9, 9, 9, 9, 0, 8, 0, 0, 0, 8], [0, 9, 9, 0, 0, 8, 8, 8, 8, 8]], "output": [[3, 8, 0, 8, 3], [3, 3, 3, 3, 3], [3, 3, 3, 3, 3], [3, 9, 9, 9, 8], [3, 3, 9, 8, 8]]}, {"input": [[0, 4, 4, 4, 0, 0, 0, 0, 3, 3], [4, 4, 0, 0, 0, 3, 0, 3, 3, 0], [4, 0, 0, 4, 4, 0, 3, 3, 3, 0], [0, 0, 4, 0, 4, 3, 0, 0, 3, 0], [0, 0, 4, 4, 4, 3, 3, 3, 3, 3], [0, 9, 0, 9, 9, 0, 0, 0, 8, 0], [9, 0, 0, 9, 9, 0, 8, 8, 0, 8], [0, 0, 0, 9, 0, 0, 0, 8, 8, 0], [0, 0, 9, 9, 0, 8, 0, 8, 0, 0], [9, 9, 0, 9, 0, 0, 8, 0, 8, 8]], "output": [[0, 9, 4, 3, 3], [3, 8, 3, 3, 9], [4, 3, 3, 3, 4], [3, 0, 9, 3, 4], [3, 3, 3, 3, 3]]}, {"input": [[0, 4, 4, 4, 0, 0, 3, 0, 3, 0], [0, 4, 0, 0, 0, 0, 3, 0, 0, 3], [0, 0, 0, 0, 0, 0, 0, 3, 3, 3], [0, 0, 4, 4, 0, 3, 0, 3, 3, 3], [0, 4, 4, 4, 4, 3, 3, 3, 3, 3], [9, 0, 9, 9, 0, 0, 0, 0, 0, 0], [9, 0, 0, 0, 9, 0, 8, 0, 8, 0], [0, 0, 9, 0, 0, 0, 0, 0, 0, 8], [0, 0, 0, 9, 0, 8, 8, 0, 0, 0], [0, 0, 0, 0, 9, 0, 0, 8, 8, 8]], "output": [[9, 3, 9, 3, 0], [9, 3, 0, 8, 3], [0, 0, 3, 3, 3], [3, 8, 3, 3, 3], [3, 3, 3, 3, 3]]}, {"input": [[4, 0, 0, 0, 4, 0, 0, 3, 3, 0], [4, 0, 0, 0, 0, 3, 3, 3, 3, 0], [0, 4, 4, 0, 4, 3, 0, 0, 3, 3], [0, 4, 4, 0, 4, 0, 0, 3, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0, 3], [0, 9, 9, 9, 9, 0, 8, 0, 0, 8], [0, 0, 9, 9, 9, 8, 0, 0, 0, 8], [9, 9, 9, 0, 0, 8, 8, 0, 8, 0], [9, 9, 9, 0, 9, 0, 8, 8, 8, 8], [0, 9, 9, 0, 9, 0, 8, 0, 0, 8]], "output": [[4, 9, 3, 3, 9], [3, 3, 3, 3, 9], [3, 9, 9, 3, 3], [9, 9, 3, 8, 9], [0, 9, 9, 0, 3]]}]}