{"train": [{"input": [[1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1], [1, 0, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0], [1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1], [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0], [1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 1], [1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1]], "output": [[1, 0, 0, 0, 1, 1, 1, 1, 2, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1], [1, 0, 1, 0, 1, 1, 1, 1, 2, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1], [1, 1, 1, 1, 0, 0, 1, 1, 2, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0], [1, 0, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 0, 1, 1, 0, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 1, 0, 1, 1, 0, 2, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1], [1, 0, 0, 1, 1, 0, 1, 0, 2, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0], [1, 1, 0, 0, 1, 1, 1, 1, 2, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [1, 1, 1, 0, 0, 1, 1, 1, 2, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 0, 1, 1, 0, 0, 2, 1, 1, 0, 0, 0, 1, 0, 1, 0, 1], [1, 0, 1, 0, 1, 0, 0, 1, 2, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1]]}, {"input": [[8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8], [0, 8, 0, 0, 0, 0, 8, 8, 8, 8, 0, 8, 8, 8], [8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 0, 0, 8, 8], [8, 0, 8, 8, 0, 8, 8, 0, 0, 8, 0, 8, 8, 0], [8, 8, 8, 8, 0, 8, 8, 0, 0, 0, 0, 8, 8, 8], [8, 8, 8, 0, 0, 8, 8, 0, 8, 0, 0, 8, 8, 8], [8, 0, 8, 8, 0, 8, 8, 8, 8, 8, 0, 0, 0, 8], [8, 8, 0, 0, 0, 8, 0, 0, 8, 8, 0, 0, 8, 8], [8, 0, 0, 8, 0, 8, 8, 8, 0, 8, 0, 8, 8, 8], [8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 0, 0, 8, 0], [0, 8, 0, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0, 8], [8, 8, 8, 8, 0, 8, 8, 8, 8, 8, 0, 0, 8, 0]], "output": [[8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 2, 0, 8, 8], [0, 8, 0, 0, 2, 0, 8, 8, 8, 8, 2, 8, 8, 8], [8, 8, 0, 8, 2, 8, 8, 8, 8, 8, 2, 0, 8, 8], [8, 0, 8, 8, 2, 8, 8, 0, 0, 8, 2, 8, 8, 0], [8, 8, 8, 8, 2, 8, 8, 0, 0, 0, 2, 8, 8, 8], [8, 8, 8, 0, 2, 8, 8, 0, 8, 0, 2, 8, 8, 8], [8, 0, 8, 8, 2, 8, 8, 8, 8, 8, 2, 0, 0, 8], [8, 8, 0, 0, 2, 8, 0, 0, 8, 8, 2, 0, 8, 8], [8, 0, 0, 8, 2, 8, 8, 8, 0, 8, 2, 8, 8, 8], [8, 8, 0, 8, 2, 8, 8, 8, 8, 8, 2, 0, 8, 0], [0, 8, 0, 8, 2, 0, 0, 0, 0, 0, 2, 8, 0, 8], [8, 8, 8, 8, 2, 8, 8, 8, 8, 8, 2, 0, 8, 0]]}, {"input": [[3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 3, 0, 3], [3, 0, 3, 0, 3, 3, 3, 0, 3, 0, 3, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 3, 0, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0], [3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3], [3, 0, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 0, 3, 3], [0, 0, 3, 0, 3, 0, 3, 0, 3, 0, 0, 3, 3, 3, 0], [3, 0, 0, 3, 3, 3, 0, 0, 3, 0, 3, 3, 0, 0, 3], [3, 0, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3], [3, 0, 0, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0], [3, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 3, 3], [3, 0, 3, 3, 3, 0, 3, 0, 0, 3, 0, 3, 3, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 0, 3, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 3, 0], [3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 3], [3, 0, 0, 3, 3, 3, 3, 3, 0, 3, 3, 0, 0, 3, 3], [0, 0, 3, 3, 0, 3, 3, 0, 0, 3, 0, 3, 0, 3, 0]], "output": [[3, 2, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 3, 0, 3], [3, 2, 3, 0, 3, 3, 3, 0, 3, 0, 3, 0, 0, 3, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [3, 2, 0, 3, 0, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0], [3, 2, 3, 3, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3], [3, 2, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 0, 3, 3], [0, 2, 3, 0, 3, 0, 3, 0, 3, 0, 0, 3, 3, 3, 0], [3, 2, 0, 3, 3, 3, 0, 0, 3, 0, 3, 3, 0, 0, 3], [3, 2, 3, 3, 3, 3, 3, 0, 3, 3, 3, 3, 3, 0, 3], [3, 2, 0, 3, 3, 0, 3, 3, 3, 3, 3, 3, 3, 3, 0], [3, 2, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 3, 3], [3, 2, 3, 3, 3, 0, 3, 0, 0, 3, 0, 3, 3, 3, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [3, 2, 3, 0, 0, 3, 0, 3, 3, 0, 3, 3, 3, 3, 0], [3, 2, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 3], [3, 2, 0, 3, 3, 3, 3, 3, 0, 3, 3, 0, 0, 3, 3], [0, 2, 3, 3, 0, 3, 3, 0, 0, 3, 0, 3, 0, 3, 0]]}], "test": [{"input": [[4, 0, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 0, 4, 0, 0], [4, 4, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 0, 4, 4, 4, 0, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 4, 0], [4, 4, 0, 4, 4, 4, 0, 0, 0, 0, 4, 4, 4, 4, 0, 4, 4, 4, 0, 4, 4, 0, 4, 4, 4], [4, 4, 4, 0, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 0, 4], [4, 0, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 4, 0, 4, 0, 4, 4, 4], [4, 4, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 4, 4, 0, 0, 4, 4, 4, 0, 0, 0, 0, 4, 0], [0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 0, 4, 0, 4, 0, 0, 4, 0, 4], [4, 4, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 0, 4, 4, 4], [4, 0, 4, 4, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 0, 0, 0, 4], [4, 4, 0, 4, 0, 0, 0, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 0, 4, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 4, 0, 0, 0, 0, 0, 4, 4, 4, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 0, 0, 4, 4], [4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 4, 4, 0, 4, 4, 4], [4, 4, 4, 4, 4, 0, 0, 4, 0, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 4, 0, 4], [0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 4, 0, 4, 4, 0], [0, 4, 4, 4, 4, 0, 0, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 0, 0, 4, 4], [4, 4, 4, 0, 4, 4, 0, 0, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0], [4, 4, 0, 4, 4, 4, 0, 4, 4, 0, 4, 4, 4, 0, 4, 4, 4, 0, 4, 4, 0, 0, 0, 4, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 4, 0, 0, 0, 0, 4, 0, 4, 4, 4, 0, 4, 4, 4], [0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 4], [4, 4, 4, 4, 4, 4, 0, 4, 4, 0, 0, 0, 0, 4, 4, 4, 0, 0, 4, 4, 4, 0, 4, 4, 0], [4, 0, 4, 0, 4, 4, 0, 4, 0, 0, 0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 0, 4, 0], [4, 4, 0, 4, 0, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0, 0, 0, 4, 0, 4, 0, 4, 4, 4], [4, 0, 0, 4, 4, 4, 0, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 0, 0, 4, 4, 4]], "output": [[4, 0, 4, 0, 4, 4, 2, 0, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 2, 4, 0, 0], [4, 4, 4, 0, 0, 4, 2, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 2, 4, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [4, 0, 4, 4, 4, 0, 2, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 2, 4, 4, 0], [4, 4, 0, 4, 4, 4, 2, 0, 0, 0, 4, 4, 4, 4, 0, 4, 4, 4, 0, 4, 4, 2, 4, 4, 4], [4, 4, 4, 0, 4, 4, 2, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 2, 4, 0, 4], [4, 0, 0, 4, 0, 4, 2, 4, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 4, 0, 4, 2, 4, 4, 4], [4, 4, 4, 4, 4, 0, 2, 4, 0, 4, 0, 0, 4, 4, 0, 0, 4, 4, 4, 0, 0, 2, 0, 4, 0], [0, 4, 4, 0, 4, 4, 2, 4, 4, 0, 4, 4, 0, 4, 4, 0, 0, 4, 0, 4, 0, 2, 4, 0, 4], [4, 4, 4, 0, 4, 4, 2, 0, 4, 4, 4, 4, 4, 0, 0, 4, 0, 4, 4, 4, 0, 2, 4, 4, 4], [4, 0, 4, 4, 4, 0, 2, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 2, 0, 0, 4], [4, 4, 0, 4, 0, 0, 2, 4, 4, 4, 0, 4, 4, 4, 4, 4, 4, 0, 4, 4, 4, 2, 4, 4, 4], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 4, 4, 0, 0, 0, 2, 0, 4, 4, 4, 4, 0, 4, 4, 0, 0, 4, 4, 4, 4, 2, 0, 4, 4], [4, 4, 4, 4, 4, 4, 2, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 4, 4, 2, 4, 4, 4], [4, 4, 4, 4, 4, 0, 2, 4, 0, 4, 0, 0, 4, 4, 4, 4, 4, 4, 4, 4, 0, 2, 4, 0, 4], [0, 4, 4, 4, 4, 4, 2, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 4, 2, 4, 4, 0], [0, 4, 4, 4, 4, 0, 2, 4, 4, 4, 0, 4, 0, 4, 0, 4, 4, 4, 4, 4, 4, 2, 0, 4, 4], [4, 4, 4, 0, 4, 4, 2, 0, 4, 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 2, 0, 0, 0], [4, 4, 0, 4, 4, 4, 2, 4, 4, 0, 4, 4, 4, 0, 4, 4, 4, 0, 4, 4, 0, 2, 0, 4, 4], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [4, 4, 4, 4, 0, 4, 2, 4, 0, 4, 4, 4, 0, 0, 0, 0, 4, 0, 4, 4, 4, 2, 4, 4, 4], [0, 4, 4, 4, 4, 4, 2, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 2, 4, 4, 4], [4, 4, 4, 4, 4, 4, 2, 4, 4, 0, 0, 0, 0, 4, 4, 4, 0, 0, 4, 4, 4, 2, 4, 4, 0], [4, 0, 4, 0, 4, 4, 2, 4, 0, 0, 0, 4, 4, 4, 4, 4, 0, 4, 0, 4, 4, 2, 0, 4, 0], [4, 4, 0, 4, 0, 4, 2, 0, 4, 0, 4, 4, 0, 4, 4, 0, 0, 0, 4, 0, 4, 2, 4, 4, 4], [4, 0, 0, 4, 4, 4, 2, 4, 0, 4, 4, 4, 4, 4, 0, 4, 4, 4, 4, 0, 0, 2, 4, 4, 4]]}]}