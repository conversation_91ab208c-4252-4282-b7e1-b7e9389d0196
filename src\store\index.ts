import { configureStore } from '@reduxjs/toolkit';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import puzzleSlice from './slices/puzzleSlice';
import chatSlice from './slices/chatSlice';
import demoSlice from './slices/demoSlice';
import settingsSlice from './slices/settingsSlice';

// API slice for data fetching
export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Puzzle', 'Analysis', 'Session'],
  endpoints: (builder) => ({
    loadARCPuzzles: builder.query({
      queryFn: async (subset: 'training' | 'evaluation' | 'both') => {
        try {
          const { arcDataLoader } = await import('../services/arcDataLoader');
          const puzzles = await arcDataLoader.loadAllPuzzles(subset);
          return { data: puzzles };
        } catch (error) {
          return { error: { status: 'CUSTOM_ERROR', error: (error as Error).message } };
        }
      },
      providesTags: ['Puzzle'],
    }),
    loadARCPuzzleById: builder.query({
      queryFn: async ({ id, subset }: { id: string; subset: 'training' | 'evaluation' }) => {
        try {
          const { arcDataLoader } = await import('../services/arcDataLoader');
          const puzzle = await arcDataLoader.loadPuzzle(id, subset);
          return { data: puzzle };
        } catch (error) {
          return { error: { status: 'CUSTOM_ERROR', error: (error as Error).message } };
        }
      },
      providesTags: ['Puzzle'],
    }),
    getPuzzles: builder.query({
      query: (subset: 'training' | 'evaluation' | 'both') => `puzzles?subset=${subset}`,
      providesTags: ['Puzzle'],
    }),
    getPuzzleById: builder.query({
      query: (id: string) => `puzzles/${id}`,
      providesTags: ['Puzzle'],
    }),
    analyzePuzzle: builder.mutation({
      queryFn: async (puzzle) => {
        try {
          const { arcAnalyzer } = await import('../services/arcAnalyzer');
          const { dataStorage } = await import('../services/dataStorage');
          
          const analysis = await arcAnalyzer.analyzePuzzle(puzzle);
          
          // Sauvegarder l'analyse dans le cache
          dataStorage.saveAnalysis(puzzle.id, analysis);
          
          return { data: analysis };
        } catch (error) {
          console.error('Analysis error:', error);
          return { error: { status: 'CUSTOM_ERROR', error: (error as Error).message } };
        }
      },
      invalidatesTags: ['Analysis'],
    }),
    sendToAI: builder.mutation({
      query: ({ provider, model, prompt, apiKey }) => ({
        url: 'ai/chat',
        method: 'POST',
        body: { provider, model, prompt, apiKey },
      }),
    }),
    validateSolution: builder.mutation({
      query: ({ solution, expected }) => ({
        url: 'validate',
        method: 'POST',
        body: { solution, expected },
      }),
    }),
  }),
});

export const {
  useLoadARCPuzzlesQuery,
  useLoadARCPuzzleByIdQuery,
  useGetPuzzlesQuery,
  useGetPuzzleByIdQuery,
  useAnalyzePuzzleMutation,
  useSendToAIMutation,
  useValidateSolutionMutation,
} = api;

export const store = configureStore({
  reducer: {
    puzzle: puzzleSlice,
    chat: chatSlice,
    demo: demoSlice,
    settings: settingsSlice,
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // Seuil adapté pour les gros datasets (401 training + 400 evaluation)
        warnAfter: 128,
      },
      // Optimiser les vérifications d'immutabilité pour les gros états
      immutableCheck: {
        warnAfter: 128,
      },
    }).concat(api.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;