{"train": [{"input": [[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 4, 4], [4, 4, 4, 1, 1, 4, 1, 1, 4, 4, 4, 4, 3, 4, 3, 4, 4], [4, 4, 4, 1, 4, 4, 4, 1, 4, 4, 4, 4, 3, 3, 3, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 1, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 1, 1, 4, 1, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 6, 6, 4, 4, 4, 6, 6], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 4, 4, 6], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 6, 4, 4, 4, 4, 4, 6]], "output": [[6, 6, 4, 4, 4, 6, 6], [6, 1, 1, 4, 1, 1, 6], [4, 1, 3, 3, 3, 1, 4], [4, 4, 3, 4, 3, 4, 4], [4, 1, 3, 3, 3, 1, 4], [6, 1, 1, 4, 1, 1, 6], [6, 6, 4, 4, 4, 6, 6]]}, {"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 1, 1, 8, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 0, 8, 8, 8, 8], [8, 8, 1, 8, 8, 8, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 1, 1, 8, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 4, 4, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 4, 8, 4, 8, 8, 8, 2, 2, 8, 2, 2, 8, 8, 8, 8, 8, 8], [8, 4, 4, 4, 8, 8, 8, 2, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 2, 8, 8, 8, 2, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 2, 2, 8, 2, 2, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[1, 1, 1, 8, 1, 1, 1], [1, 2, 2, 8, 2, 2, 1], [1, 2, 4, 4, 4, 2, 1], [8, 8, 4, 0, 4, 8, 8], [1, 2, 4, 4, 4, 2, 1], [1, 2, 2, 8, 2, 2, 1], [1, 1, 1, 8, 1, 1, 1]]}, {"input": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 2, 3, 3, 1, 1, 1, 3, 3, 3, 1, 1, 1, 3, 8, 8, 3], [3, 3, 2, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 8, 3, 3], [3, 3, 3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8, 3, 3], [3, 3, 2, 3, 3, 3, 3, 7, 7, 7, 3, 3, 3, 3, 3, 8, 8, 3], [2, 2, 2, 3, 3, 3, 3, 7, 3, 7, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 1, 3, 7, 7, 7, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 1, 3, 3, 3, 3, 3, 3, 3, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 1, 1, 1, 3, 3, 3, 1, 1, 1, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 4, 3, 3, 3, 3], [3, 3, 3, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 4], [4, 1, 1, 1, 3, 3, 3, 1, 1, 1, 4], [3, 1, 2, 2, 2, 3, 2, 2, 2, 1, 3], [3, 1, 2, 8, 8, 3, 8, 8, 2, 1, 3], [3, 3, 2, 8, 7, 7, 7, 8, 2, 3, 3], [3, 3, 3, 3, 7, 6, 7, 3, 3, 3, 3], [3, 3, 2, 8, 7, 7, 7, 8, 2, 3, 3], [3, 1, 2, 8, 8, 3, 8, 8, 2, 1, 3], [3, 1, 2, 2, 2, 3, 2, 2, 2, 1, 3], [4, 1, 1, 1, 3, 3, 3, 1, 1, 1, 4], [4, 4, 3, 3, 3, 3, 3, 3, 3, 4, 4]]}], "test": [{"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 1, 1, 1, 4, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 8, 1, 8, 1, 1, 1, 4, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 8, 8, 8, 1, 1, 1, 4, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 4, 4, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 3, 1, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 3, 3, 1, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 1], [1, 1, 1, 1, 2, 2, 2, 1, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 1, 1, 1, 1]], "output": [[4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4], [4, 2, 2, 2, 1, 1, 1, 2, 2, 2, 4], [4, 2, 6, 6, 1, 1, 1, 6, 6, 2, 4], [4, 2, 6, 3, 3, 1, 3, 3, 6, 2, 4], [4, 1, 1, 3, 8, 8, 8, 3, 1, 1, 4], [1, 1, 1, 1, 8, 1, 8, 1, 1, 1, 1], [4, 1, 1, 3, 8, 8, 8, 3, 1, 1, 4], [4, 2, 6, 3, 3, 1, 3, 3, 6, 2, 4], [4, 2, 6, 6, 1, 1, 1, 6, 6, 2, 4], [4, 2, 2, 2, 1, 1, 1, 2, 2, 2, 4], [4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4]]}]}