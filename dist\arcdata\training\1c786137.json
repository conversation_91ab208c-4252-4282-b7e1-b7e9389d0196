{"train": [{"input": [[3, 8, 8, 0, 3, 8, 8, 0, 8, 0, 3, 1, 1, 1, 8, 8, 0, 3, 8, 3, 8], [3, 3, 0, 0, 5, 3, 0, 3, 8, 0, 3, 3, 8, 1, 1, 8, 1, 3, 1, 8, 3], [1, 5, 1, 3, 1, 1, 8, 3, 0, 0, 3, 8, 3, 0, 1, 0, 8, 8, 5, 5, 0], [5, 3, 0, 8, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 0, 3, 0, 0, 3], [0, 1, 3, 3, 2, 0, 0, 8, 0, 3, 3, 3, 3, 2, 0, 0, 8, 0, 3, 3, 1], [8, 0, 0, 8, 2, 1, 0, 0, 0, 3, 0, 3, 1, 2, 0, 0, 0, 8, 0, 1, 0], [1, 1, 5, 0, 2, 3, 3, 0, 3, 3, 0, 8, 1, 2, 1, 0, 8, 3, 1, 0, 0], [0, 0, 8, 8, 2, 3, 3, 5, 1, 0, 3, 0, 0, 2, 1, 0, 5, 0, 3, 0, 1], [0, 1, 0, 0, 2, 5, 1, 3, 0, 1, 3, 1, 1, 2, 8, 8, 0, 5, 0, 3, 8], [8, 3, 3, 3, 2, 5, 0, 8, 0, 3, 0, 8, 8, 2, 3, 3, 0, 0, 3, 3, 8], [1, 1, 1, 5, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 8, 1, 3, 0, 0], [3, 3, 3, 0, 8, 8, 0, 8, 3, 0, 8, 8, 3, 0, 3, 0, 8, 1, 0, 1, 0], [8, 0, 0, 3, 3, 0, 8, 3, 0, 3, 3, 0, 1, 3, 3, 1, 8, 0, 0, 3, 8], [5, 1, 5, 1, 8, 3, 5, 0, 8, 3, 3, 8, 1, 8, 0, 0, 0, 3, 0, 0, 5], [1, 3, 1, 0, 1, 3, 1, 0, 5, 0, 3, 3, 8, 0, 8, 3, 8, 8, 8, 0, 0], [5, 3, 3, 3, 3, 8, 8, 0, 1, 1, 0, 8, 5, 1, 3, 0, 0, 8, 3, 1, 0], [3, 1, 3, 3, 8, 0, 3, 8, 0, 3, 1, 8, 3, 1, 8, 1, 1, 3, 8, 1, 0], [0, 3, 8, 3, 3, 0, 1, 3, 0, 3, 8, 5, 3, 0, 3, 1, 0, 3, 0, 0, 8], [3, 8, 3, 0, 1, 3, 8, 0, 1, 3, 8, 1, 0, 1, 1, 8, 5, 8, 3, 1, 1], [1, 5, 1, 3, 3, 1, 5, 3, 3, 1, 1, 3, 5, 0, 8, 8, 1, 1, 8, 0, 8], [1, 3, 0, 1, 3, 3, 1, 0, 0, 1, 5, 8, 3, 5, 3, 8, 0, 3, 8, 3, 8], [3, 1, 3, 0, 8, 0, 8, 0, 0, 1, 3, 1, 1, 0, 8, 8, 5, 1, 0, 1, 8], [3, 3, 1, 0, 3, 1, 8, 8, 0, 0, 5, 1, 8, 8, 1, 3, 3, 5, 3, 5, 8]], "output": [[0, 0, 8, 0, 3, 3, 3, 3], [1, 0, 0, 0, 3, 0, 3, 1], [3, 3, 0, 3, 3, 0, 8, 1], [3, 3, 5, 1, 0, 3, 0, 0], [5, 1, 3, 0, 1, 3, 1, 1], [5, 0, 8, 0, 3, 0, 8, 8]]}, {"input": [[0, 6, 9, 6, 6, 0, 6, 3, 6, 9, 6, 6, 6, 9, 9, 0], [9, 9, 0, 6, 6, 0, 0, 9, 3, 6, 6, 6, 9, 9, 0, 6], [6, 0, 9, 0, 0, 6, 0, 6, 6, 0, 3, 0, 0, 6, 0, 0], [9, 6, 6, 9, 9, 9, 6, 3, 6, 9, 9, 6, 6, 3, 6, 6], [6, 6, 0, 0, 6, 6, 9, 0, 0, 3, 0, 0, 0, 0, 0, 9], [9, 9, 6, 0, 0, 9, 0, 0, 3, 9, 3, 0, 0, 0, 9, 0], [3, 6, 4, 4, 4, 4, 4, 6, 0, 0, 0, 9, 0, 0, 0, 9], [9, 0, 4, 3, 3, 0, 4, 0, 0, 6, 0, 0, 9, 6, 9, 3], [9, 0, 4, 9, 3, 9, 4, 9, 0, 0, 3, 9, 0, 0, 9, 3], [6, 9, 4, 6, 6, 0, 4, 3, 9, 6, 0, 6, 0, 9, 3, 0], [3, 3, 4, 9, 0, 0, 4, 9, 0, 6, 0, 0, 0, 6, 0, 0], [0, 0, 4, 6, 3, 9, 4, 6, 0, 9, 0, 9, 0, 0, 0, 0], [9, 9, 4, 4, 4, 4, 4, 9, 9, 0, 9, 9, 0, 0, 0, 6]], "output": [[3, 3, 0], [9, 3, 9], [6, 6, 0], [9, 0, 0], [6, 3, 9]]}, {"input": [[2, 5, 0, 0, 3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 3, 5, 3, 5], [2, 0, 0, 2, 0, 2, 2, 2, 2, 2, 2, 5, 3, 0, 3, 2, 0, 5], [0, 5, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 5, 0, 0], [2, 0, 2, 8, 0, 0, 5, 3, 3, 3, 2, 2, 5, 0, 8, 2, 5, 5], [5, 0, 3, 8, 3, 0, 0, 5, 5, 5, 5, 2, 0, 5, 8, 3, 3, 3], [0, 5, 5, 8, 3, 5, 0, 2, 0, 3, 0, 5, 3, 0, 8, 0, 2, 5], [5, 2, 2, 8, 3, 2, 5, 5, 0, 5, 3, 0, 5, 0, 8, 0, 0, 0], [0, 0, 0, 8, 5, 2, 5, 2, 5, 0, 2, 2, 2, 2, 8, 2, 0, 5], [5, 0, 5, 8, 0, 5, 2, 5, 0, 0, 0, 0, 3, 3, 8, 0, 0, 5], [3, 0, 0, 8, 2, 3, 2, 3, 0, 0, 5, 0, 5, 0, 8, 3, 2, 0], [3, 5, 0, 8, 3, 2, 5, 0, 5, 0, 0, 0, 5, 5, 8, 0, 0, 2], [3, 3, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 2, 0], [5, 0, 0, 3, 0, 3, 3, 5, 2, 5, 0, 0, 0, 0, 0, 5, 0, 0], [2, 5, 2, 5, 2, 2, 0, 0, 0, 5, 2, 0, 2, 0, 3, 0, 3, 0], [0, 2, 2, 2, 2, 0, 0, 2, 0, 2, 3, 3, 2, 0, 2, 5, 2, 5], [3, 0, 0, 0, 0, 5, 3, 0, 0, 0, 2, 2, 5, 0, 2, 3, 2, 0], [0, 0, 2, 5, 0, 5, 0, 3, 0, 0, 0, 0, 2, 3, 3, 5, 2, 3]], "output": [[0, 0, 5, 3, 3, 3, 2, 2, 5, 0], [3, 0, 0, 5, 5, 5, 5, 2, 0, 5], [3, 5, 0, 2, 0, 3, 0, 5, 3, 0], [3, 2, 5, 5, 0, 5, 3, 0, 5, 0], [5, 2, 5, 2, 5, 0, 2, 2, 2, 2], [0, 5, 2, 5, 0, 0, 0, 0, 3, 3], [2, 3, 2, 3, 0, 0, 5, 0, 5, 0], [3, 2, 5, 0, 5, 0, 0, 0, 5, 5]]}], "test": [{"input": [[0, 0, 0, 8, 1, 1, 8, 0, 0, 8, 0, 8, 0, 0, 0, 8], [0, 1, 0, 8, 8, 1, 0, 1, 1, 2, 8, 1, 1, 2, 0, 2], [0, 0, 8, 8, 1, 1, 8, 8, 1, 1, 8, 0, 8, 0, 0, 1], [1, 0, 1, 0, 8, 0, 1, 8, 1, 0, 1, 1, 8, 8, 8, 0], [8, 0, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 2], [1, 0, 8, 3, 2, 0, 8, 1, 1, 1, 0, 1, 0, 3, 0, 0], [0, 8, 8, 3, 8, 1, 0, 8, 2, 8, 1, 2, 8, 3, 1, 8], [1, 0, 8, 3, 8, 2, 0, 2, 0, 1, 1, 8, 1, 3, 8, 8], [0, 8, 0, 3, 0, 1, 8, 8, 1, 1, 8, 1, 8, 3, 2, 1], [1, 0, 0, 3, 0, 1, 8, 8, 0, 8, 0, 2, 0, 3, 8, 1], [0, 8, 8, 3, 0, 8, 8, 2, 8, 8, 8, 8, 8, 3, 8, 8], [1, 1, 1, 3, 8, 0, 2, 0, 0, 0, 0, 8, 8, 3, 8, 0], [1, 8, 0, 3, 0, 2, 8, 8, 1, 2, 0, 0, 2, 3, 8, 1], [8, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 2], [8, 1, 0, 0, 0, 0, 8, 8, 0, 1, 2, 8, 8, 8, 1, 8], [8, 1, 0, 0, 1, 1, 8, 0, 1, 2, 8, 1, 0, 1, 2, 0], [8, 0, 8, 2, 8, 0, 8, 2, 0, 1, 8, 1, 8, 1, 8, 8]], "output": [[2, 0, 8, 1, 1, 1, 0, 1, 0], [8, 1, 0, 8, 2, 8, 1, 2, 8], [8, 2, 0, 2, 0, 1, 1, 8, 1], [0, 1, 8, 8, 1, 1, 8, 1, 8], [0, 1, 8, 8, 0, 8, 0, 2, 0], [0, 8, 8, 2, 8, 8, 8, 8, 8], [8, 0, 2, 0, 0, 0, 0, 8, 8], [0, 2, 8, 8, 1, 2, 0, 0, 2]]}]}