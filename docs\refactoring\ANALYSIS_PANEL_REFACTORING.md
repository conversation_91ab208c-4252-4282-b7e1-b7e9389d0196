# 🔧 Refactoring AnalysisPanel - Séparation par Niveaux

## 📋 Vue d'Ensemble

Le fichier `AnalysisPanel.tsx` fait actuellement **2600+ lignes** et contient toute la logique d'affichage des 4 niveaux d'analyse. Ce document propose un refactoring pour diviser ce composant monolithique en composants séparés par niveau.

## 🎯 Objectifs du Refactoring

1. **Maintenabilité** : Fichiers plus petits et focalisés
2. **Réutilisabilité** : Composants indépendants par niveau
3. **Performance** : Lazy loading possible par niveau
4. **Lisibilité** : Code plus organisé et compréhensible
5. **Testabilité** : Tests unitaires par composant

## 📁 Structure Proposée

```
src/components/AnalysisPanel/
├── AnalysisPanel.tsx                 # Composant principal (orchestrateur)
├── components/
│   ├── Level0Panel.tsx              # Niveau 0 : Donn<PERSON> Brutes
│   ├── Level1Panel.tsx              # Niveau 1 : Analyses Dérivées
│   ├── Level2Panel.tsx              # Niveau 2 : Comparaisons
│   ├── Level3Panel.tsx              # Niveau 3 : Synthèse
│   └── shared/
│       ├── TabPanel.tsx             # Composant TabPanel réutilisable
│       ├── LevelHeader.tsx          # En-tête de niveau standardisé
│       ├── AnalysisAccordion.tsx    # Accordéon d'analyse réutilisable
│       └── StatusChips.tsx          # Chips de statut réutilisables
├── hooks/
│   ├── useAnalysisState.ts          # Hook pour l'état d'analyse
│   └── useExpandedSections.ts       # Hook pour les sections expandées
└── types/
    └── AnalysisPanelTypes.ts        # Types spécifiques au panel
```

## 🔄 Plan de Refactoring

### Phase 1 : Extraction des Composants Partagés

#### 1.1 TabPanel Générique

**Fichier** : `src/components/AnalysisPanel/components/shared/TabPanel.tsx`

```typescript
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  className?: string;
}

export const TabPanel: React.FC<TabPanelProps> = ({ 
  children, 
  value, 
  index, 
  className,
  ...other 
}) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analysis-tabpanel-${index}`}
      aria-labelledby={`analysis-tab-${index}`}
      className={className}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2, pb: 4 }}>
          {children}
        </Box>
      )}
    </div>
  );
};
```

#### 1.2 En-tête de Niveau Standardisé

**Fichier** : `src/components/AnalysisPanel/components/shared/LevelHeader.tsx`

```typescript
interface LevelHeaderProps {
  level: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  hasData: boolean;
  lastAnalysisTime?: string;
}

export const LevelHeader: React.FC<LevelHeaderProps> = ({
  level,
  title,
  description,
  icon,
  hasData,
  lastAnalysisTime
}) => {
  return (
    <Stack spacing={2}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        {icon}
        <Box sx={{ ml: 1 }}>
          {title}
        </Box>
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          {description}
        </Typography>
      </Alert>

      {!hasData && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Aucune donnée du Niveau {level} disponible. Lancez une analyse pour voir les résultats.
          </Typography>
        </Alert>
      )}

      {lastAnalysisTime && (
        <Typography variant="caption" color="text.secondary">
          Dernière analyse: {lastAnalysisTime}
        </Typography>
      )}
    </Stack>
  );
};
```

#### 1.3 Accordéon d'Analyse Réutilisable

**Fichier** : `src/components/AnalysisPanel/components/shared/AnalysisAccordion.tsx`

```typescript
interface AnalysisAccordionProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  expanded: boolean;
  onChange: (event: React.SyntheticEvent, isExpanded: boolean) => void;
  children: React.ReactNode;
}

export const AnalysisAccordion: React.FC<AnalysisAccordionProps> = ({
  id,
  title,
  icon,
  expanded,
  onChange,
  children
}) => {
  return (
    <Accordion expanded={expanded} onChange={onChange}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle1">
          {icon}
          <Box component="span" sx={{ ml: 1 }}>
            {title}
          </Box>
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        {children}
      </AccordionDetails>
    </Accordion>
  );
};
```

### Phase 2 : Extraction des Hooks

#### 2.1 Hook d'État d'Analyse

**Fichier** : `src/components/AnalysisPanel/hooks/useAnalysisState.ts`

```typescript
export const useAnalysisState = () => {
  const { currentPuzzle, analysis, showValues } = useSelector((state: RootState) => state.puzzle);
  const [lastAnalysisTime, setLastAnalysisTime] = useState<string | null>(null);

  const getLevelStatus = useCallback((level: string) => {
    if (!analysis) return 'pending';

    switch (level) {
      case 'level0':
        return analysis.level_0 ? 'complete' : 'pending';
      case 'level1':
        return analysis.level_1 ? 'complete' : 'pending';
      case 'level2':
        return analysis.level_2 ? 'complete' : 'pending';
      case 'level3':
        return analysis.level_3 ? 'complete' : 'pending';
      default:
        return 'pending';
    }
  }, [analysis]);

  return {
    currentPuzzle,
    analysis,
    showValues,
    lastAnalysisTime,
    setLastAnalysisTime,
    getLevelStatus
  };
};
```

#### 2.2 Hook des Sections Expandées

**Fichier** : `src/components/AnalysisPanel/hooks/useExpandedSections.ts`

```typescript
export const useExpandedSections = (defaultExpanded: string[] = []) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(defaultExpanded);

  const handleAccordionChange = useCallback((section: string) => 
    (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedSections(prev =>
        isExpanded
          ? [...prev, section]
          : prev.filter(s => s !== section)
      );
    }, []
  );

  const isExpanded = useCallback((section: string) => 
    expandedSections.includes(section), [expandedSections]
  );

  return {
    expandedSections,
    handleAccordionChange,
    isExpanded
  };
};
```

### Phase 3 : Composants par Niveau

#### 3.1 Niveau 0 - Données Brutes

**Fichier** : `src/components/AnalysisPanel/components/Level0Panel.tsx`

```typescript
interface Level0PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export const Level0Panel: React.FC<Level0PanelProps> = ({ analysis, showValues }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections();

  if (!analysis.level_0) {
    return (
      <LevelHeader
        level={0}
        title="Niveau 0 : Données Brutes"
        description="Stockage des données factuelles pures, sans aucune interprétation ni calcul dérivé."
        icon={<DataObjectIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={0}
        title="Niveau 0 : Données Brutes"
        description="Stockage des données factuelles pures, sans aucune interprétation ni calcul dérivé."
        icon={<DataObjectIcon />}
        hasData={true}
      />

      {/* Exemples d'Entraînement */}
      {analysis.level_0.training_examples && (
        <Stack spacing={3}>
          {analysis.level_0.training_examples.map((example, index) => (
            <TrainingExampleDisplay
              key={example.example_id}
              example={example}
              index={index}
              showValues={showValues}
            />
          ))}
        </Stack>
      )}
    </Stack>
  );
};
```

#### 3.2 Niveau 1 - Analyses Dérivées

**Fichier** : `src/components/AnalysisPanel/components/Level1Panel.tsx`

```typescript
interface Level1PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export const Level1Panel: React.FC<Level1PanelProps> = ({ analysis, showValues }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections(['geometric_detection']);

  if (!analysis.level_1) {
    return (
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={1}
        title="Niveau 1 : Analyses Dérivées par Domaine"
        description="Analyse de chaque grille individuellement dans des domaines spécialisés."
        icon={<AnalyticsIcon />}
        hasData={true}
      />

      {/* Analyses Complètes par Exemple d'Entraînement */}
      {analysis.level_1.training_examples_analysis && (
        <Stack spacing={2}>
          {analysis.level_1.training_examples_analysis.map((exampleAnalysis, index) => (
            <ExampleAnalysisDisplay
              key={exampleAnalysis.example_id}
              exampleAnalysis={exampleAnalysis}
              index={index}
              showValues={showValues}
              expandedSections={expandedSections}
              handleAccordionChange={handleAccordionChange}
            />
          ))}
        </Stack>
      )}

      {/* Analyses Détaillées */}
      <DetailedAnalysisSection
        analysis={analysis.level_1}
        expandedSections={expandedSections}
        handleAccordionChange={handleAccordionChange}
        showValues={showValues}
      />
    </Stack>
  );
};
```

#### 3.3 Niveau 2 - Comparaisons

**Fichier** : `src/components/AnalysisPanel/components/Level2Panel.tsx`

```typescript
interface Level2PanelProps {
  analysis: PuzzleAnalysis;
  showValues: boolean;
}

export const Level2Panel: React.FC<Level2PanelProps> = ({ analysis, showValues }) => {
  const { expandedSections, handleAccordionChange, isExpanded } = useExpandedSections();

  if (!analysis.level_2) {
    return (
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={false}
      />
    );
  }

  return (
    <Stack spacing={2}>
      <LevelHeader
        level={2}
        title="Niveau 2 : Comparaisons Input/Output"
        description="Comparaisons factuelles entre input et output pour détecter les transformations appliquées."
        icon={<CompareIcon />}
        hasData={true}
      />

      {analysis.level_2.training_examples_comparisons && (
        <Stack spacing={2}>
          {analysis.level_2.training_examples_comparisons.map((comparison, index) => (
            <ComparisonDisplay
              key={comparison.example_id}
              comparison={comparison}
              index={index}
              expandedSections={expandedSections}
              handleAccordionChange={handleAccordionChange}
            />
          ))}
        </Stack>
      )}
    </Stack>
  );
};
```

#### 3.4 Niveau 3 - Synthèse

**Fichier** : `src/components/AnalysisPanel/components/Level3Panel.tsx`

```typescript
interface Level3PanelProps {
  analysis: PuzzleAnalysis;
}

export const Level3Panel: React.FC<Level3PanelProps> = ({ analysis }) => {
  return (
    <Stack spacing={2}>
      <LevelHeader
        level={3}
        title="Niveau 3 : Synthèse par Entraînement"
        description="Synthèse des patterns récurrents à travers tous les exemples d'entraînement."
        icon={<PsychologyIcon />}
        hasData={!!analysis.level_3}
      />

      {analysis.level_3 ? (
        <SynthesisDisplay analysis={analysis.level_3} />
      ) : (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Le Niveau 3 (synthèse par entraînement) nécessite plusieurs exemples d'entraînement pour fonctionner.
          </Typography>
        </Alert>
      )}
    </Stack>
  );
};
```

### Phase 4 : Composant Principal Refactorisé

#### 4.1 AnalysisPanel Principal

**Fichier** : `src/components/AnalysisPanel/AnalysisPanel.tsx` (refactorisé)

```typescript
import React, { useState, useEffect, Suspense } from 'react';
import { Paper, Typography, Box, Tabs, Tab, Stack, Button, CircularProgress, Alert, Tooltip, Chip } from '@mui/material';
import { DataObjectIcon, AnalyticsIcon, CompareIcon, PsychologyIcon, RefreshIcon } from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';

// Lazy loading des composants de niveau
const Level0Panel = React.lazy(() => import('./components/Level0Panel'));
const Level1Panel = React.lazy(() => import('./components/Level1Panel'));
const Level2Panel = React.lazy(() => import('./components/Level2Panel'));
const Level3Panel = React.lazy(() => import('./components/Level3Panel'));

// Composants partagés
import { TabPanel } from './components/shared/TabPanel';

// Hooks
import { useAnalysisState } from './hooks/useAnalysisState';
import { useAnalyzePuzzleMutation } from '../../store';
import { setAnalysis } from '../../store/slices/puzzleSlice';
import { dataStorage } from '../../services/dataStorage';

const AnalysisPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { currentPuzzle, analysis, showValues, lastAnalysisTime, setLastAnalysisTime, getLevelStatus } = useAnalysisState();
  const [analyzePuzzle, { isLoading }] = useAnalyzePuzzleMutation();
  const [currentTab, setCurrentTab] = useState(0);

  const handleAnalyze = async () => {
    if (!currentPuzzle) return;

    try {
      const result = await analyzePuzzle(currentPuzzle).unwrap();
      if (result) {
        dispatch(setAnalysis(result));
        setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
    }
  };

  // Auto-analyse lors du changement de puzzle
  useEffect(() => {
    const autoAnalyze = async () => {
      if (!currentPuzzle) return;

      const cachedAnalysis = dataStorage.getAnalysis(currentPuzzle.id);

      if (cachedAnalysis) {
        dispatch(setAnalysis(cachedAnalysis.analysis));
        setLastAnalysisTime(new Date(cachedAnalysis.timestamp).toLocaleString('fr-FR'));
      } else {
        try {
          const result = await analyzePuzzle(currentPuzzle).unwrap();
          if (result) {
            dispatch(setAnalysis(result));
            setLastAnalysisTime(new Date().toLocaleString('fr-FR'));
          }
        } catch (error) {
          console.error('Erreur lors de l\'auto-analyse:', error);
        }
      }
    };

    autoAnalyze();
  }, [currentPuzzle?.id, analyzePuzzle, dispatch]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  if (!currentPuzzle) {
    return (
      <Paper sx={{ p: 2, height: '100%' }}>
        <Typography variant="h6" gutterBottom>
          Analyse ARC AGI
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Sélectionnez un puzzle pour voir l'analyse en 4 niveaux
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header avec contrôles */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Analyse ARC AGI</Typography>
            <Stack direction="column" alignItems="flex-end" spacing={0.5}>
              <Button
                size="small"
                startIcon={isLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                onClick={handleAnalyze}
                disabled={isLoading}
                variant="contained"
              >
                {isLoading ? 'Analyse...' : 'Analyser'}
              </Button>
              {lastAnalysisTime && (
                <Typography variant="caption" color="text.secondary">
                  Dernière analyse: {lastAnalysisTime}
                </Typography>
              )}
            </Stack>
          </Stack>

          <Alert severity="info" sx={{ py: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              Architecture Pure en 4 Niveaux : Données Brutes → Analyses Dérivées → Comparaisons → Synthèse
            </Typography>
          </Alert>

          {/* Indicateurs de progression par niveau */}
          {analysis && (
            <Stack direction="row" spacing={1} justifyContent="center">
              {[
                { key: 'level0', label: 'Niveau 0', icon: <DataObjectIcon /> },
                { key: 'level1', label: 'Niveau 1', icon: <AnalyticsIcon /> },
                { key: 'level2', label: 'Niveau 2', icon: <CompareIcon /> },
                { key: 'level3', label: 'Niveau 3', icon: <PsychologyIcon /> }
              ].map((level, index) => (
                <Tooltip key={level.key} title={level.label}>
                  <Chip
                    icon={level.icon}
                    label={`N${index}`}
                    size="small"
                    color={getLevelStatus(level.key) === 'complete' ? 'success' : 'default'}
                    variant={getLevelStatus(level.key) === 'complete' ? 'filled' : 'outlined'}
                  />
                </Tooltip>
              ))}
            </Stack>
          )}
        </Stack>
      </Box>

      {/* Onglets pour les différents niveaux */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ minHeight: 48 }}
        >
          <Tab icon={<DataObjectIcon />} label="Niveau 0" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<AnalyticsIcon />} label="Niveau 1" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<CompareIcon />} label="Niveau 2" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
          <Tab icon={<PsychologyIcon />} label="Niveau 3" iconPosition="start" sx={{ minHeight: 48, fontSize: '0.875rem' }} />
        </Tabs>
      </Box>

      {/* Contenu des onglets avec lazy loading */}
      <Box sx={{ flex: '1 1 0', overflowY: 'auto', overflowX: 'hidden', minHeight: 0 }}>
        {!analysis ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom color="text.secondary">
              Analyse ARC AGI en 4 Niveaux
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Cliquez sur "Analyser" pour obtenir une analyse complète selon l'architecture pure en niveaux
            </Typography>
          </Box>
        ) : (
          <>
            <TabPanel value={currentTab} index={0}>
              <Suspense fallback={<CircularProgress />}>
                <Level0Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={1}>
              <Suspense fallback={<CircularProgress />}>
                <Level1Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={2}>
              <Suspense fallback={<CircularProgress />}>
                <Level2Panel analysis={analysis} showValues={showValues} />
              </Suspense>
            </TabPanel>

            <TabPanel value={currentTab} index={3}>
              <Suspense fallback={<CircularProgress />}>
                <Level3Panel analysis={analysis} />
              </Suspense>
            </TabPanel>
          </>
        )}
      </Box>
    </Paper>
  );
};

export default AnalysisPanel;
```

## 📊 Bénéfices du Refactoring

### 🎯 Maintenabilité
- **Fichiers plus petits** : ~300-500 lignes par composant vs 2600+ lignes
- **Responsabilité unique** : Chaque composant gère un seul niveau
- **Code focalisé** : Plus facile à comprendre et modifier

### ⚡ Performance
- **Lazy loading** : Chargement à la demande des composants
- **Bundle splitting** : Réduction de la taille du bundle initial
- **Memoization** : Optimisations possibles par composant

### 🧪 Testabilité
- **Tests isolés** : Tests unitaires par niveau
- **Mocking facilité** : Interfaces claires entre composants
- **Coverage amélioré** : Tests plus précis et complets

### 🔄 Réutilisabilité
- **Composants indépendants** : Réutilisables dans d'autres contextes
- **Hooks partagés** : Logique métier réutilisable
- **Types centralisés** : Cohérence des interfaces

## 🚀 Plan d'Implémentation

### Étape 1 : Préparation (1-2h)
1. Créer la structure de dossiers
2. Extraire les types communs
3. Créer les hooks de base

### Étape 2 : Composants Partagés (2-3h)
1. Implémenter `TabPanel`
2. Implémenter `LevelHeader`
3. Implémenter `AnalysisAccordion`
4. Créer les composants utilitaires

### Étape 3 : Composants par Niveau (4-6h)
1. Extraire et adapter `Level0Panel`
2. Extraire et adapter `Level1Panel`
3. Extraire et adapter `Level2Panel`
4. Créer `Level3Panel`

### Étape 4 : Refactoring Principal (1-2h)
1. Simplifier `AnalysisPanel.tsx`
2. Intégrer le lazy loading
3. Tester l'intégration

### Étape 5 : Tests et Optimisations (2-3h)
1. Tests unitaires par composant
2. Tests d'intégration
3. Optimisations de performance

## 📝 Notes d'Implémentation

### Gestion des États
- Utiliser des hooks personnalisés pour la logique partagée
- Props drilling minimal grâce aux hooks
- État local pour les interactions spécifiques au niveau

### Performance
- Lazy loading avec `React.lazy()` et `Suspense`
- Memoization avec `React.memo()` pour les composants coûteux
- Virtualisation possible pour les listes longues

### Accessibilité
- Maintenir les attributs ARIA existants
- Navigation au clavier préservée
- Lecteurs d'écran compatibles

### Compatibilité
- Interfaces identiques pour l'utilisateur final
- Pas de breaking changes dans l'API
- Migration transparente

---

**Ce refactoring transformera un composant monolithique de 2600+ lignes en une architecture modulaire, maintenable et performante, tout en préservant toutes les fonctionnalités existantes.**