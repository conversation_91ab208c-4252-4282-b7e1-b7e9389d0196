import React from 'react';
import { Typography, Box, Alert, Stack } from '@mui/material';
import { LevelHeaderProps } from '../../types/AnalysisPanelTypes';

export const LevelHeader: React.FC<LevelHeaderProps> = ({
  level,
  title,
  description,
  icon,
  hasData,
  lastAnalysisTime
}) => {
  return (
    <Stack spacing={2}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        {icon}
        <Box sx={{ ml: 1 }}>
          {title}
        </Box>
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          {description}
        </Typography>
      </Alert>

      {!hasData && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Aucune donnée du Niveau {level} disponible. Lancez une analyse pour voir les résultats.
          </Typography>
        </Alert>
      )}

      {lastAnalysisTime && (
        <Typography variant="caption" color="text.secondary">
          Dernière analyse: {lastAnalysisTime}
        </Typography>
      )}
    </Stack>
  );
};