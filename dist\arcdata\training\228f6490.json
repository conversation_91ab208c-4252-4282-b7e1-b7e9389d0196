{"train": [{"input": [[7, 0, 0, 0, 0, 0, 0, 0, 7, 7], [0, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 5, 0, 0, 5, 5, 0, 6, 6, 0], [0, 5, 0, 0, 5, 5, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 5, 5, 5, 5, 5], [0, 8, 8, 0, 0, 5, 5, 0, 0, 5], [0, 8, 8, 0, 0, 5, 5, 5, 5, 5]], "output": [[7, 0, 0, 0, 0, 0, 0, 0, 7, 7], [0, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 5, 8, 8, 5, 5, 0, 0, 0, 0], [0, 5, 8, 8, 5, 5, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 0, 0, 0, 0], [0, 5, 5, 5, 5, 5, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 5, 5, 5, 5, 5], [0, 0, 0, 0, 0, 5, 5, 6, 6, 5], [0, 0, 0, 0, 0, 5, 5, 5, 5, 5]]}, {"input": [[5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [5, 0, 0, 0, 5, 0, 9, 9, 9, 9], [5, 5, 5, 0, 5, 0, 9, 9, 9, 9], [5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 0, 6], [3, 3, 3, 0, 0, 0, 6, 6, 0, 0], [0, 0, 3, 5, 5, 5, 5, 5, 5, 0], [0, 0, 0, 5, 0, 0, 0, 0, 5, 0], [6, 6, 0, 5, 0, 0, 0, 0, 5, 0], [6, 6, 0, 5, 5, 5, 5, 5, 5, 0]], "output": [[5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [5, 3, 3, 3, 5, 0, 0, 0, 0, 0], [5, 5, 5, 3, 5, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 0, 6], [0, 0, 0, 0, 0, 0, 6, 6, 0, 0], [0, 0, 0, 5, 5, 5, 5, 5, 5, 0], [0, 0, 0, 5, 9, 9, 9, 9, 5, 0], [6, 6, 0, 5, 9, 9, 9, 9, 5, 0], [6, 6, 0, 5, 5, 5, 5, 5, 5, 0]]}, {"input": [[2, 2, 0, 0, 5, 5, 5, 5, 5, 5], [2, 2, 2, 0, 5, 0, 0, 0, 5, 5], [0, 0, 0, 0, 5, 5, 5, 0, 0, 5], [0, 4, 4, 0, 5, 5, 5, 5, 5, 5], [0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 0, 4, 4, 0], [5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [5, 0, 0, 5, 5, 0, 0, 0, 0, 4], [5, 0, 0, 0, 5, 0, 8, 8, 8, 0], [5, 5, 5, 5, 5, 0, 0, 0, 8, 8]], "output": [[0, 0, 0, 0, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 5, 8, 8, 8, 5, 5], [0, 0, 0, 0, 5, 5, 5, 8, 8, 5], [0, 4, 4, 0, 5, 5, 5, 5, 5, 5], [0, 0, 4, 0, 0, 4, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 0, 4, 4, 0], [5, 5, 5, 5, 5, 0, 0, 0, 0, 0], [5, 2, 2, 5, 5, 0, 0, 0, 0, 4], [5, 2, 2, 2, 5, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 5, 5, 5, 5, 5, 0, 0, 2], [2, 0, 5, 0, 0, 0, 5, 0, 0, 0], [0, 0, 5, 5, 0, 5, 5, 4, 4, 4], [0, 0, 5, 5, 5, 5, 5, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 0, 0, 2], [7, 7, 7, 0, 0, 2, 0, 2, 0, 0], [0, 7, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 5, 5, 5, 5], [0, 2, 0, 5, 0, 0, 0, 5, 5, 5], [2, 0, 0, 5, 5, 5, 5, 5, 5, 5]], "output": [[0, 0, 5, 5, 5, 5, 5, 0, 0, 2], [2, 0, 5, 7, 7, 7, 5, 0, 0, 0], [0, 0, 5, 5, 7, 5, 5, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 0, 0, 2], [0, 0, 0, 0, 0, 2, 0, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 5, 5, 5, 5], [0, 2, 0, 5, 4, 4, 4, 5, 5, 5], [2, 0, 0, 5, 5, 5, 5, 5, 5, 5]]}]}