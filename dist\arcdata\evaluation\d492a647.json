{"train": [{"input": [[5, 0, 5, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 5, 5], [0, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0], [5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 0], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0], [5, 5, 5, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 5, 0, 0], [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 0], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5], [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5], [0, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 0, 0], [5, 5, 5, 5, 0, 5, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0], [0, 5, 5, 0, 0, 5, 0, 5, 0, 0, 0, 0, 5, 5, 0, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5]], "output": [[5, 0, 5, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 5, 5], [0, 5, 5, 3, 5, 5, 5, 3, 5, 3, 0, 5, 0, 3, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0], [5, 5, 0, 3, 0, 3, 0, 3, 0, 3, 0, 5, 0, 5, 5, 3], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0], [5, 5, 5, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 5, 0, 3], [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 0], [0, 5, 5, 3, 0, 3, 0, 3, 0, 3, 0, 3, 5, 5, 5, 5], [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5], [0, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 0, 3], [5, 5, 5, 5, 0, 5, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0], [0, 5, 5, 3, 0, 5, 0, 5, 0, 3, 0, 3, 5, 5, 0, 5], [5, 5, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0, 5]]}, {"input": [[0, 0, 5, 0, 5, 5, 5, 0, 5, 0, 5, 5, 5], [5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 5, 5, 5], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 0, 0, 1, 0, 0, 0, 0, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 0, 5, 5, 0, 5, 0, 5, 0, 5, 5, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 0, 5]], "output": [[0, 1, 5, 1, 5, 5, 5, 1, 5, 1, 5, 5, 5], [5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 5, 5, 5], [5, 1, 5, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 1, 0, 1, 0, 1, 0, 1, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 5, 5, 1, 0, 1, 0, 1, 0, 1, 0, 1, 5], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 1, 5, 5, 0, 5, 0, 5, 0, 5, 5, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], [5, 5, 0, 5, 0, 5, 5, 5, 5, 1, 5, 1, 5]]}], "test": [{"input": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 5, 0], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 5, 5, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [0, 0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 5, 5, 5, 0], [0, 5, 5, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 0, 5, 5, 5], [0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5]], "output": [[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 5, 0], [2, 5, 5, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 5, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5], [5, 0, 5, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [2, 5, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 5, 2], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 5, 5, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 5, 5], [5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 5, 5, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 5], [0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [5, 5, 5, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 5, 5], [0, 0, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 5, 5, 5, 0], [2, 5, 5, 0, 2, 0, 2, 0, 5, 5, 2, 5, 5, 0, 5, 5, 5], [0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5]]}]}