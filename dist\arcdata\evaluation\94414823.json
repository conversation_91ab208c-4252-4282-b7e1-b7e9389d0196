{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 8, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 8, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 4, 4, 8, 8, 5, 0, 0], [0, 0, 5, 4, 4, 8, 8, 5, 0, 0], [0, 0, 5, 8, 8, 4, 4, 5, 0, 0], [0, 0, 5, 8, 8, 4, 4, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 6, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 7, 7, 6, 6, 5, 0, 0], [0, 0, 5, 7, 7, 6, 6, 5, 0, 0], [0, 0, 5, 6, 6, 7, 7, 5, 0, 0], [0, 0, 5, 6, 6, 7, 7, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 7, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 3, 3, 2, 2, 5, 0, 0], [0, 0, 5, 3, 3, 2, 2, 5, 0, 0], [0, 0, 5, 2, 2, 3, 3, 5, 0, 0], [0, 0, 5, 2, 2, 3, 3, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 9, 0, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 1, 1, 9, 9, 5, 0, 0], [0, 0, 5, 1, 1, 9, 9, 5, 0, 0], [0, 0, 5, 9, 9, 1, 1, 5, 0, 0], [0, 0, 5, 9, 9, 1, 1, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 9, 0, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}]}