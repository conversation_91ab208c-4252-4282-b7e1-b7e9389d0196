{"train": [{"input": [[1, 1, 1], [6, 6, 6], [6, 1, 6]], "output": [[1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1], [6, 6, 6, 6, 6, 6], [6, 6, 6, 6, 6, 6], [6, 6, 1, 1, 6, 6], [6, 6, 1, 1, 6, 6]]}, {"input": [[4, 4, 7], [8, 7, 7], [8, 8, 4]], "output": [[4, 4, 4, 4, 4, 4, 7, 7, 7], [4, 4, 4, 4, 4, 4, 7, 7, 7], [4, 4, 4, 4, 4, 4, 7, 7, 7], [8, 8, 8, 7, 7, 7, 7, 7, 7], [8, 8, 8, 7, 7, 7, 7, 7, 7], [8, 8, 8, 7, 7, 7, 7, 7, 7], [8, 8, 8, 8, 8, 8, 4, 4, 4], [8, 8, 8, 8, 8, 8, 4, 4, 4], [8, 8, 8, 8, 8, 8, 4, 4, 4]]}, {"input": [[4, 2, 8], [2, 2, 5], [8, 5, 4]], "output": [[4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [4, 4, 4, 4, 2, 2, 2, 2, 8, 8, 8, 8], [2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5], [2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5], [8, 8, 8, 8, 5, 5, 5, 5, 4, 4, 4, 4], [8, 8, 8, 8, 5, 5, 5, 5, 4, 4, 4, 4], [8, 8, 8, 8, 5, 5, 5, 5, 4, 4, 4, 4], [8, 8, 8, 8, 5, 5, 5, 5, 4, 4, 4, 4]]}, {"input": [[8, 8, 8], [8, 8, 8], [8, 8, 8]], "output": [[8, 8, 8], [8, 8, 8], [8, 8, 8]]}, {"input": [[3, 3, 3], [3, 3, 3], [3, 3, 3]], "output": [[3, 3, 3], [3, 3, 3], [3, 3, 3]]}, {"input": [[3, 6, 6], [3, 6, 6], [3, 3, 3]], "output": [[3, 3, 6, 6, 6, 6], [3, 3, 6, 6, 6, 6], [3, 3, 6, 6, 6, 6], [3, 3, 6, 6, 6, 6], [3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3]]}, {"input": [[2, 2, 4], [4, 4, 4], [2, 4, 2]], "output": [[2, 2, 2, 2, 4, 4], [2, 2, 2, 2, 4, 4], [4, 4, 4, 4, 4, 4], [4, 4, 4, 4, 4, 4], [2, 2, 4, 4, 2, 2], [2, 2, 4, 4, 2, 2]]}], "test": [{"input": [[7, 1, 7], [3, 3, 6], [8, 8, 6]], "output": [[7, 7, 7, 7, 7, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7], [7, 7, 7, 7, 7, 1, 1, 1, 1, 1, 7, 7, 7, 7, 7], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 6, 6], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 6, 6, 6, 6, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 6, 6, 6, 6, 6]]}]}