{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 4, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 4, 0, 2, 0], [0, 0, 5, 0, 0, 0, 0, 4, 0, 2, 0], [0, 0, 5, 0, 0, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 2, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0]], "output": [[2], [2], [2], [2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 8, 2, 0, 0, 0, 0, 6, 6, 6, 0], [0, 0, 2, 8, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 8, 2, 0, 0], [6, 6, 6, 0, 0, 0, 0, 0, 2, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[7, 7], [7, 7]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 6, 6, 6, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 6, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0], [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 6, 6, 6, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 1, 2], [1, 1, 1]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 0], [0, 4, 4, 0, 0, 0, 0, 8, 4, 0, 0, 0, 0], [0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[4, 8], [8, 8], [8, 4]]}]}