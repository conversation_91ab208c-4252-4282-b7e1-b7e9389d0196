{"train": [{"input": [[3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [0, 0, 0, 0, 2, 0, 0, 0, 0, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0], [3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 3, 3, 3, 3, 2, 0, 0, 0, 0]], "output": [[0, 0, 0, 3], [0, 0, 3, 0], [0, 3, 0, 0], [0, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 2, 2, 2, 2, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [2, 2, 2, 2, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [2, 2, 2, 2, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [2, 2, 2, 2, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 1, 1, 1, 1, 8, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3], [0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8, 3, 3, 3, 3]], "output": [[0, 0, 2, 0], [0, 0, 1, 2], [0, 1, 0, 0], [3, 0, 0, 0]]}, {"input": [[0, 0, 0, 2, 8, 8, 8, 2, 0, 0, 0], [0, 0, 0, 2, 8, 8, 8, 2, 0, 0, 0], [0, 0, 0, 2, 8, 8, 8, 2, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 8, 8, 2, 8, 8, 8, 2, 0, 0, 0], [8, 8, 8, 2, 8, 8, 8, 2, 0, 0, 0], [8, 8, 8, 2, 8, 8, 8, 2, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [0, 0, 0, 2, 0, 0, 0, 2, 4, 4, 4], [0, 0, 0, 2, 0, 0, 0, 2, 4, 4, 4], [0, 0, 0, 2, 0, 0, 0, 2, 4, 4, 4]], "output": [[0, 8, 0], [0, 8, 8], [4, 0, 0]]}], "test": [{"input": [[1, 1, 1, 1, 1, 8, 3, 3, 3, 3, 3, 8, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 3, 3, 3, 3, 3, 8, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 3, 3, 3, 3, 3, 8, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 3, 3, 3, 3, 3, 8, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 8, 3, 3, 3, 3, 3, 8, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 1], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [2, 2, 2, 2, 2, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 2, 2, 2, 2, 2], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 3, 3, 3, 3, 3, 8, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0]], "output": [[1, 1, 3, 1], [0, 0, 3, 0], [2, 0, 3, 2], [0, 0, 3, 0]]}]}