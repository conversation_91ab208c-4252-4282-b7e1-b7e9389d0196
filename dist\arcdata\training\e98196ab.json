{"train": [{"input": [[0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 0, 0, 0, 0, 0, 0, 0, 8, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]], "output": [[0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], [0, 0, 0, 8, 0, 0, 0, 8, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0], [1, 8, 0, 0, 0, 0, 0, 0, 0, 8, 1]]}, {"input": [[0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 0, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 0, 0, 0, 0, 0, 7, 0, 0, 0, 7]], "output": [[0, 3, 0, 0, 0, 3, 0, 0, 0, 3, 7], [7, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0], [0, 0, 7, 3, 7, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 3, 0, 0, 0, 0, 7, 0, 0, 0, 7]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0]], "output": [[2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 2], [0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], [0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0], [2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 1]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 0, 7, 7, 0, 0, 0, 0, 7, 0, 0], [0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [0, 7, 0, 7, 0, 0, 0, 0, 0, 0, 7], [0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], [6, 0, 0, 0, 6, 0, 6, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6], [6, 0, 0, 0, 6, 0, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0], [6, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0]], "output": [[6, 0, 0, 0, 6, 0, 6, 0, 0, 0, 7], [0, 0, 7, 7, 0, 0, 0, 0, 7, 0, 6], [6, 0, 0, 0, 6, 0, 7, 0, 0, 0, 6], [0, 7, 0, 7, 0, 0, 0, 0, 0, 6, 7], [6, 0, 0, 6, 0, 0, 7, 0, 0, 0, 0]]}]}