{"train": [{"input": [[0, 8, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 8, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [1, 0, 0, 1, 0, 0, 2, 2, 2, 2, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 2, 0, 0, 2, 1, 1, 1, 1, 1, 1, 1, 8, 1, 0, 1], [1, 1, 1, 1, 1, 0, 2, 0, 0, 2, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0], [1, 0, 0, 0, 0, 1, 2, 0, 0, 2, 1, 8, 1, 1, 1, 1, 1, 0, 1, 1, 1], [0, 0, 1, 1, 0, 1, 2, 2, 2, 2, 1, 0, 1, 0, 0, 1, 1, 8, 0, 0, 8], [0, 1, 8, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 8, 1, 1, 0, 0], [1, 1, 1, 8, 8, 1, 1, 1, 0, 0, 8, 1, 1, 1, 1, 1, 8, 1, 0, 0, 1], [8, 1, 0, 1, 1, 1, 1, 0, 8, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 1], [8, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 8, 1, 1, 8, 1], [1, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1], [1, 0, 8, 1, 1, 8, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 8, 1, 1, 1], [1, 1, 8, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 8, 1, 0, 1, 0, 1, 1, 8], [1, 1, 1, 1, 1, 1, 0, 0, 8, 1, 0, 0, 1, 1, 8, 1, 1, 8, 1, 0, 1], [8, 8, 8, 1, 1, 1, 1, 8, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1], [1, 1, 0, 1, 8, 0, 0, 8, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0], [1, 8, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0], [1, 1, 8, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1], [1, 1, 0, 0, 8, 1, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 8, 0, 0, 0, 0], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 8, 1, 8, 0]], "output": [[0, 8, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [1, 1, 0, 8, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [1, 0, 0, 1, 0, 0, 2, 2, 2, 2, 1, 1, 1, 1, 1, 8, 1, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1, 2, 0, 0, 2, 1, 1, 1, 1, 1, 1, 1, 8, 1, 0, 1], [1, 1, 1, 1, 1, 0, 2, 0, 0, 2, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0], [1, 0, 0, 0, 0, 1, 2, 0, 0, 2, 1, 8, 1, 1, 1, 1, 1, 0, 1, 1, 1], [0, 0, 1, 1, 0, 1, 2, 2, 2, 2, 1, 0, 1, 0, 0, 1, 1, 8, 0, 0, 8], [0, 1, 8, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 8, 1, 1, 0, 0], [1, 1, 1, 8, 8, 1, 1, 1, 0, 0, 8, 1, 1, 1, 1, 1, 8, 1, 0, 0, 1], [8, 1, 0, 1, 1, 1, 1, 0, 8, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 1], [8, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 8, 1, 1, 8, 1], [1, 1, 1, 8, 1, 0, 1, 1, 8, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1], [1, 0, 8, 1, 1, 8, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 8, 1, 1, 1], [1, 1, 8, 1, 1, 1, 0, 1, 0, 2, 2, 2, 2, 8, 1, 0, 1, 0, 1, 1, 8], [1, 1, 1, 1, 1, 1, 0, 0, 8, 2, 0, 0, 2, 1, 8, 1, 1, 8, 1, 0, 1], [8, 8, 8, 1, 1, 1, 1, 8, 1, 2, 0, 0, 2, 1, 0, 1, 1, 1, 1, 0, 1], [1, 1, 0, 1, 8, 0, 0, 8, 1, 2, 0, 0, 2, 1, 1, 1, 0, 1, 0, 1, 0], [1, 8, 8, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 0, 0, 1, 1, 0], [1, 1, 8, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1], [1, 1, 0, 0, 8, 1, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 8, 0, 0, 0, 0], [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 8, 1, 8, 0]]}, {"input": [[3, 0, 3, 4, 3, 3, 3, 3, 0, 3, 3, 4, 0, 3, 0, 4, 3, 4, 4, 0, 0], [3, 3, 0, 0, 3, 3, 3, 4, 0, 0, 4, 4, 4, 3, 0, 0, 3, 3, 4, 0, 3], [4, 4, 4, 3, 4, 3, 0, 3, 0, 0, 4, 3, 0, 3, 3, 4, 3, 0, 0, 3, 0], [0, 4, 4, 4, 3, 0, 3, 3, 3, 0, 3, 0, 3, 0, 0, 0, 0, 3, 4, 3, 3], [3, 3, 0, 4, 3, 3, 0, 0, 0, 0, 3, 0, 4, 4, 4, 3, 0, 3, 0, 0, 0], [0, 3, 0, 0, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 3, 3, 4, 3, 0, 3], [0, 3, 0, 0, 3, 4, 0, 3, 4, 0, 4, 4, 0, 0, 3, 4, 0, 0, 0, 3, 3], [0, 3, 3, 3, 0, 4, 4, 3, 4, 3, 0, 3, 3, 3, 4, 0, 3, 0, 3, 3, 3], [4, 0, 4, 3, 4, 3, 4, 4, 0, 0, 4, 0, 0, 0, 0, 3, 0, 3, 3, 0, 0], [0, 0, 4, 0, 0, 0, 0, 3, 4, 4, 3, 4, 0, 0, 0, 4, 0, 0, 4, 3, 3], [3, 0, 0, 8, 8, 8, 8, 8, 4, 3, 0, 3, 3, 0, 4, 4, 0, 4, 4, 4, 4], [3, 3, 0, 8, 0, 0, 0, 8, 3, 0, 0, 0, 0, 4, 0, 3, 3, 0, 4, 3, 3], [0, 0, 0, 8, 0, 0, 0, 8, 3, 3, 0, 3, 3, 4, 3, 0, 4, 0, 3, 0, 0], [3, 0, 4, 8, 8, 8, 8, 8, 0, 3, 0, 3, 0, 0, 3, 3, 3, 0, 4, 3, 0], [4, 0, 0, 0, 0, 3, 0, 4, 0, 0, 3, 0, 0, 3, 3, 3, 4, 0, 4, 0, 3], [0, 0, 4, 3, 0, 0, 0, 3, 0, 0, 3, 4, 0, 0, 4, 0, 0, 3, 4, 3, 4], [4, 4, 0, 0, 3, 0, 3, 4, 4, 3, 4, 3, 4, 0, 4, 4, 0, 3, 4, 3, 4], [3, 4, 3, 3, 0, 0, 0, 0, 3, 0, 3, 4, 0, 0, 0, 3, 3, 3, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 3, 4, 0, 3, 3, 3, 4, 0, 4, 0, 3, 4, 0], [3, 3, 3, 0, 4, 0, 4, 3, 0, 0, 0, 3, 0, 0, 3, 3, 0, 0, 4, 3, 0], [0, 4, 3, 3, 3, 0, 4, 4, 3, 4, 3, 4, 0, 4, 3, 4, 4, 0, 0, 4, 0]], "output": [[3, 0, 3, 4, 3, 3, 3, 3, 0, 3, 3, 4, 0, 3, 0, 4, 3, 4, 4, 0, 0], [3, 3, 0, 0, 3, 3, 3, 4, 0, 0, 4, 4, 4, 3, 0, 0, 3, 3, 4, 0, 3], [4, 4, 4, 3, 4, 3, 0, 3, 0, 0, 4, 3, 0, 3, 3, 4, 3, 0, 0, 3, 0], [0, 4, 4, 4, 3, 0, 3, 3, 3, 0, 3, 0, 3, 0, 0, 0, 0, 3, 4, 3, 3], [3, 3, 0, 4, 3, 3, 0, 0, 0, 0, 3, 0, 4, 4, 4, 3, 0, 3, 0, 0, 0], [0, 3, 0, 0, 3, 0, 0, 3, 0, 3, 0, 0, 0, 3, 3, 3, 3, 4, 3, 0, 3], [0, 3, 0, 0, 3, 4, 0, 3, 4, 0, 4, 4, 0, 0, 3, 4, 0, 0, 0, 3, 3], [0, 3, 3, 3, 0, 4, 4, 3, 4, 3, 0, 8, 8, 8, 8, 8, 3, 0, 3, 3, 3], [4, 0, 4, 3, 4, 3, 4, 4, 0, 0, 4, 8, 0, 0, 0, 8, 0, 3, 3, 0, 0], [0, 0, 4, 0, 0, 0, 0, 3, 4, 4, 3, 8, 0, 0, 0, 8, 0, 0, 4, 3, 3], [3, 0, 0, 8, 8, 8, 8, 8, 4, 3, 0, 8, 8, 8, 8, 8, 0, 4, 4, 4, 4], [3, 3, 0, 8, 0, 0, 0, 8, 3, 0, 0, 0, 0, 4, 0, 3, 3, 0, 4, 3, 3], [0, 0, 0, 8, 0, 0, 0, 8, 3, 3, 0, 3, 3, 4, 3, 0, 4, 0, 3, 0, 0], [3, 0, 4, 8, 8, 8, 8, 8, 0, 3, 0, 3, 0, 0, 3, 3, 3, 0, 4, 3, 0], [4, 0, 0, 0, 0, 3, 0, 4, 0, 0, 3, 0, 0, 3, 3, 3, 4, 0, 4, 0, 3], [0, 0, 4, 3, 0, 0, 0, 3, 0, 0, 3, 4, 0, 0, 4, 0, 0, 3, 4, 3, 4], [4, 4, 0, 0, 3, 0, 3, 4, 4, 3, 4, 3, 4, 0, 4, 4, 0, 3, 4, 3, 4], [3, 4, 3, 3, 0, 0, 0, 0, 3, 0, 3, 4, 0, 0, 0, 3, 3, 3, 3, 0, 3], [0, 0, 0, 0, 0, 3, 0, 3, 3, 4, 0, 3, 3, 3, 4, 0, 4, 0, 3, 4, 0], [3, 3, 3, 0, 4, 0, 4, 3, 0, 0, 0, 3, 0, 0, 3, 3, 0, 0, 4, 3, 0], [0, 4, 3, 3, 3, 0, 4, 4, 3, 4, 3, 4, 0, 4, 3, 4, 4, 0, 0, 4, 0]]}, {"input": [[0, 0, 3, 0, 3, 2, 0, 2, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3], [3, 2, 2, 0, 3, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 2, 0, 3, 2], [3, 3, 0, 3, 0, 0, 3, 2, 2, 2, 2, 3, 2, 2, 2, 2, 3, 0, 0, 3, 2], [2, 2, 3, 2, 4, 4, 4, 4, 4, 4, 3, 0, 3, 2, 0, 2, 2, 2, 0, 0, 3], [3, 3, 2, 0, 4, 0, 0, 0, 0, 4, 2, 0, 2, 2, 0, 2, 3, 0, 2, 2, 0], [3, 2, 2, 2, 4, 0, 0, 0, 0, 4, 0, 3, 2, 2, 3, 2, 2, 3, 3, 2, 0], [2, 0, 2, 0, 4, 0, 0, 0, 0, 4, 2, 0, 0, 0, 2, 2, 2, 0, 2, 2, 2], [0, 2, 0, 2, 4, 4, 4, 4, 4, 4, 2, 2, 0, 2, 0, 2, 0, 0, 2, 2, 2], [2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 3, 2, 3, 3, 0, 2, 0, 0, 0, 2, 2], [0, 2, 3, 0, 3, 0, 2, 3, 2, 2, 2, 0, 2, 0, 0, 0, 2, 2, 3, 2, 0], [3, 0, 2, 0, 2, 0, 0, 2, 2, 0, 3, 3, 2, 3, 0, 3, 3, 0, 0, 3, 0], [2, 3, 0, 3, 2, 2, 2, 2, 2, 0, 0, 0, 0, 2, 0, 2, 0, 3, 0, 0, 2], [3, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 2, 2, 2, 3, 0, 2, 2, 2, 2, 2], [3, 3, 3, 2, 0, 2, 0, 2, 0, 3, 2, 2, 2, 0, 0, 3, 2, 2, 3, 2, 2], [0, 0, 2, 2, 2, 3, 2, 0, 0, 2, 3, 2, 0, 3, 0, 2, 2, 3, 2, 2, 0], [2, 2, 2, 2, 2, 3, 2, 3, 3, 3, 2, 0, 0, 0, 0, 2, 0, 0, 2, 3, 0], [2, 2, 2, 2, 3, 0, 0, 3, 3, 2, 0, 0, 0, 0, 0, 0, 2, 2, 3, 2, 0], [2, 0, 3, 2, 2, 2, 3, 2, 3, 3, 3, 0, 0, 0, 0, 0, 2, 0, 0, 2, 3], [2, 2, 0, 0, 0, 0, 0, 0, 0, 3, 2, 3, 2, 2, 3, 0, 0, 2, 2, 0, 0], [0, 3, 0, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 3, 0, 2, 0, 0, 0, 3, 2], [2, 3, 2, 2, 2, 0, 0, 3, 2, 0, 3, 2, 0, 2, 2, 2, 3, 0, 0, 2, 2]], "output": [[0, 0, 3, 0, 3, 2, 0, 2, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3], [3, 2, 2, 0, 3, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 2, 0, 3, 2], [3, 3, 0, 3, 0, 0, 3, 2, 2, 2, 2, 3, 2, 2, 2, 2, 3, 0, 0, 3, 2], [2, 2, 3, 2, 4, 4, 4, 4, 4, 4, 3, 0, 3, 2, 0, 2, 2, 2, 0, 0, 3], [3, 3, 2, 0, 4, 0, 0, 0, 0, 4, 2, 0, 2, 2, 0, 2, 3, 0, 2, 2, 0], [3, 2, 2, 2, 4, 0, 0, 0, 0, 4, 0, 3, 2, 2, 3, 2, 2, 3, 3, 2, 0], [2, 0, 2, 0, 4, 0, 0, 0, 0, 4, 2, 0, 0, 0, 2, 2, 2, 0, 2, 2, 2], [0, 2, 0, 2, 4, 4, 4, 4, 4, 4, 2, 2, 0, 2, 0, 2, 0, 0, 2, 2, 2], [2, 0, 2, 2, 2, 0, 2, 0, 2, 0, 3, 2, 3, 3, 0, 2, 0, 0, 0, 2, 2], [0, 2, 3, 0, 3, 0, 2, 3, 2, 2, 2, 0, 2, 0, 0, 0, 2, 2, 3, 2, 0], [3, 0, 2, 0, 2, 0, 0, 2, 2, 0, 3, 3, 2, 3, 0, 3, 3, 0, 0, 3, 0], [2, 3, 0, 3, 2, 2, 2, 2, 2, 0, 0, 0, 0, 2, 0, 2, 0, 3, 0, 0, 2], [3, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 2, 2, 2, 3, 0, 2, 2, 2, 2, 2], [3, 3, 3, 2, 0, 2, 0, 2, 0, 3, 2, 2, 2, 0, 0, 3, 2, 2, 3, 2, 2], [0, 0, 2, 2, 2, 3, 2, 0, 0, 2, 4, 4, 4, 4, 4, 4, 2, 3, 2, 2, 0], [2, 2, 2, 2, 2, 3, 2, 3, 3, 3, 4, 0, 0, 0, 0, 4, 0, 0, 2, 3, 0], [2, 2, 2, 2, 3, 0, 0, 3, 3, 2, 4, 0, 0, 0, 0, 4, 2, 2, 3, 2, 0], [2, 0, 3, 2, 2, 2, 3, 2, 3, 3, 4, 0, 0, 0, 0, 4, 2, 0, 0, 2, 3], [2, 2, 0, 0, 0, 0, 0, 0, 0, 3, 4, 4, 4, 4, 4, 4, 0, 2, 2, 0, 0], [0, 3, 0, 2, 2, 2, 0, 0, 0, 2, 2, 2, 2, 3, 0, 2, 0, 0, 0, 3, 2], [2, 3, 2, 2, 2, 0, 0, 3, 2, 0, 3, 2, 0, 2, 2, 2, 3, 0, 0, 2, 2]]}], "test": [{"input": [[0, 2, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 1, 2, 0, 1, 1, 1, 0, 1, 2], [1, 1, 1, 0, 2, 1, 2, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 2, 1, 1], [1, 1, 1, 0, 2, 2, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 2, 1, 1], [2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 2, 0, 1, 1, 1, 1], [0, 2, 1, 0, 1, 1, 2, 2, 1, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1, 2], [1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 2, 0], [0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 1], [1, 1, 1, 2, 2, 1, 0, 1, 2, 2, 1, 1, 2, 0, 0, 1, 0, 1, 1, 1, 2], [1, 0, 1, 0, 1, 0, 0, 2, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0], [0, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1], [0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0], [0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 1, 1, 1, 1, 1], [1, 3, 3, 3, 3, 1, 2, 0, 2, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1], [2, 3, 0, 0, 3, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [1, 3, 0, 0, 3, 1, 1, 2, 0, 1, 1, 1, 0, 2, 1, 1, 1, 0, 1, 1, 1], [1, 3, 0, 0, 3, 1, 2, 0, 0, 0, 1, 2, 1, 1, 1, 2, 1, 0, 1, 0, 1], [1, 3, 0, 0, 3, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [0, 3, 0, 0, 3, 1, 0, 2, 0, 1, 1, 1, 1, 0, 1, 1, 0, 2, 1, 1, 1], [1, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0], [1, 1, 1, 2, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1]], "output": [[0, 2, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 1, 2, 0, 1, 1, 1, 0, 1, 2], [1, 1, 1, 0, 2, 1, 2, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 2, 1, 1], [1, 1, 1, 0, 2, 2, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 2, 1, 1], [2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 3, 3, 3, 3, 0, 1, 1, 1, 1], [0, 2, 1, 0, 1, 1, 2, 2, 1, 1, 0, 1, 3, 0, 0, 3, 0, 1, 1, 1, 2], [1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 3, 0, 0, 3, 1, 0, 0, 2, 0], [0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 3, 0, 0, 3, 1, 0, 1, 1, 1], [1, 1, 1, 2, 2, 1, 0, 1, 2, 2, 1, 1, 3, 0, 0, 3, 0, 1, 1, 1, 2], [1, 0, 1, 0, 1, 0, 0, 2, 1, 1, 1, 0, 3, 0, 0, 3, 1, 1, 0, 1, 0], [0, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 3, 3, 3, 3, 0, 1, 1, 0, 1], [0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0], [0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 1, 1, 1, 1, 1], [1, 3, 3, 3, 3, 1, 2, 0, 2, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1], [2, 3, 0, 0, 3, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], [1, 3, 0, 0, 3, 1, 1, 2, 0, 1, 1, 1, 0, 2, 1, 1, 1, 0, 1, 1, 1], [1, 3, 0, 0, 3, 1, 2, 0, 0, 0, 1, 2, 1, 1, 1, 2, 1, 0, 1, 0, 1], [1, 3, 0, 0, 3, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1], [0, 3, 0, 0, 3, 1, 0, 2, 0, 1, 1, 1, 1, 0, 1, 1, 0, 2, 1, 1, 1], [1, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0], [1, 1, 1, 2, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1]]}]}