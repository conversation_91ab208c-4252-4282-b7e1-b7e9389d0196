{"train": [{"input": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1, 0]], "output": [[8, 1], [1, 8], [8, 1], [1, 8], [8, 1], [1, 8], [8, 1], [1, 8], [8, 1], [1, 8]]}, {"input": [[0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1, 0, 0]], "output": [[8, 1, 8], [1, 8, 8], [8, 1, 8], [8, 8, 1], [8, 1, 8], [1, 8, 8], [8, 1, 8], [8, 8, 1], [8, 1, 8], [1, 8, 8]]}, {"input": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [1, 0, 0, 0]], "output": [[8, 8, 8, 1], [8, 8, 1, 8], [8, 1, 8, 8], [1, 8, 8, 8], [8, 1, 8, 8], [8, 8, 1, 8], [8, 8, 8, 1], [8, 8, 1, 8], [8, 1, 8, 8], [1, 8, 8, 8]]}], "test": [{"input": [[0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1, 0, 0, 0, 0]], "output": [[8, 1, 8, 8, 8], [1, 8, 8, 8, 8], [8, 1, 8, 8, 8], [8, 8, 1, 8, 8], [8, 8, 8, 1, 8], [8, 8, 8, 8, 1], [8, 8, 8, 1, 8], [8, 8, 1, 8, 8], [8, 1, 8, 8, 8], [1, 8, 8, 8, 8]]}]}