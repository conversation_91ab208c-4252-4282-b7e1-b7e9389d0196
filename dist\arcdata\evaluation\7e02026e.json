{"train": [{"input": [[8, 0, 0, 8, 0, 0, 0, 8, 8, 0, 8, 0], [8, 0, 8, 0, 0, 0, 8, 0, 0, 8, 0, 0], [0, 0, 0, 8, 0, 8, 8, 8, 8, 8, 0, 8], [0, 8, 0, 8, 0, 0, 8, 0, 8, 8, 0, 0], [8, 0, 0, 8, 0, 0, 0, 8, 8, 8, 0, 0], [8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 0], [0, 8, 0, 0, 0, 8, 0, 8, 0, 8, 8, 0], [0, 8, 8, 8, 8, 0, 0, 8, 0, 0, 8, 8], [0, 8, 0, 8, 8, 8, 8, 0, 0, 8, 8, 0], [0, 8, 8, 8, 8, 0, 0, 0, 8, 0, 0, 8], [8, 0, 8, 0, 0, 0, 0, 0, 8, 8, 0, 0], [0, 8, 0, 8, 0, 8, 0, 8, 0, 0, 8, 0]], "output": [[8, 0, 0, 8, 3, 0, 0, 8, 8, 0, 8, 0], [8, 0, 8, 3, 3, 3, 8, 0, 0, 8, 0, 0], [0, 0, 0, 8, 3, 8, 8, 8, 8, 8, 0, 8], [0, 8, 0, 8, 0, 0, 8, 0, 8, 8, 0, 0], [8, 0, 0, 8, 0, 0, 0, 8, 8, 8, 0, 0], [8, 8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 0], [0, 8, 0, 0, 0, 8, 0, 8, 0, 8, 8, 0], [0, 8, 8, 8, 8, 0, 0, 8, 0, 0, 8, 8], [0, 8, 0, 8, 8, 8, 8, 0, 0, 8, 8, 0], [0, 8, 8, 8, 8, 0, 3, 0, 8, 0, 0, 8], [8, 0, 8, 0, 0, 3, 3, 3, 8, 8, 0, 0], [0, 8, 0, 8, 0, 8, 3, 8, 0, 0, 8, 0]]}, {"input": [[8, 0, 8, 8, 8, 8, 0, 8, 0, 8, 8, 8], [0, 8, 0, 0, 0, 8, 8, 0, 0, 0, 8, 8], [8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8], [0, 8, 0, 0, 8, 8, 0, 0, 0, 8, 0, 0], [8, 0, 8, 8, 0, 0, 8, 8, 0, 0, 8, 8], [8, 8, 8, 0, 8, 8, 0, 0, 8, 8, 8, 8], [8, 0, 8, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 8, 0, 8, 0, 8, 0, 0, 0, 8, 8, 0], [0, 8, 0, 8, 0, 0, 0, 8, 8, 0, 8, 8], [8, 8, 8, 8, 0, 0, 0, 0, 8, 0, 8, 0], [0, 8, 8, 0, 0, 0, 8, 8, 0, 0, 0, 0], [8, 0, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 0, 8, 8, 8, 8, 0, 8, 0, 8, 8, 8], [0, 8, 0, 0, 0, 8, 8, 0, 0, 0, 8, 8], [8, 0, 8, 0, 8, 8, 8, 8, 8, 8, 8, 8], [0, 8, 0, 0, 8, 8, 0, 0, 0, 8, 0, 0], [8, 0, 8, 8, 0, 0, 8, 8, 0, 0, 8, 8], [8, 8, 8, 0, 8, 8, 0, 0, 8, 8, 8, 8], [8, 0, 8, 0, 0, 0, 8, 0, 0, 0, 0, 0], [0, 8, 0, 8, 0, 8, 0, 0, 0, 8, 8, 0], [0, 8, 0, 8, 0, 3, 0, 8, 8, 0, 8, 8], [8, 8, 8, 8, 3, 3, 3, 0, 8, 0, 8, 0], [0, 8, 8, 3, 3, 3, 8, 8, 0, 0, 0, 0], [8, 0, 0, 8, 3, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[8, 8, 0, 0, 0, 8, 0, 0, 0, 0, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 8], [8, 8, 8, 0, 0, 8, 8, 0, 0, 0, 8, 8], [0, 8, 0, 8, 8, 8, 8, 0, 0, 8, 8, 8], [0, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0], [0, 0, 0, 8, 8, 0, 8, 0, 8, 8, 0, 0], [0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0], [8, 8, 8, 0, 8, 8, 8, 8, 0, 8, 0, 8], [8, 8, 0, 0, 0, 8, 8, 8, 0, 8, 8, 8], [8, 8, 0, 0, 0, 8, 0, 8, 8, 8, 8, 8], [8, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8], [8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 0, 8]], "output": [[8, 8, 0, 0, 0, 8, 0, 0, 0, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 3, 3, 3, 8], [8, 8, 8, 0, 0, 8, 8, 3, 3, 3, 8, 8], [0, 8, 0, 8, 8, 8, 8, 0, 3, 8, 8, 8], [0, 3, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0], [3, 3, 3, 8, 8, 0, 8, 0, 8, 8, 0, 0], [0, 3, 8, 8, 0, 8, 8, 0, 8, 8, 8, 0], [8, 8, 8, 3, 8, 8, 8, 8, 0, 8, 0, 8], [8, 8, 3, 3, 3, 8, 8, 8, 0, 8, 8, 8], [8, 8, 3, 3, 3, 8, 0, 8, 8, 8, 8, 8], [8, 0, 0, 3, 0, 8, 8, 8, 8, 8, 8, 8], [8, 0, 8, 8, 8, 8, 8, 0, 8, 8, 0, 8]]}], "test": [{"input": [[8, 0, 8, 8, 8, 8, 8, 0, 8, 0, 8, 0], [0, 8, 8, 8, 0, 0, 8, 0, 8, 0, 0, 0], [8, 8, 8, 8, 0, 0, 0, 8, 8, 8, 8, 8], [8, 0, 0, 0, 8, 0, 8, 8, 0, 0, 8, 0], [0, 8, 8, 8, 0, 8, 0, 8, 8, 0, 8, 8], [0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0], [8, 0, 8, 8, 0, 8, 8, 0, 8, 0, 0, 0], [0, 8, 0, 8, 0, 0, 8, 8, 8, 8, 8, 8], [0, 0, 0, 8, 8, 0, 0, 8, 0, 8, 0, 0], [0, 0, 0, 0, 8, 0, 8, 8, 0, 8, 8, 0], [0, 0, 0, 8, 8, 0, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 0, 0, 0, 0, 8, 8, 8]], "output": [[8, 0, 8, 8, 8, 8, 8, 0, 8, 0, 8, 0], [0, 8, 8, 8, 0, 3, 8, 0, 8, 0, 0, 0], [8, 8, 8, 8, 3, 3, 3, 8, 8, 8, 8, 8], [8, 0, 0, 0, 8, 3, 8, 8, 0, 0, 8, 0], [0, 8, 8, 8, 0, 8, 0, 8, 8, 3, 8, 8], [0, 0, 8, 8, 8, 0, 0, 0, 3, 3, 3, 0], [8, 0, 8, 8, 0, 8, 8, 0, 8, 3, 0, 0], [0, 8, 0, 8, 0, 0, 8, 8, 8, 8, 8, 8], [0, 3, 3, 8, 8, 0, 0, 8, 0, 8, 0, 0], [3, 3, 3, 3, 8, 0, 8, 8, 0, 8, 8, 0], [0, 3, 3, 8, 8, 0, 8, 8, 0, 8, 8, 8], [8, 8, 8, 0, 8, 0, 0, 0, 0, 8, 8, 8]]}]}