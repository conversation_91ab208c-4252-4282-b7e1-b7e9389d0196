{"train": [{"input": [[0, 0, 5, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 5, 5, 5, 5, 5, 5, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 5, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 0, 0, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 1, 1, 1, 0, 0], [0, 0, 0, 5, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 5, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 5, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 0, 0, 0, 5, 5, 5, 0, 0, 0], [0, 5, 0, 5, 5, 5, 5, 0, 0, 0], [0, 0, 0, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 5, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 1, 1, 5, 0, 0, 0], [0, 5, 0, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 5, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]}, {"input": [[0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 5, 0, 0, 0, 0, 0, 0, 5, 5], [0, 0, 0, 5, 5, 5, 5, 0, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 5, 0, 5, 5, 5, 0, 0, 0], [0, 5, 0, 0, 5, 5, 0, 0, 5, 0], [5, 0, 0, 0, 5, 5, 0, 0, 0, 5]], "output": [[0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0, 1, 5], [0, 0, 0, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 5, 0, 1, 1, 5, 0, 0, 0], [0, 1, 0, 0, 1, 1, 0, 0, 1, 0], [1, 0, 0, 0, 1, 1, 0, 0, 0, 1]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 0, 0, 5, 5, 0, 0, 5, 0], [0, 0, 0, 5, 0, 0, 5, 0, 0, 0], [0, 0, 5, 0, 0, 0, 0, 5, 0, 0], [0, 0, 0, 5, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [5, 0, 0, 0, 5, 5, 0, 0, 0, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0, 5]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 1, 1, 0, 0, 1, 0], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 5, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [1, 0, 0, 0, 1, 1, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 5, 0, 0, 0, 0, 0, 5]]}], "test": [{"input": [[0, 5, 0, 0, 0, 0, 0, 0, 5, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 5, 0, 5, 5, 5, 5, 0, 5, 0], [0, 0, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 5, 5, 0, 0], [0, 0, 5, 5, 5, 5, 5, 0, 0, 0]], "output": [[0, 1, 0, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 0, 0, 0, 5, 0, 0, 0, 0, 0], [0, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 1, 0, 1, 1, 1, 1, 0, 1, 0], [0, 0, 0, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 5, 0, 0], [0, 0, 5, 1, 1, 1, 1, 0, 0, 0]]}]}