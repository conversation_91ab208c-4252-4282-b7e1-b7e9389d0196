{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 2, 2, 2, 2, 2, 0, 2, 0, 0, 1, 2, 1, 0, 0, 0], [0, 0, 2, 0, 0, 2, 2, 2, 2, 0, 2, 0, 0, 2, 2, 2, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 2, 0, 0, 1, 2, 1, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 0, 0, 3, 3, 3, 0, 0], [0, 0, 2, 2, 2, 0, 0, 0, 2, 2, 2, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 2, 2, 2, 2, 0, 2, 2, 2, 2, 0, 0, 0, 3, 3, 3, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 8, 8, 8, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 0], [0, 0, 0, 4, 4, 4, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 2, 4, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 2, 2, 4, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 4, 4, 2, 2, 3, 2, 3], [2, 2, 2, 4, 2, 2, 3, 2, 3], [2, 4, 4, 4, 2, 2, 3, 2, 3], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 1, 2, 1, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 1, 2, 1, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [8, 2, 2, 2, 2, 5, 2, 5, 2], [8, 8, 2, 2, 2, 5, 2, 5, 2], [8, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 0, 2, 2, 2, 2, 2, 0], [0, 2, 0, 0, 0, 2, 2, 2, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 2, 2, 2, 2, 0, 0, 2, 2, 0], [0, 2, 2, 2, 2, 0, 2, 0, 2, 0], [0, 2, 2, 2, 2, 2, 0, 0, 2, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 2, 4, 0, 0, 0, 0, 0, 0], [0, 2, 2, 4, 0, 0, 0, 0, 0, 0], [0, 4, 2, 4, 0, 2, 2, 3, 0, 0], [0, 0, 0, 0, 0, 2, 3, 2, 0, 0], [0, 0, 0, 0, 0, 3, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2], [2, 4, 2, 4, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2], [2, 4, 4, 4, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 3, 2], [2, 2, 2, 2, 2, 3, 2, 2], [2, 2, 2, 2, 3, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 2, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], [0, 0, 0, 0, 0, 8, 2, 8, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 0, 0, 0, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 0, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 8, 2, 8, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 8, 2, 8, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2]]}], "test": [{"input": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 0, 2, 2, 2, 2, 0, 2, 2, 2, 0, 2, 0], [0, 0, 2, 0, 0, 2, 2, 0, 0, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 0, 0, 2, 2, 0, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 2, 2, 0], [0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 0, 0, 0, 0, 2, 3, 3, 0, 0, 0, 0], [0, 1, 2, 1, 0, 0, 0, 0, 2, 2, 3, 0, 0, 0, 0], [0, 1, 1, 1, 0, 0, 0, 0, 2, 3, 3, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 2, 0], [0, 0, 0, 4, 2, 4, 0, 0, 0, 0, 0, 8, 2, 2, 0], [0, 0, 0, 4, 2, 4, 0, 0, 0, 0, 0, 2, 2, 8, 0], [0, 0, 0, 4, 2, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "output": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1], [2, 2, 8, 8, 3, 3, 2, 2, 2, 1, 2, 1], [2, 2, 2, 8, 3, 2, 2, 2, 2, 1, 1, 1], [2, 8, 2, 2, 3, 3, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 4, 4, 4, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 2, 2, 2, 2, 2, 4, 4, 4, 2, 2]]}]}