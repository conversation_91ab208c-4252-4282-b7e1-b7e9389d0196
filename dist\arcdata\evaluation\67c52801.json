{"train": [{"input": [[0, 0, 0, 0, 0, 0], [0, 2, 2, 0, 0, 0], [0, 0, 0, 0, 3, 3], [0, 0, 0, 0, 3, 3], [0, 0, 0, 0, 0, 0], [1, 0, 1, 0, 0, 1], [1, 1, 1, 1, 1, 1]], "output": [[0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 2, 0, 3, 3, 0], [1, 2, 1, 3, 3, 1], [1, 1, 1, 1, 1, 1]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0], [2, 2, 2, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 5, 5, 0], [0, 0, 0, 0, 0, 5, 5, 0], [0, 0, 0, 0, 0, 0, 0, 0], [8, 0, 0, 8, 0, 0, 0, 8], [8, 8, 8, 8, 8, 8, 8, 8]], "output": [[0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 2, 2, 2, 0], [8, 5, 5, 8, 2, 2, 2, 8], [8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[0, 0, 8, 8, 0], [0, 0, 0, 0, 0], [3, 0, 3, 3, 3], [3, 3, 3, 3, 3]], "output": [[0, 0, 0, 0, 0], [0, 8, 0, 0, 0], [3, 8, 3, 3, 3], [3, 3, 3, 3, 3]]}, {"input": [[0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0], [6, 6, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 0, 6, 6, 0, 0, 7, 7, 7, 0, 0], [1, 5, 1, 6, 6, 1, 1, 7, 7, 7, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}], "test": [{"input": [[2, 2, 0, 6, 6, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 0, 0, 0, 0, 0, 1, 1, 0, 0], [2, 2, 0, 0, 0, 0, 0, 1, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], "output": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 6, 0, 1, 1, 0, 2, 2, 2, 2, 0], [5, 6, 5, 1, 1, 5, 2, 2, 2, 2, 5], [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]]}]}