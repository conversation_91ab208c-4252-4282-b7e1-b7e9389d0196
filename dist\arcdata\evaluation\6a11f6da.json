{"test": [{"input": [[1, 0, 1, 1, 1], [1, 0, 1, 0, 0], [0, 1, 1, 0, 0], [0, 1, 1, 1, 0], [1, 0, 1, 0, 1], [0, 0, 8, 0, 0], [0, 0, 8, 8, 8], [8, 8, 0, 8, 8], [0, 0, 8, 0, 0], [8, 8, 0, 8, 0], [0, 6, 0, 6, 0], [0, 0, 6, 0, 6], [0, 6, 0, 0, 6], [0, 0, 6, 0, 6], [6, 0, 6, 6, 0]], "output": [[1, 6, 1, 6, 1], [1, 0, 6, 8, 6], [8, 6, 1, 8, 6], [0, 1, 6, 1, 6], [6, 8, 6, 6, 1]]}], "train": [{"input": [[1, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 1, 0, 1, 0], [1, 1, 1, 0, 1], [0, 0, 0, 1, 0], [8, 0, 8, 0, 0], [8, 0, 0, 8, 0], [8, 0, 0, 0, 8], [8, 8, 0, 0, 0], [8, 8, 0, 0, 0], [0, 6, 0, 0, 6], [6, 0, 0, 6, 6], [0, 6, 6, 6, 0], [6, 6, 0, 6, 6], [0, 0, 6, 0, 6]], "output": [[1, 6, 8, 0, 6], [6, 1, 1, 6, 6], [8, 6, 6, 6, 8], [6, 6, 1, 6, 6], [8, 8, 6, 1, 6]]}, {"input": [[1, 0, 1, 0, 1], [0, 1, 0, 0, 1], [0, 1, 0, 0, 0], [1, 0, 0, 1, 1], [1, 0, 0, 1, 1], [0, 0, 0, 0, 0], [0, 8, 8, 8, 0], [0, 8, 0, 0, 0], [8, 0, 0, 0, 8], [8, 0, 8, 8, 0], [0, 0, 6, 0, 6], [6, 0, 6, 0, 0], [6, 0, 0, 0, 6], [6, 0, 0, 0, 6], [0, 6, 6, 6, 6]], "output": [[1, 0, 6, 0, 6], [6, 1, 6, 8, 1], [6, 1, 0, 0, 6], [6, 0, 0, 1, 6], [1, 6, 6, 6, 6]]}, {"input": [[0, 0, 1, 1, 0], [1, 1, 1, 0, 0], [0, 1, 1, 1, 0], [0, 1, 0, 0, 1], [1, 0, 0, 1, 1], [8, 0, 8, 8, 0], [8, 0, 8, 8, 8], [8, 8, 8, 0, 8], [0, 8, 0, 8, 8], [8, 0, 8, 8, 8], [6, 0, 6, 0, 6], [0, 0, 0, 0, 6], [6, 6, 6, 6, 6], [0, 0, 6, 0, 0], [0, 6, 0, 6, 0]], "output": [[6, 0, 6, 1, 6], [1, 1, 1, 8, 6], [6, 6, 6, 6, 6], [0, 1, 6, 8, 1], [1, 6, 8, 6, 1]]}, {"input": [[0, 1, 1, 1, 1], [0, 1, 1, 0, 0], [0, 1, 1, 1, 0], [0, 0, 1, 1, 1], [0, 1, 1, 1, 0], [0, 8, 8, 0, 0], [8, 0, 0, 8, 0], [0, 8, 0, 0, 8], [0, 0, 8, 0, 0], [8, 0, 8, 0, 8], [0, 6, 0, 6, 6], [0, 0, 6, 6, 6], [0, 6, 0, 0, 0], [0, 6, 6, 0, 6], [0, 0, 0, 0, 0]], "output": [[0, 6, 1, 6, 6], [8, 1, 6, 6, 6], [0, 6, 1, 1, 8], [0, 6, 6, 1, 6], [8, 1, 1, 1, 8]]}, {"input": [[1, 1, 1, 0, 0], [0, 0, 1, 1, 0], [1, 1, 0, 0, 1], [0, 1, 1, 1, 1], [0, 0, 0, 0, 1], [0, 8, 0, 0, 8], [8, 8, 8, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 8, 0], [0, 0, 8, 8, 8], [6, 6, 0, 0, 0], [0, 6, 6, 6, 0], [0, 0, 6, 0, 6], [0, 0, 6, 6, 6], [6, 6, 6, 6, 6]], "output": [[6, 6, 1, 0, 8], [8, 6, 6, 6, 0], [1, 1, 6, 0, 6], [0, 1, 6, 6, 6], [6, 6, 6, 6, 6]]}]}