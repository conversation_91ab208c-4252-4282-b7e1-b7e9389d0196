{"train": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8], [8, 8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8], [8, 8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8], [8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8, 8], [8, 8, 3, 3, 3, 8, 8, 3, 3, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 6, 8, 8, 8, 8, 8, 8, 8], [8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8, 8], [8, 6, 6, 6, 6, 6, 6, 6, 8, 8, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 8, 8], [8, 8, 8, 3, 3, 3, 8, 8, 3, 3, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}, {"input": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 4, 1, 1, 1, 4, 4, 4, 1, 1, 1], [1, 1, 4, 1, 1, 1, 4, 4, 4, 1, 1, 1], [1, 1, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1], [1, 1, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1], [1, 1, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1], [1, 1, 2, 2, 2, 2, 1, 1, 2, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], "output": [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 4, 1, 1, 1, 4, 4, 4, 1, 1, 1, 1], [1, 4, 1, 1, 1, 4, 4, 4, 1, 1, 1, 1], [1, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1], [1, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1], [1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 1, 1], [1, 1, 1, 2, 2, 2, 2, 1, 1, 2, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]}, {"input": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 7, 7, 7, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3], [3, 8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3], [3, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3, 3, 3], [3, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3, 3, 3]], "output": [[3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 2, 2, 2, 3, 3, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 7, 7, 7, 3, 3], [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3], [8, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 3, 3], [3, 3, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3, 3], [3, 3, 6, 6, 6, 6, 3, 3, 3, 3, 3, 3, 3]]}], "test": [{"input": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8], [8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 4, 4, 4, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 4, 8, 4, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], "output": [[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 8, 8, 1, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8], [8, 1, 1, 1, 1, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 2, 2, 2, 2, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 2, 2, 2, 8, 8, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 4, 4, 4, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 4, 8, 4, 8, 8, 8], [8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]}]}