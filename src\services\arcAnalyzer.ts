import {
  ARCPuzzle,
  ARCGrid,
  PuzzleAnalysis,
  Level0Data,
  SingleGridAnalysis,
  GeometricDetection,
  StructuralClassification,
  ObjectsAnalysis,
  ColorsAnalysis,
  SymmetriesAnalysis,
  PatternsAnalysis,
  BlockAnalysis,
  MosaicsAnalysis
} from '../types';

/**
 * ARC Analyzer Service - Implements the 4-level pure architecture
 * Level 0: Raw Data
 * Level 1: Domain-specific analyses (Objects, Patterns, Separations, etc.)
 * Level 2: Input/Output comparisons
 * Level 3: Training synthesis
 */

// Types locaux pour la transformation (pas encore dans types/index.ts)





export class ARCAnalyzer {
  /**
   * Level 0: Extract raw data from ARC grids
   */
  private extractLevel0Data(input: ARCGrid, output: ARCGrid): Level0Data {
    const getValueFrequencies = (grid: number[][]): Record<number, number> => {
      const frequencies: Record<number, number> = {};
      for (const row of grid) {
        for (const value of row) {
          frequencies[value] = (frequencies[value] || 0) + 1;
        }
      }
      return frequencies;
    };

    return {
      input_grid: {
        grid_array: input.grid,
        width: input.width,
        height: input.height,
        value_frequencies: getValueFrequencies(input.grid),
      },
      output_grid: {
        grid_array: output.grid,
        width: output.width,
        height: output.height,
        value_frequencies: getValueFrequencies(output.grid),
      },
    };
  }

  /**
   * Level 1A: Geometric Detection
   */
  private analyzeGeometricDetection(grid: number[][]): GeometricDetection {
    const height = grid.length;
    const width = grid[0]?.length || 0;

    const uniform_rows: Array<{ index: number; color: number; length: number }> = [];
    const uniform_columns: Array<{ index: number; color: number; length: number }> = [];

    // Detect uniform rows
    for (let i = 0; i < height; i++) {
      const row = grid[i];
      const firstColor = row[0];
      if (row.every(cell => cell === firstColor)) {
        uniform_rows.push({ index: i, color: firstColor, length: width });
      }
    }

    // Detect uniform columns
    for (let j = 0; j < width; j++) {
      const column = grid.map(row => row[j]);
      const firstColor = column[0];
      if (column.every(cell => cell === firstColor)) {
        uniform_columns.push({ index: j, color: firstColor, length: height });
      }
    }

    const color_frequency_in_lines: Record<number, number> = {};
    [...uniform_rows, ...uniform_columns].forEach(line => {
      color_frequency_in_lines[line.color] = (color_frequency_in_lines[line.color] || 0) + 1;
    });

    return {
      uniform_rows,
      uniform_columns,
      uniform_main_diagonals: [], // Simplified for now
      uniform_anti_diagonals: [], // Simplified for now
      line_distribution: {
        rows: uniform_rows.length,
        columns: uniform_columns.length,
        main_diagonals: 0,
        anti_diagonals: 0,
      },
      color_frequency_in_lines,
    };
  }

  /**
   * Level 1B: Structural Classification
   */
  private analyzeStructuralClassification(
    grid: number[][],
    geometric: GeometricDetection
  ): StructuralClassification {
    const height = grid.length;
    const width = grid[0]?.length || 0;

    // Detect borders
    const top_border = geometric.uniform_rows.find(row => row.index === 0);
    const bottom_border = geometric.uniform_rows.find(row => row.index === height - 1);
    const left_border = geometric.uniform_columns.find(col => col.index === 0);
    const right_border = geometric.uniform_columns.find(col => col.index === width - 1);

    const borders = {
      top_border: {
        exists: !!top_border,
        row: top_border?.index || -1,
        color: top_border?.color || -1,
        complete: !!top_border,
      },
      bottom_border: {
        exists: !!bottom_border,
        row: bottom_border?.index || -1,
        color: bottom_border?.color || -1,
        complete: !!bottom_border,
      },
      left_border: {
        exists: !!left_border,
        column: left_border?.index || -1,
        color: left_border?.color || -1,
        complete: !!left_border,
      },
      right_border: {
        exists: !!right_border,
        column: right_border?.index || -1,
        color: right_border?.color || -1,
        complete: !!right_border,
      },
      border_completeness: (top_border && bottom_border && left_border && right_border)
        ? 'complete' as const
        : (top_border || bottom_border || left_border || right_border)
          ? 'partial' as const
          : 'none' as const,
    };

    // Detect separators (lines that could divide the grid)
    const separators = [
      ...geometric.uniform_rows
        .filter(row => row.index > 0 && row.index < height - 1)
        .map((row) => ({
          separator_id: `horizontal_${row.index}`,
          type: 'horizontal' as const,
          position: row.index,
          color: row.color,
          spans_full_dimension: true,
          thickness: 1,
          divides_into_blocks: true,
          top_block_height: row.index,
          bottom_block_height: height - row.index - 1,
        })),
      ...geometric.uniform_columns
        .filter(col => col.index > 0 && col.index < width - 1)
        .map((col) => ({
          separator_id: `vertical_${col.index}`,
          type: 'vertical' as const,
          position: col.index,
          color: col.color,
          spans_full_dimension: true,
          thickness: 1,
          divides_into_blocks: true,
          left_block_width: col.index,
          right_block_width: width - col.index - 1,
        })),
    ];

    return {
      borders,
      separators,
      grid_lines: {
        has_grid_structure: separators.length > 1,
        vertical_grid_lines: separators.filter(s => s.type === 'vertical').map(s => s.position),
        horizontal_grid_lines: separators.filter(s => s.type === 'horizontal').map(s => s.position),
        creates_regular_blocks: separators.length >= 2,
        block_dimensions: [2, 2], // Simplified
        grid_size: [2, 2], // Simplified
      },
      structural_roles: {
        containment_lines: [
          ...(borders.top_border.exists ? [0] : []),
          ...(borders.bottom_border.exists ? [height - 1] : []),
          ...(borders.left_border.exists ? [0] : []),
          ...(borders.right_border.exists ? [width - 1] : []),
        ],
        division_lines: separators.map(s => s.position),
        decoration_lines: [], // Simplified for now
        functional_line_ratio: (separators.length +
          (borders.top_border.exists ? 1 : 0) +
          (borders.bottom_border.exists ? 1 : 0) +
          (borders.left_border.exists ? 1 : 0) +
          (borders.right_border.exists ? 1 : 0)
        ) / (geometric.uniform_rows.length + geometric.uniform_columns.length || 1),
      },
    };
  }

  /**
   * Level 1C: Block Analysis
   */
  private analyzeBlocks(
    grid: number[][],
    structural: StructuralClassification,
    _valueFrequencies: Record<number, number>
  ): BlockAnalysis {
    const height = grid.length;
    const width = grid[0]?.length || 0;
    const detected_blocks: BlockAnalysis['detected_blocks'] = [];

    // If we have separators, use them to define blocks
    if (structural.separators.length > 0) {
      const verticalSeps = structural.separators
        .filter(s => s.type === 'vertical')
        .map(s => s.position)
        .sort((a, b) => a - b);
      const horizontalSeps = structural.separators
        .filter(s => s.type === 'horizontal')
        .map(s => s.position)
        .sort((a, b) => a - b);

      // Add boundaries
      const vBounds = [0, ...verticalSeps, width];
      const hBounds = [0, ...horizontalSeps, height];

      // Create blocks from grid divisions
      for (let i = 0; i < hBounds.length - 1; i++) {
        for (let j = 0; j < vBounds.length - 1; j++) {
          const top = hBounds[i];
          const bottom = hBounds[i + 1];
          const left = vBounds[j];
          const right = vBounds[j + 1];

          // Skip if separator lines are included
          const actualTop = top + (i > 0 ? 1 : 0);
          const actualBottom = bottom - (i < hBounds.length - 2 ? 1 : 0);
          const actualLeft = left + (j > 0 ? 1 : 0);
          const actualRight = right - (j < vBounds.length - 2 ? 1 : 0);

          if (actualTop < actualBottom && actualLeft < actualRight) {
            const block = this.extractBlock(grid, actualTop, actualLeft, actualBottom, actualRight, i, j);
            detected_blocks.push(block);
          }
        }
      }
    } else {
      // No separators, treat entire grid as one block
      const block = this.extractBlock(grid, 0, 0, height, width, 0, 0);
      detected_blocks.push(block);
    }

    // Analyze block uniformity
    const all_same_size = detected_blocks.length > 1 && 
      detected_blocks.every(block => 
        block.dimensions[0] === detected_blocks[0].dimensions[0] && 
        block.dimensions[1] === detected_blocks[0].dimensions[1]
      );

    const uniform_dimensions = all_same_size ? detected_blocks[0]?.dimensions : undefined;

    // Analyze content patterns
    const block_hashes = detected_blocks.map(block => block.block_pattern_hash);
    const unique_hashes = [...new Set(block_hashes)];
    
    const identical_blocks: string[][] = [];
    const similar_blocks: string[][] = [];
    const unique_blocks: string[] = [];

    unique_hashes.forEach(hash => {
      const blocks_with_hash = detected_blocks
        .filter(block => block.block_pattern_hash === hash)
        .map(block => block.block_id);
      
      if (blocks_with_hash.length > 1) {
        identical_blocks.push(blocks_with_hash);
      } else {
        unique_blocks.push(blocks_with_hash[0]);
      }
    });

    // Background consistency
    const background_colors = detected_blocks.map(block => block.block_color_analysis.probable_background);
    const common_background = background_colors.filter(bg => bg !== undefined)[0];
    const same_background_across_blocks = background_colors.every(bg => bg === common_background);

    return {
      detected_blocks,
      block_uniformity: {
        all_same_size,
        uniform_dimensions,
        size_variations: [], // Simplified
        total_blocks: detected_blocks.length,
      },
      block_content_patterns: {
        uniform_blocks: detected_blocks.filter(block => block.block_uniformity.is_uniform).map(block => block.block_id),
        patterned_blocks: detected_blocks.filter(block => block.block_uniformity.has_internal_pattern).map(block => block.block_id),
        diverse_blocks: detected_blocks.filter(block => !block.block_uniformity.is_uniform && !block.block_uniformity.has_internal_pattern).map(block => block.block_id),
        background_consistency: {
          same_background_across_blocks,
          common_background_color: common_background,
          background_variations: background_colors.reduce((acc, bg) => {
            if (bg !== undefined) {
              acc[bg.toString()] = (acc[bg.toString()] || 0) + 1;
            }
            return acc;
          }, {} as Record<string, number>),
        },
        content_similarity: {
          identical_blocks,
          similar_blocks,
          unique_blocks,
        },
      },
    };
  }

  private extractBlock(
    grid: number[][],
    top: number,
    left: number,
    bottom: number,
    right: number,
    blockI: number,
    blockJ: number
  ): BlockAnalysis['detected_blocks'][0] {
    const block_width = right - left;
    const block_height = bottom - top;
    const block_grid_array: number[][] = [];

    // Extract block grid
    for (let i = top; i < bottom; i++) {
      const row: number[] = [];
      for (let j = left; j < right; j++) {
        row.push(grid[i]?.[j] || 0);
      }
      block_grid_array.push(row);
    }

    // Calculate value frequencies
    const value_frequencies: Record<number, number> = {};
    for (const row of block_grid_array) {
      for (const value of row) {
        value_frequencies[value] = (value_frequencies[value] || 0) + 1;
      }
    }

    const total_pixels = block_width * block_height;
    const present_colors = Object.keys(value_frequencies).map(k => parseInt(k));
    const dominant_color = Object.entries(value_frequencies)
      .reduce((a, b) => value_frequencies[parseInt(a[0])] > value_frequencies[parseInt(b[0])] ? a : b)[0];
    const probable_background = parseInt(dominant_color);

    // Check uniformity
    const is_uniform = present_colors.length === 1;
    const uniform_color = is_uniform ? present_colors[0] : undefined;
    const uniformity_ratio = is_uniform ? 1 : (value_frequencies[probable_background] || 0) / total_pixels;

    // Simple pattern detection
    const has_internal_pattern = !is_uniform && present_colors.length > 2;
    const pattern_complexity = has_internal_pattern ? 
      (present_colors.length > 4 ? 'complex' : 'simple') as 'none' | 'simple' | 'complex' : 'none';

    // Calculate symmetries
    const horizontal = block_grid_array.every((row, i) =>
      row.every((cell, j) => cell === block_grid_array[block_height - 1 - i]?.[j])
    );
    const vertical = block_grid_array.every(row =>
      row.every((cell, j) => cell === row[block_width - 1 - j])
    );
    const diagonal_main = block_width === block_height && block_grid_array.every((row, i) =>
      row.every((cell, j) => cell === block_grid_array[j]?.[i])
    );
    const diagonal_anti = block_width === block_height && block_grid_array.every((row, i) =>
      row.every((cell, j) => cell === block_grid_array[block_width - 1 - j]?.[block_height - 1 - i])
    );

    const symmetry_count = [horizontal, vertical, diagonal_main, diagonal_anti].filter(Boolean).length;

    return {
      block_id: `block_${blockI}_${blockJ}`,
      bounds: { top, left, bottom, right },
      dimensions: [block_width, block_height],
      area: total_pixels,
      separated_by: [], // Simplified
      block_grid_array,
      block_statistics: {
        value_frequencies,
        width: block_width,
        height: block_height,
        total_pixels,
      },
      block_color_analysis: {
        present_colors,
        dominant_color: probable_background,
        probable_background,
        non_background_colors: present_colors.filter(c => c !== probable_background),
        color_diversity: present_colors.length,
        background_ratio: (value_frequencies[probable_background] || 0) / total_pixels,
      },
      block_uniformity: {
        is_uniform,
        uniform_color,
        uniformity_ratio,
        has_internal_pattern,
        pattern_complexity,
      },
      block_symmetries: {
        horizontal,
        vertical,
        diagonal_main,
        diagonal_anti,
        symmetry_count,
      },
      block_pattern_hash: JSON.stringify(block_grid_array),
    };
  }

  /**
   * Level 1: Mosaics Analysis
   */
  private analyzeMosaics(grid: number[][], valueFrequencies: Record<number, number>): MosaicsAnalysis {
    const height = grid.length;
    const width = grid[0]?.length || 0;
    const total_pixels = height * width;

    // Size analysis
    const size_category = total_pixels > 400 ? 'very_large' : 
                         total_pixels > 200 ? 'large' : 
                         total_pixels > 100 ? 'medium' : 'small';
    const exceeds_average = total_pixels > 150; // Arbitrary threshold

    // Color balance analysis
    const present_colors = Object.keys(valueFrequencies).map(k => parseInt(k));
    const color_counts = Object.values(valueFrequencies);
    const max_count = Math.max(...color_counts);
    const dominant_color_ratio = max_count / total_pixels;
    
    // Calculate entropy for color balance
    const color_entropy = -color_counts.reduce((entropy, count) => {
      const p = count / total_pixels;
      return entropy + (p > 0 ? p * Math.log2(p) : 0);
    }, 0);
    
    const balance_score = color_entropy / Math.log2(present_colors.length || 1);
    const is_well_balanced = balance_score > 0.7 && dominant_color_ratio < 0.6;

    // Detect uniform zones (simplified)
    const uniform_zones: MosaicsAnalysis['mosaic_detection']['uniform_zones_detection']['uniform_zones'] = [];
    
    // Look for rectangular uniform areas
    for (let color of present_colors) {
      const positions: Array<[number, number]> = [];
      for (let i = 0; i < height; i++) {
        for (let j = 0; j < width; j++) {
          if (grid[i][j] === color) {
            positions.push([i, j]);
          }
        }
      }

      if (positions.length > 4) { // Only consider significant zones
        const minI = Math.min(...positions.map(p => p[0]));
        const maxI = Math.max(...positions.map(p => p[0]));
        const minJ = Math.min(...positions.map(p => p[1]));
        const maxJ = Math.max(...positions.map(p => p[1]));
        
        const zone_width = maxJ - minJ + 1;
        const zone_height = maxI - minI + 1;
        const expected_area = zone_width * zone_height;
        
        // Check if it's a uniform rectangular zone
        if (positions.length === expected_area) {
          uniform_zones.push({
            zone_id: `uniform_zone_${color}`,
            color,
            bounds: [minI, minJ, maxI, maxJ],
            area: positions.length,
            shape: zone_width === zone_height ? 'square' : 'rectangle',
            position: minI === 0 || maxI === height - 1 || minJ === 0 || maxJ === width - 1 ? 
                     (minI === 0 && minJ === 0) || (minI === 0 && maxJ === width - 1) || 
                     (maxI === height - 1 && minJ === 0) || (maxI === height - 1 && maxJ === width - 1) ? 'corner' : 'edge' : 'center',
          });
        }
      }
    }

    const uniform_coverage_ratio = uniform_zones.reduce((sum, zone) => sum + zone.area, 0) / total_pixels;

    // Mosaic detection logic
    const is_potential_mosaic = 
      size_category !== 'small' && 
      present_colors.length >= 3 && 
      is_well_balanced && 
      uniform_zones.length >= 2;

    const confidence_score = 
      (size_category === 'very_large' ? 0.3 : size_category === 'large' ? 0.2 : 0.1) +
      (present_colors.length >= 5 ? 0.3 : present_colors.length >= 3 ? 0.2 : 0.1) +
      (balance_score * 0.3) +
      (uniform_zones.length >= 3 ? 0.2 : uniform_zones.length >= 2 ? 0.1 : 0);

    const detection_reasons: string[] = [];
    if (size_category !== 'small') detection_reasons.push(`Taille ${size_category}`);
    if (present_colors.length >= 3) detection_reasons.push(`${present_colors.length} couleurs`);
    if (is_well_balanced) detection_reasons.push('Couleurs équilibrées');
    if (uniform_zones.length >= 2) detection_reasons.push(`${uniform_zones.length} zones uniformes`);

    return {
      mosaic_detection: {
        is_potential_mosaic,
        confidence_score: Math.min(confidence_score, 1),
        detection_reasons,
        size_analysis: {
          grid_dimensions: [width, height],
          total_pixels,
          size_category,
          exceeds_average,
        },
        color_balance_analysis: {
          color_entropy,
          balance_score,
          dominant_color_ratio,
          is_well_balanced,
        },
        uniform_zones_detection: {
          uniform_zones,
          total_uniform_zones: uniform_zones.length,
          uniform_coverage_ratio,
        },
      },
    };
  }

  /**
   * Level 1: Objects Analysis (Table view approach)
   */
  private analyzeObjects(grid: number[][], valueFrequencies: Record<number, number>): ObjectsAnalysis {
    // Detect background color (most frequent)
    const probable_background = Object.entries(valueFrequencies)
      .reduce((a, b) => valueFrequencies[parseInt(a[0])] > valueFrequencies[parseInt(b[0])] ? a : b)[0];

    const background_color = parseInt(probable_background);
    const background_count = valueFrequencies[background_color];
    const total_pixels = grid.length * (grid[0]?.length || 0);
    const background_confidence = background_count / total_pixels;

    const non_background_colors = Object.keys(valueFrequencies)
      .map(k => parseInt(k))
      .filter(color => color !== background_color);

    // Detect objects (simplified connected components)
    const detected_objects: ObjectsAnalysis['detected_objects'] = [];
    const visited = new Set<string>();

    for (let i = 0; i < grid.length; i++) {
      for (let j = 0; j < (grid[0]?.length || 0); j++) {
        const key = `${i},${j}`;
        if (!visited.has(key) && grid[i][j] !== background_color) {
          const object = this.extractObject(grid, i, j, background_color, visited);
          if (object.area > 0) {
            detected_objects.push(object);
          }
        }
      }
    }

    // Detect anchor points (red pixels, color 2)
    const possible_anchor_points: ObjectsAnalysis['anchor_analysis']['possible_anchor_points'] = [];
    for (let i = 0; i < grid.length; i++) {
      for (let j = 0; j < (grid[0]?.length || 0); j++) {
        if (grid[i][j] === 2) { // Red color as anchor
          possible_anchor_points.push({
            anchor_id: `anchor_${i}_${j}`,
            position: [i, j],
            size: 1,
            shape: 'single_pixel',
            nearby_objects: [], // Simplified
            location_type: this.getLocationTypeForPosition(i, j, grid.length, grid[0]?.length || 0),
          });
        }
      }
    }

    return {
      table_analysis: {
        probable_background: background_color,
        background_confidence,
        non_background_colors,
        table_coverage: background_confidence,
        noise_pixels: 0, // Simplified
      },
      detected_objects,
      anchor_analysis: {
        possible_anchor_points,
      },
      object_statistics: {
        total_objects: detected_objects.length,
        objects_by_color: detected_objects.reduce((acc, obj) => {
          acc[obj.color] = (acc[obj.color] || 0) + 1;
          return acc;
        }, {} as Record<number, number>),
        objects_by_shape: detected_objects.reduce((acc, obj) => {
          acc[obj.shape_classification.basic_shape] = (acc[obj.shape_classification.basic_shape] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        table_occupancy: 1 - background_confidence,
      },
    };
  }

  private extractObject(
    grid: number[][],
    startI: number,
    startJ: number,
    _backgroundColor: number,
    visited: Set<string>
  ): ObjectsAnalysis['detected_objects'][0] {
    const height = grid.length;
    const width = grid[0]?.length || 0;
    const color = grid[startI][startJ];
    const pixels: Array<[number, number]> = [];
    const queue: Array<[number, number]> = [[startI, startJ]];

    // Flood fill to find connected component
    while (queue.length > 0) {
      const [i, j] = queue.shift()!;
      const key = `${i},${j}`;

      if (visited.has(key) || i < 0 || i >= height || j < 0 || j >= width || grid[i][j] !== color) {
        continue;
      }

      visited.add(key);
      pixels.push([i, j]);

      // Add neighbors
      queue.push([i - 1, j], [i + 1, j], [i, j - 1], [i, j + 1]);
    }

    // Calculate properties
    const minI = Math.min(...pixels.map(p => p[0]));
    const maxI = Math.max(...pixels.map(p => p[0]));
    const minJ = Math.min(...pixels.map(p => p[1]));
    const maxJ = Math.max(...pixels.map(p => p[1]));

    const objWidth = maxJ - minJ + 1;
    const objHeight = maxI - minI + 1;
    const area = pixels.length;
    const centerI = pixels.reduce((sum, p) => sum + p[0], 0) / pixels.length;
    const centerJ = pixels.reduce((sum, p) => sum + p[1], 0) / pixels.length;

    // Classify shape (simplified)
    let basic_shape: ObjectsAnalysis['detected_objects'][0]['shape_classification']['basic_shape'] = 'irregular';
    if (objWidth === 1 && objHeight === 1) {
      basic_shape = 'dot';
    } else if (objWidth === 1 || objHeight === 1) {
      basic_shape = 'line';
    } else if (objWidth === objHeight && area === objWidth * objHeight) {
      basic_shape = 'square';
    } else if (area === objWidth * objHeight) {
      basic_shape = 'rectangle';
    }

    return {
      object_id: `obj_${color}_${basic_shape}_${pixels.length}`,
      color,
      multi_color: false,
      area,
      center: [centerI, centerJ],
      bbox: [minI, minJ, maxI, maxJ],
      width: objWidth,
      height: objHeight,
      aspect_ratio: objWidth / objHeight,
      shape_classification: {
        basic_shape,
        shape_confidence: 0.8,
        symmetries: {
          horizontal: false, // Simplified
          vertical: false,
          diagonal_main: false,
          diagonal_anti: false,
        },
      },
      object_hash: `${color}_${objWidth}x${objHeight}_${area}`,
      table_position: {
        absolute_position: [Math.round(centerI), Math.round(centerJ)],
        relative_position: this.getRelativePosition(centerI, centerJ, height, width),
        distance_to_edges: {
          top: Math.round(centerI),
          bottom: height - Math.round(centerI) - 1,
          left: Math.round(centerJ),
          right: width - Math.round(centerJ) - 1,
        },
        touching_edges: [],
      },
    };
  }

  private getLocationTypeForPosition(i: number, j: number, height: number, width: number): 'corner' | 'edge' | 'center' | 'isolated' {
    const isCorner = (i === 0 || i === height - 1) && (j === 0 || j === width - 1);
    const isEdge = i === 0 || i === height - 1 || j === 0 || j === width - 1;

    if (isCorner) return 'corner';
    if (isEdge) return 'edge';
    return 'center';
  }

  private getRelativePosition(centerI: number, centerJ: number, height: number, width: number): string {
    const verticalThird = height / 3;
    const horizontalThird = width / 3;

    const vertical = centerI < verticalThird ? 'top' : centerI > 2 * verticalThird ? 'bottom' : 'middle';
    const horizontal = centerJ < horizontalThird ? 'left' : centerJ > 2 * horizontalThird ? 'right' : 'center';

    return `${vertical}_${horizontal}`;
  }

  /**
   * Level 1: Colors Analysis
   */
  private analyzeColors(valueFrequencies: Record<number, number>): ColorsAnalysis {
    const present_colors = Object.keys(valueFrequencies).map(k => parseInt(k)).sort((a, b) => a - b);
    const dominant_color = Object.entries(valueFrequencies)
      .reduce((a, b) => valueFrequencies[parseInt(a[0])] > valueFrequencies[parseInt(b[0])] ? a : b)[0];

    return {
      present_colors,
      dominant_color: parseInt(dominant_color),
      background_color: parseInt(dominant_color), // Assume dominant is background
      non_background_colors: present_colors.filter(c => c !== parseInt(dominant_color)),
      color_count: present_colors.length,
      color_distribution: valueFrequencies,
    };
  }

  /**
   * Level 1: Symmetries Analysis
   */
  private analyzeSymmetries(grid: number[][]): SymmetriesAnalysis {
    const height = grid.length;
    const width = grid[0]?.length || 0;

    // Check horizontal symmetry
    const horizontal = grid.every((row, i) =>
      row.every((cell, j) => cell === grid[height - 1 - i][j])
    );

    // Check vertical symmetry
    const vertical = grid.every(row =>
      row.every((cell, j) => cell === row[width - 1 - j])
    );

    // Simplified diagonal checks
    const diagonal_main = width === height && grid.every((row, i) =>
      row.every((cell, j) => cell === grid[j][i])
    );

    const diagonal_anti = width === height && grid.every((row, i) =>
      row.every((cell, j) => cell === grid[width - 1 - j][height - 1 - i])
    );

    const symmetry_count = [horizontal, vertical, diagonal_main, diagonal_anti].filter(Boolean).length;

    return {
      horizontal,
      vertical,
      diagonal_main,
      diagonal_anti,
      symmetry_count,
      symmetry_axes: [], // Simplified
    };
  }

  /**
   * Level 1: Patterns Analysis
   */
  private analyzePatterns(grid: number[][]): PatternsAnalysis {
    // Simplified pattern detection
    const patterns = new Map<string, number>();
    const height = grid.length;
    const width = grid[0]?.length || 0;

    // Look for 2x2 patterns
    for (let i = 0; i < height - 1; i++) {
      for (let j = 0; j < width - 1; j++) {
        const pattern = [
          [grid[i][j], grid[i][j + 1]],
          [grid[i + 1][j], grid[i + 1][j + 1]]
        ];
        const hash = JSON.stringify(pattern);
        patterns.set(hash, (patterns.get(hash) || 0) + 1);
      }
    }

    const detected_patterns = Array.from(patterns.entries())
      .filter(([_, count]) => count > 1)
      .map(([hash, count], index) => ({
        pattern_id: `pattern_${index}`,
        pattern_size: [2, 2] as [number, number],
        pattern_hash: hash,
        occurrence_count: count,
        coverage_ratio: (count * 4) / (height * width),
        pattern_type: 'exact' as const,
      }));

    return {
      detected_patterns,
      pattern_statistics: {
        total_patterns: detected_patterns.length,
        pattern_density: detected_patterns.length / (height * width),
        pattern_coverage: detected_patterns.reduce((sum, p) => sum + p.coverage_ratio, 0),
      },
      complexity_analysis: {
        pattern_complexity: detected_patterns.length > 5 ? 'complex' : detected_patterns.length > 2 ? 'medium' : 'simple',
        regularity_index: detected_patterns.length > 0 ? 0.7 : 0.3,
        predictability: detected_patterns.length > 0 ? 0.8 : 0.2,
      },
    };
  }

  /**
   * Level 1: Complete analysis of a single grid
   */
  private analyzeLevel1Grid(gridData: Level0Data['input_grid']): SingleGridAnalysis {
    const { grid_array, value_frequencies } = gridData;

    // Sequential sub-levels
    const geometric_detection = this.analyzeGeometricDetection(grid_array);
    const structural_classification = this.analyzeStructuralClassification(grid_array, geometric_detection);
    const block_analysis = this.analyzeBlocks(grid_array, structural_classification, value_frequencies);

    // Parallel domains
    const objects = this.analyzeObjects(grid_array, value_frequencies);
    const colors = this.analyzeColors(value_frequencies);
    const symmetries = this.analyzeSymmetries(grid_array);
    const repeating_patterns = this.analyzePatterns(grid_array);
    const mosaics = this.analyzeMosaics(grid_array, value_frequencies);

    const total_pixels = grid_array.length * (grid_array[0]?.length || 0);
    const non_zero_pixels = Object.entries(value_frequencies)
      .filter(([color, _]) => parseInt(color) !== 0)
      .reduce((sum, [_, count]) => sum + count, 0);

    return {
      geometric_detection,
      structural_classification,
      block_analysis,
      objects,
      colors,
      symmetries,
      repeating_patterns,
      mosaics,
      spatial_properties: {
        object_density: objects.object_statistics.total_objects / total_pixels,
        background_ratio: objects.table_analysis.background_confidence,
        filled_ratio: non_zero_pixels / total_pixels,
        center_of_mass: [grid_array.length / 2, (grid_array[0]?.length || 0) / 2], // Simplified
        spatial_distribution: 'dispersed', // Simplified
      },
    };
  }

  /**
   * Transformation Analysis (diff grid calculation)
   */
  private analyzeTransformation(level0: Level0Data): any {
    const { input_grid, output_grid } = level0;

    const same_dimensions = input_grid.width === output_grid.width &&
      input_grid.height === output_grid.height;

    let diff_grid = null;
    let change_positions: Array<[number, number]> | null = null;
    let change_ratio: number | null = null;
    let diff_analysis = null;

    if (same_dimensions) {
      diff_grid = input_grid.grid_array.map((row, i) =>
        row.map((cell, j) => {
          const outputCell = output_grid.grid_array[i][j];
          return cell === outputCell ? 0 : outputCell; // 0 = no change, otherwise new value
        })
      );

      change_positions = [];
      let changes = 0;
      for (let i = 0; i < diff_grid.length; i++) {
        for (let j = 0; j < diff_grid[i].length; j++) {
          if (diff_grid[i][j] !== 0) {
            change_positions.push([i, j]);
            changes++;
          }
        }
      }
      change_ratio = changes / (input_grid.width * input_grid.height);

      // Analyse complète de la grille diff (même structure que input/output)
      if (changes > 0) {
        const diff_value_frequencies = this.calculateFrequencies(diff_grid);
        const diff_grid_data = {
          grid_array: diff_grid,
          width: input_grid.width,
          height: input_grid.height,
          value_frequencies: diff_value_frequencies
        };
        diff_analysis = this.analyzeLevel1Grid(diff_grid_data);
      }
    }

    return {
      dimension_compatibility: {
        same_dimensions,
        width_change: output_grid.width - input_grid.width,
        height_change: output_grid.height - input_grid.height,
        transformation_type: same_dimensions ? 'same_size' : 'resize',
      },
      diff_grid: {
        exists: same_dimensions,
        grid_array: diff_grid || undefined,
        change_ratio: change_ratio,
        change_positions: change_positions,
        diff_analysis: diff_analysis
      },
    };
  }

  /**
   * Calculate value frequencies for a grid
   */
  private calculateFrequencies(grid: number[][]): Record<number, number> {
    const frequencies: Record<number, number> = {};
    for (const row of grid) {
      for (const cell of row) {
        frequencies[cell] = (frequencies[cell] || 0) + 1;
      }
    }
    return frequencies;
  }

  /**
   * Main analysis method - implements the full 4-level architecture
   */
  public async analyzePuzzle(puzzle: ARCPuzzle): Promise<PuzzleAnalysis> {
    try {
      // Analyze all training examples and aggregate results
      if (!puzzle.train || puzzle.train.length === 0) {
        throw new Error('No training examples available');
      }

      // Analyze all training examples
      const trainingExamples = puzzle.train.map((example, index) => {
        // Level 0: Extract raw data
        const level0Data = this.extractLevel0Data(example.input, example.output);

        // Level 1: Domain analyses
        const input_analysis = this.analyzeLevel1Grid(level0Data.input_grid);
        const output_analysis = this.analyzeLevel1Grid(level0Data.output_grid);

        return {
          example_id: `train_${index}`,
          level0Data,
          input_analysis,
          output_analysis
        };
      });

      // For the main analysis, use the first example (but keep all examples available)
      const mainExample = trainingExamples[0];
      const { level0Data, input_analysis, output_analysis } = mainExample;

      // Level 2: Transformation analysis (moved from Level 1)
      const transformation_analysis = this.analyzeTransformation(level0Data);
      const trainingComparisons = trainingExamples.map(ex => ({
        example_id: ex.example_id,
        transformation_analysis: this.analyzeTransformation(ex.level0Data)
      }));

      // Build the new 4-level architecture
      const analysis: PuzzleAnalysis = {
        // Niveau 0 : Données Brutes (tous les exemples d'entraînement)
        level_0: {
          training_examples: trainingExamples.map(ex => ({
            example_id: ex.example_id,
            input_grid: ex.level0Data.input_grid,
            output_grid: ex.level0Data.output_grid
          })),
          // Garder la structure principale pour rétrocompatibilité
          input_grid: level0Data.input_grid,
          output_grid: level0Data.output_grid
        },

        // Niveau 1 : Analyses Dérivées par Domaine
        level_1: {
          // Analyses de tous les exemples d'entraînement
          training_examples_analysis: trainingExamples.map(ex => ({
            example_id: ex.example_id,
            input_analysis: ex.input_analysis,
            output_analysis: ex.output_analysis
          })),

          // Analyse principale (premier exemple pour rétrocompatibilité)
          input_analysis,
          output_analysis
        },

        // Niveau 2 : Comparaisons Input/Output
        level_2: {
          // Comparaisons par exemple d'entraînement
          training_examples_comparisons: trainingComparisons,

          // Analyse principale (premier exemple pour rétrocompatibilité)
          transformation_analysis
        },

        // Niveau 3 : Synthèse par Entraînement (placeholder)
        level_3: {
          recurring_patterns: {
            global_patterns: [],
            consistency_score: 0,
            pattern_conflicts: []
          },
          global_rules: {
            rule_hypotheses: [],
            best_rule_confidence: 0,
            rule_consistency: 0
          },
          predictions: {
            test_predictions: [],
            prediction_confidence: 0,
            alternative_predictions: []
          }
        },

        // Rétrocompatibilité (optionnelle, peut être supprimée plus tard)
        grid_info: {
          input: {
            width: level0Data.input_grid.width,
            height: level0Data.input_grid.height,
            colors: input_analysis.colors.present_colors,
            total_cells: level0Data.input_grid.width * level0Data.input_grid.height,
          },
          output: {
            width: level0Data.output_grid.width,
            height: level0Data.output_grid.height,
            colors: output_analysis.colors.present_colors,
            total_cells: level0Data.output_grid.width * level0Data.output_grid.height,
          },
        },
        objects: {
          input: input_analysis.objects,
          output: output_analysis.objects,
        },
        patterns: {
          input: input_analysis.repeating_patterns,
          output: output_analysis.repeating_patterns,
        },
        transformations: {
          dimension_change: {
            same_dimensions: transformation_analysis.dimension_compatibility.same_dimensions,
            width_change: transformation_analysis.dimension_compatibility.width_change,
            height_change: transformation_analysis.dimension_compatibility.height_change,
            transformation_type: transformation_analysis.dimension_compatibility.transformation_type || 'unknown',
          },
          object_changes: [],
          pattern_changes: [],
        },
        symmetries: {
          input: input_analysis.symmetries,
          output: output_analysis.symmetries,
        },
        complexity: {
          input: input_analysis.repeating_patterns.complexity_analysis,
          output: output_analysis.repeating_patterns.complexity_analysis,
          transformation_complexity: 'medium',
        },
        colors: {
          input: input_analysis.colors,
          output: output_analysis.colors,
        },
        spatial_relations: {
          input: input_analysis.spatial_properties,
          output: output_analysis.spatial_properties,
        },
        line_uniformity: {
          input: input_analysis.geometric_detection,
          output: output_analysis.geometric_detection,
        },
        diff_analysis: transformation_analysis.diff_grid.exists ? {
          change_ratio: transformation_analysis.diff_grid.change_ratio || 0,
          change_positions: transformation_analysis.diff_grid.change_positions || [],
          change_patterns: [],
        } : null,
        enhanced_objects: {
          input: input_analysis.objects.detected_objects,
          output: output_analysis.objects.detected_objects,
        },
        repeating_patterns: {
          input: input_analysis.repeating_patterns.detected_patterns,
          output: output_analysis.repeating_patterns.detected_patterns,
        },
      };

      return analysis;
    } catch (error) {
      console.error('Error analyzing puzzle:', error);
      throw error;
    }
  }
}

export const arcAnalyzer = new ARCAnalyzer();