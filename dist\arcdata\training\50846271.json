{"train": [{"input": [[0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0], [0, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 0, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 2, 5, 5, 5, 0, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 2, 5, 5, 0, 0, 5, 0, 5, 5, 0], [0, 5, 0, 0, 5, 0, 0, 0, 5, 2, 5, 2, 5, 5, 5, 2, 5, 0, 5, 0, 0, 0], [0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 2, 5, 0, 0, 5, 0, 0, 5, 5, 5], [0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 2, 5, 0, 5, 5, 0, 5, 0, 0, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 5, 0, 5], [5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 5, 0, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5], [0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 0, 5, 5], [0, 0, 5, 5, 0, 2, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0], [5, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 5], [0, 0, 2, 5, 5, 2, 2, 2, 2, 0, 0, 0, 5, 5, 0, 5, 0, 0, 5, 0, 5, 0], [0, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0], [5, 0, 0, 0, 5, 2, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0], [0, 0, 5, 5, 0, 2, 5, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 5], [0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 0, 0, 0, 5]], "output": [[0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0], [0, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 2, 0, 0, 0, 0, 0, 0, 0, 5, 0], [5, 0, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 2, 5, 5, 5, 0, 5, 5, 5, 0, 0], [0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 2, 5, 5, 0, 0, 5, 0, 5, 5, 0], [0, 5, 0, 0, 5, 0, 0, 0, 5, 2, 8, 2, 8, 8, 8, 2, 5, 0, 5, 0, 0, 0], [0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 2, 5, 0, 0, 5, 0, 0, 5, 5, 5], [0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 2, 5, 0, 5, 5, 0, 5, 0, 0, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 5, 0, 5], [5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 0, 0, 0, 0, 0, 5, 0, 0], [0, 5, 0, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5], [0, 5, 5, 0, 0, 8, 0, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 0, 5, 5], [0, 0, 5, 5, 0, 2, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0], [5, 0, 5, 0, 0, 8, 5, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 5], [0, 0, 2, 8, 8, 2, 2, 2, 2, 0, 0, 0, 5, 5, 0, 5, 0, 0, 5, 0, 5, 0], [0, 5, 5, 0, 0, 8, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0], [5, 0, 0, 0, 5, 2, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0], [0, 0, 5, 5, 0, 2, 5, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 5], [0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 5, 5], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 0, 0, 0, 5]]}, {"input": [[0, 5, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 5, 5], [5, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 0, 5, 5, 0], [0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 5], [5, 0, 0, 5, 5, 0, 2, 5, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5], [0, 5, 0, 5, 2, 5, 2, 2, 2, 0, 5, 5, 0, 5, 0, 5, 5, 0, 0, 0], [5, 5, 0, 0, 5, 5, 2, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5], [0, 0, 0, 5, 0, 5, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 5], [5, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 0, 5, 0, 5], [5, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 5, 5, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 2, 5, 2, 2, 2, 0, 0, 5], [0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5], [0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0], [5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0], [5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 5], [0, 0, 5, 0, 5, 5, 5, 2, 2, 5, 5, 0, 0, 5, 0, 0, 5, 5, 0, 0], [0, 5, 5, 0, 0, 5, 5, 2, 5, 0, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0], [0, 0, 5, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 0], [0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5]], "output": [[0, 5, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 5, 5], [5, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5, 0, 0, 5, 0, 0, 0, 5, 5, 0], [0, 5, 0, 5, 5, 0, 8, 5, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 5], [5, 0, 0, 5, 5, 0, 2, 5, 0, 5, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5], [0, 5, 0, 5, 2, 8, 2, 2, 2, 0, 5, 5, 0, 5, 0, 5, 5, 0, 0, 0], [5, 5, 0, 0, 5, 5, 2, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 0, 0, 0], [0, 0, 5, 5, 0, 0, 8, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 5], [0, 0, 0, 5, 0, 5, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0, 5], [5, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 8, 0, 0, 5, 0, 5], [5, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 0, 5, 0, 8, 5, 5, 5, 5, 5], [5, 0, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 2, 8, 2, 2, 2, 0, 0, 5], [0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 8, 0, 0, 5, 0, 5], [0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 5, 8, 5, 5, 5, 5, 0], [5, 5, 0, 0, 5, 5, 0, 8, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0], [5, 5, 0, 0, 0, 5, 5, 8, 0, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 5], [0, 0, 5, 0, 5, 8, 8, 2, 2, 8, 5, 0, 0, 5, 0, 0, 5, 5, 0, 0], [0, 5, 5, 0, 0, 5, 5, 2, 5, 0, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0], [0, 0, 5, 0, 5, 0, 5, 8, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 0], [0, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5], [5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5]]}, {"input": [[0, 0, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0], [0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 5], [0, 5, 5, 5, 0, 5, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0], [5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0], [5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 2, 5, 0, 0, 5, 0, 5, 5], [5, 0, 5, 0, 0, 5, 5, 0, 5, 2, 2, 5, 2, 2, 5, 5, 0, 5, 0], [0, 5, 0, 5, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 5, 5], [5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 2, 5, 5, 5, 0, 0, 0, 0], [5, 2, 2, 5, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0], [5, 2, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5], [0, 2, 5, 0, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0], [5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 0, 5], [0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 0, 5, 5, 5, 5, 0, 0, 0], [5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 0, 0], [5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5], [0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 5]], "output": [[0, 0, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0], [0, 0, 5, 5, 5, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 5], [0, 5, 5, 5, 0, 5, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0], [5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0], [5, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 8, 5, 0, 0, 0, 5, 0, 0], [5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 2, 5, 0, 0, 5, 0, 5, 5], [5, 0, 5, 0, 0, 5, 5, 0, 5, 2, 2, 8, 2, 2, 5, 5, 0, 5, 0], [0, 8, 0, 5, 5, 5, 5, 5, 0, 5, 0, 8, 5, 5, 5, 0, 5, 5, 5], [5, 8, 5, 0, 5, 5, 5, 5, 0, 0, 5, 2, 5, 5, 5, 0, 0, 0, 0], [8, 2, 2, 8, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0], [5, 2, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 5], [0, 2, 5, 0, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 0], [0, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0], [5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 5, 5, 5, 0, 5], [0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 0, 5, 5, 5, 5, 0, 0, 0], [5, 5, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 0, 0], [5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5], [0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 5]]}, {"input": [[0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5], [5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5], [5, 0, 5, 0, 0, 5, 5, 0, 2, 0, 5, 0], [5, 5, 0, 0, 5, 0, 5, 0, 2, 5, 0, 5], [5, 0, 0, 5, 5, 5, 2, 5, 2, 2, 2, 0], [5, 5, 5, 0, 5, 5, 0, 5, 2, 0, 0, 5], [5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 0, 0], [5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 5, 0, 0, 0, 0, 5, 0, 0], [5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0], [5, 0, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5]], "output": [[0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5], [5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5], [5, 0, 5, 0, 0, 5, 5, 0, 2, 0, 5, 0], [5, 5, 0, 0, 5, 0, 5, 0, 2, 5, 0, 5], [5, 0, 0, 5, 5, 5, 2, 8, 2, 2, 2, 0], [5, 5, 5, 0, 5, 5, 0, 5, 2, 0, 0, 5], [5, 5, 5, 0, 5, 0, 0, 5, 8, 0, 0, 0], [5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0], [0, 5, 5, 0, 5, 0, 0, 0, 0, 5, 0, 0], [5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0], [5, 0, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5]]}], "test": [{"input": [[0, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0, 0, 5, 5, 0], [0, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5], [0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5], [0, 0, 5, 5, 0, 5, 5, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0], [0, 5, 0, 5, 2, 2, 5, 2, 2, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0], [0, 0, 0, 5, 0, 5, 2, 5, 5, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0], [5, 5, 0, 0, 5, 5, 2, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 2, 5, 2, 5, 5, 0, 0, 5, 5, 5, 0], [0, 0, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 2, 5, 0, 5, 0, 0, 5, 5, 0], [0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0, 0, 5, 5], [5, 5, 0, 0, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 5], [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 5], [5, 5, 0, 5, 5, 2, 2, 2, 5, 5, 5, 0, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0], [5, 0, 0, 0, 5, 2, 5, 0, 5, 0, 5, 0, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 0, 2, 0, 5, 5, 0, 0, 2, 2, 2, 2, 2, 5, 0, 5, 0, 5, 5], [5, 0, 5, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 5]], "output": [[0, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0, 0, 5, 5, 0], [0, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5], [0, 0, 0, 0, 5, 5, 8, 0, 0, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 5], [0, 0, 5, 5, 0, 5, 8, 5, 0, 5, 0, 5, 0, 5, 0, 5, 5, 0, 5, 5, 5, 0], [0, 5, 0, 5, 2, 2, 8, 2, 2, 5, 0, 0, 5, 0, 5, 5, 5, 0, 0, 5, 5, 0], [0, 0, 0, 5, 0, 5, 2, 5, 5, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0], [5, 5, 0, 0, 5, 5, 2, 0, 5, 5, 0, 0, 0, 8, 0, 0, 0, 5, 5, 5, 5, 5], [5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 8, 0, 5, 0, 0, 5, 0, 5, 0], [5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 5, 2, 8, 2, 8, 8, 0, 0, 5, 5, 5, 0], [0, 0, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 8, 0, 5, 5, 5, 0, 0, 0, 0], [0, 0, 0, 5, 5, 5, 0, 5, 0, 5, 0, 5, 5, 2, 5, 0, 5, 0, 0, 5, 5, 0], [0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 5, 5, 0, 0, 0, 5, 5], [5, 5, 0, 0, 5, 8, 5, 0, 0, 5, 5, 0, 5, 0, 5, 5, 0, 0, 5, 5, 0, 5], [0, 0, 5, 5, 5, 8, 5, 5, 5, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 0, 5], [5, 5, 0, 8, 8, 2, 2, 2, 5, 5, 5, 0, 5, 8, 5, 0, 5, 0, 0, 5, 5, 0], [5, 0, 0, 0, 5, 2, 5, 0, 5, 0, 5, 0, 5, 8, 5, 5, 0, 0, 0, 0, 5, 5], [5, 5, 5, 0, 0, 2, 0, 5, 5, 0, 0, 2, 2, 2, 2, 2, 5, 0, 5, 0, 5, 5], [5, 0, 5, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 8, 0, 5, 5, 5, 0, 5, 5, 0], [5, 5, 5, 5, 5, 0, 5, 0, 5, 5, 5, 5, 0, 8, 0, 0, 5, 5, 0, 5, 0, 5]]}]}