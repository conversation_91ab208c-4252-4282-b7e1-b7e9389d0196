{"train": [{"input": [[0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1], [1, 0, 0, 0, 3, 0, 0, 1, 0, 0, 0, 0], [0, 0, 1, 3, 1, 3, 0, 0, 0, 0, 0, 0], [1, 0, 3, 1, 1, 1, 3, 0, 1, 0, 1, 0], [1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 3, 0, 3, 0, 1, 0, 1, 0], [0, 0, 0, 3, 1, 0, 1, 1, 0, 0, 1, 0], [1, 0, 3, 1, 0, 1, 0, 0, 1, 0, 0, 1], [0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1]], "output": [[0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1], [1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1], [1, 0, 0, 0, 3, 0, 0, 1, 0, 0, 0, 0], [0, 0, 1, 3, 8, 3, 0, 0, 0, 0, 0, 0], [1, 0, 3, 8, 8, 8, 3, 0, 1, 0, 1, 0], [1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0], [0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 3, 8, 3, 0, 1, 0, 1, 0], [0, 0, 0, 3, 8, 8, 8, 8, 0, 0, 1, 0], [1, 0, 3, 8, 8, 8, 8, 8, 8, 0, 0, 1], [0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1]]}, {"input": [[1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0], [1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1], [1, 0, 0, 0, 0, 0, 3, 0, 1, 0, 1, 1], [1, 0, 0, 1, 0, 3, 0, 1, 1, 1, 1, 1], [1, 1, 1, 0, 3, 1, 0, 0, 1, 0, 1, 1], [0, 1, 1, 3, 1, 1, 1, 1, 0, 1, 0, 0], [0, 1, 0, 0, 3, 1, 0, 0, 1, 0, 0, 1], [1, 1, 1, 1, 1, 3, 0, 0, 1, 0, 0, 1], [0, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 1], [1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0], [1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0], [1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0], [0, 1, 0, 0, 3, 1, 1, 0, 0, 0, 0, 1], [0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1], [0, 1, 3, 0, 0, 0, 3, 0, 0, 1, 1, 1]], "output": [[1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0], [1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1], [1, 0, 0, 0, 0, 0, 3, 0, 1, 0, 1, 1], [1, 0, 0, 1, 0, 3, 8, 1, 1, 1, 1, 1], [1, 1, 1, 0, 3, 8, 8, 0, 1, 0, 1, 1], [0, 1, 1, 3, 8, 8, 8, 1, 0, 1, 0, 0], [0, 1, 0, 0, 3, 8, 8, 0, 1, 0, 0, 1], [1, 1, 1, 1, 1, 3, 8, 0, 1, 0, 0, 1], [0, 0, 1, 0, 0, 1, 3, 0, 1, 0, 1, 1], [1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0], [1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0], [1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0], [0, 1, 0, 0, 3, 1, 1, 0, 0, 0, 0, 1], [0, 1, 0, 8, 8, 8, 1, 1, 0, 0, 1, 1], [0, 1, 3, 8, 8, 8, 3, 0, 0, 1, 1, 1]]}, {"input": [[1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1], [1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 0, 0, 3, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 3, 1, 3, 0, 1, 1, 0, 0, 1, 1, 1, 0], [0, 1, 0, 3, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0], [1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1], [0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1], [1, 0, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0], [1, 0, 0, 1, 0, 0, 1, 1, 3, 0, 0, 0, 3, 1, 1, 0], [0, 1, 0, 1, 1, 0, 1, 3, 1, 1, 1, 0, 0, 3, 1, 3], [1, 0, 0, 0, 0, 1, 3, 0, 0, 0, 0, 1, 0, 0, 3, 0], [0, 0, 1, 0, 1, 3, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], [1, 1, 0, 1, 1, 1, 3, 0, 0, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 1, 3, 1, 0, 1, 0, 1, 1, 1]], "output": [[1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1], [1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 0, 0, 3, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 3, 8, 3, 0, 1, 1, 0, 0, 1, 1, 1, 0], [0, 1, 0, 3, 8, 8, 8, 8, 0, 1, 0, 1, 1, 1, 0, 0], [1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1], [0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1], [1, 0, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0], [1, 0, 0, 1, 0, 0, 1, 1, 3, 0, 0, 0, 3, 8, 8, 8], [0, 1, 0, 1, 1, 0, 1, 3, 8, 1, 1, 0, 0, 3, 8, 3], [1, 0, 0, 0, 0, 1, 3, 8, 8, 0, 0, 1, 0, 0, 3, 0], [0, 0, 1, 0, 1, 3, 8, 8, 8, 1, 0, 0, 0, 0, 0, 0], [1, 1, 0, 1, 1, 1, 3, 8, 8, 1, 1, 1, 1, 0, 0, 0], [0, 0, 0, 1, 1, 1, 1, 8, 8, 0, 0, 0, 0, 1, 1, 1], [0, 1, 1, 1, 0, 0, 1, 1, 3, 1, 0, 1, 0, 1, 1, 1]]}], "test": [{"input": [[0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 3, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0], [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0], [0, 3, 0, 1, 0, 3, 1, 0, 1, 1, 0, 1, 0, 3, 1, 0, 0, 3], [0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 3, 0, 3, 0], [0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 3, 1, 0], [0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0], [1, 3, 0, 0, 1, 1, 0, 1, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 3, 0, 0, 0, 1, 1, 3, 0, 1, 1, 0, 1, 0, 0, 1, 0], [0, 0, 0, 3, 0, 1, 1, 3, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0], [0, 0, 3, 1, 1, 0, 3, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0], [0, 1, 1, 0, 0, 1, 1, 3, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0], [1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1]], "output": [[0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 3, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0], [0, 0, 8, 8, 8, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0], [0, 3, 8, 8, 8, 3, 1, 0, 1, 1, 0, 1, 0, 3, 8, 8, 8, 3], [0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 3, 8, 3, 0], [0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 3, 1, 0], [0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0], [1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0], [1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0], [1, 3, 0, 0, 1, 1, 0, 1, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0], [0, 8, 3, 0, 0, 0, 1, 1, 3, 8, 1, 1, 0, 1, 0, 0, 1, 0], [0, 8, 8, 3, 0, 1, 1, 3, 8, 8, 0, 0, 1, 1, 1, 0, 1, 0], [0, 8, 3, 1, 1, 0, 3, 8, 8, 8, 0, 1, 0, 1, 1, 0, 0, 0], [0, 8, 1, 0, 0, 1, 1, 3, 8, 8, 1, 1, 1, 1, 1, 0, 1, 0], [1, 1, 1, 1, 1, 0, 1, 0, 8, 8, 0, 0, 0, 0, 0, 1, 0, 1]]}]}