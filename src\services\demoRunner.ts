import { ARCPuzzle, De<PERSON>M<PERSON>, DemoSession, Demo<PERSON><PERSON>ult, PuzzleAnalysis } from '../types';
import { aiProviderService } from './aiProviderService';
import { solutionValidator } from './solutionValidator';
import { arcAnalyzer } from './arcAnalyzer';
import { dataStorage } from './dataStorage';

export class DemoRunner {
  private static instance: DemoRunner;
  private currentSession: DemoSession | null = null;
  private isRunning = false;
  private isPaused = false;
  private onProgressCallback?: (current: number, total: number, session: DemoSession) => void;

  private constructor() {}

  static getInstance(): DemoRunner {
    if (!DemoRunner.instance) {
      DemoRunner.instance = new DemoRunner();
    }
    return DemoRunner.instance;
  }

  setProgressCallback(callback: (current: number, total: number, session: DemoSession) => void): void {
    this.onProgressCallback = callback;
  }

  async startDemo(config: De<PERSON>M<PERSON>, puzzles: ARCPuzzle[]): Promise<void> {
    if (this.isRunning) {
      throw new Error('Une démo est déjà en cours');
    }

    // Vérifier que le fournisseur est configuré
    if (!aiProviderService.hasApiKey(config.selectedProvider)) {
      throw new Error(`Clé API requise pour ${config.selectedProvider}`);
    }

    this.isRunning = true;
    this.isPaused = false;

    this.currentSession = {
      id: Date.now().toString(),
      startTime: new Date(),
      puzzlesProcessed: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      totalCost: 0,
      results: [],
    };

    try {
      for (let i = 0; i < puzzles.length && this.isRunning; i++) {
        if (this.isPaused) {
          await this.waitForResume();
        }

        const puzzle = puzzles[i];
        console.log(`Traitement du puzzle ${i + 1}/${puzzles.length}: ${puzzle.id}`);

        // Notifier le progrès
        this.onProgressCallback?.(i, puzzles.length, this.currentSession);

        try {
          const result = await this.processPuzzleAutomatically(puzzle, config);
          this.currentSession.results.push(result);
          this.updateSessionStats();

          // Notifier le progrès après traitement
          this.onProgressCallback?.(i + 1, puzzles.length, this.currentSession);

          // Attendre avant le puzzle suivant
          if (config.autoAdvanceDelay > 0 && i < puzzles.length - 1) {
            await this.delay(config.autoAdvanceDelay * 1000);
          }
        } catch (error) {
          console.error(`Erreur sur puzzle ${puzzle.id}:`, error);

          // Ajouter un résultat d'échec
          const failureResult: DemoResult = {
            puzzleId: puzzle.id,
            success: false,
            responseTime: 0,
            cost: 0,
            accuracy: 0,
            errorCount: 0,
            attempts: 1,
          };
          this.currentSession.results.push(failureResult);
          this.updateSessionStats();

          if (config.stopOnError) {
            this.stopDemo();
            break;
          }
        }
      }
    } finally {
      this.finalizeDemoSession();
    }
  }

  stopDemo(): void {
    this.isRunning = false;
    this.isPaused = false;
  }

  pauseDemo(): void {
    this.isPaused = true;
  }

  resumeDemo(): void {
    this.isPaused = false;
  }

  private async waitForResume(): Promise<void> {
    return new Promise((resolve) => {
      const checkResume = () => {
        if (!this.isPaused || !this.isRunning) {
          resolve();
        } else {
          setTimeout(checkResume, 100);
        }
      };
      checkResume();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getCurrentSession(): DemoSession | null {
    return this.currentSession;
  }

  isRunningDemo(): boolean {
    return this.isRunning;
  }

  isPausedDemo(): boolean {
    return this.isPaused;
  }

  private async processPuzzleAutomatically(puzzle: ARCPuzzle, config: DemoMode): Promise<DemoResult> {
    const startTime = Date.now();
    let attempts = 0;
    let success = false;
    let accuracy = 0;
    let errorCount = 0;
    let totalCost = 0;

    // 1. Analyser le puzzle
    let analysis: PuzzleAnalysis;
    try {
      analysis = await arcAnalyzer.analyzePuzzle(puzzle);
    } catch (error) {
      console.warn('Erreur lors de l\'analyse, utilisation d\'une analyse par défaut:', error);
      analysis = this.createDefaultAnalysis();
    }

    // 2. Tentatives de résolution avec retry
    const maxAttempts = 3;
    while (attempts < maxAttempts && !success && this.isRunning) {
      attempts++;

      try {
        // Générer le prompt
        const prompt = this.generateAutoPrompt(puzzle, analysis, attempts);

        // Envoyer à l'IA
        const response = await aiProviderService.sendRequest({
          provider: config.selectedProvider,
          model: config.selectedModel,
          prompt,
          temperature: 0.7,
          maxTokens: 2000,
          timeout: 30000,
        });

        totalCost += response.cost;

        // Parser la solution
        const proposedSolution = solutionValidator.parseSolutionFromText(response.content);

        if (proposedSolution && puzzle.test[0].output) {
          // Valider la solution
          const validation = solutionValidator.validateSolution(
            proposedSolution,
            puzzle.test[0].output
          );

          success = validation.isValid;
          accuracy = validation.accuracy;
          errorCount = validation.errorGrid ?
            validation.errorGrid.flat().filter(cell => !cell).length : 0;

          if (success) {
            console.log(`✅ Puzzle ${puzzle.id} résolu en ${attempts} tentative(s)`);
            break;
          } else {
            console.log(`❌ Tentative ${attempts} échouée pour ${puzzle.id}: ${validation.feedback}`);
          }
        } else {
          console.log(`❌ Impossible de parser la solution pour ${puzzle.id}`);
        }

      } catch (error) {
        console.warn(`Tentative ${attempts} échouée pour ${puzzle.id}:`, error);

        // Si c'est une erreur de rate limit, attendre plus longtemps
        if ((error as Error).message.includes('rate limit')) {
          await this.delay(60000); // Attendre 1 minute
        }
      }
    }

    const responseTime = Date.now() - startTime;

    return {
      puzzleId: puzzle.id,
      success,
      responseTime,
      cost: totalCost,
      accuracy,
      errorCount,
      attempts,
    };
  }

  private updateSessionStats(): void {
    if (!this.currentSession) return;

    this.currentSession.puzzlesProcessed = this.currentSession.results.length;
    this.currentSession.successCount = this.currentSession.results.filter(r => r.success).length;
    this.currentSession.failureCount = this.currentSession.results.filter(r => !r.success).length;

    const totalTime = this.currentSession.results.reduce((sum, result) => sum + result.responseTime, 0);
    this.currentSession.averageResponseTime = totalTime / this.currentSession.results.length;

    const totalCost = this.currentSession.results.reduce((sum, result) => sum + result.cost, 0);
    this.currentSession.totalCost = totalCost;
  }

  private finalizeDemoSession(): void {
    if (this.currentSession) {
      this.currentSession.endTime = new Date();

      // Sauvegarder la session
      try {
        dataStorage.saveSession(this.currentSession);
        console.log('💾 Session sauvegardée:', this.currentSession.id);
      } catch (error) {
        console.error('Erreur lors de la sauvegarde de la session:', error);
      }

      console.log('🏁 Demo terminée:', {
        puzzlesProcessed: this.currentSession.puzzlesProcessed,
        successRate: `${((this.currentSession.successCount / this.currentSession.puzzlesProcessed) * 100).toFixed(1)}%`,
        totalCost: `$${this.currentSession.totalCost.toFixed(4)}`,
        duration: `${Math.round((this.currentSession.endTime.getTime() - this.currentSession.startTime.getTime()) / 1000)}s`
      });
    }
    this.isRunning = false;
    this.isPaused = false;
  }

  private generateAutoPrompt(puzzle: ARCPuzzle, analysis: PuzzleAnalysis, attempt: number): string {
    let prompt = `Résolvez ce puzzle ARC étape par étape.

PUZZLE ID: ${puzzle.id}
TENTATIVE: ${attempt}/3

EXEMPLES D'ENTRAÎNEMENT:`;

    puzzle.train.forEach((example, i) => {
      prompt += `\n\nExemple ${i + 1}:
INPUT (${example.input.width}×${example.input.height}):
${example.input.grid.map(row => row.join(' ')).join('\n')}

OUTPUT (${example.output.width}×${example.output.height}):
${example.output.grid.map(row => row.join(' ')).join('\n')}`;
    });

    prompt += `\n\nTEST À RÉSOUDRE:
INPUT (${puzzle.test[0].input.width}×${puzzle.test[0].input.height}):
${puzzle.test[0].input.grid.map(row => row.join(' ')).join('\n')}

ANALYSE DISPONIBLE:
- Couleurs input: ${analysis.colors?.input?.present_colors?.join(', ') || 'N/A'}
- Couleurs output: ${analysis.colors?.output?.present_colors?.join(', ') || 'N/A'}
- Objets détectés: ${analysis.objects?.input?.object_statistics?.total_objects || 0}
- Transformation: ${analysis.transformations?.dimension_change?.transformation_type || 'N/A'}

INSTRUCTIONS:
1. Analysez les patterns dans les exemples
2. Identifiez la règle de transformation
3. Appliquez cette règle au test
4. Retournez UNIQUEMENT la grille solution au format JSON

RÉPONSE (JSON uniquement):`;

    if (attempt > 1) {
      prompt += `\n\nATTENTION: Tentative ${attempt}. Réanalysez attentivement les patterns.`;
    }

    return prompt;
  }

  private createDefaultAnalysis(): PuzzleAnalysis {
    return {
      grid_info: {
        input: { width: 3, height: 3, colors: [0, 1, 2], total_cells: 9 },
        output: { width: 3, height: 3, colors: [0, 1, 2], total_cells: 9 }
      },
      objects: {
        input: {
          table_analysis: { probable_background: 0, background_confidence: 0.9, non_background_colors: [1, 2], table_coverage: 0.7, noise_pixels: 0 },
          detected_objects: [],
          anchor_analysis: { possible_anchor_points: [] },
          object_statistics: { total_objects: 2, objects_by_color: {}, objects_by_shape: {}, table_occupancy: 0.3 }
        },
        output: {
          table_analysis: { probable_background: 0, background_confidence: 0.9, non_background_colors: [1, 2], table_coverage: 0.7, noise_pixels: 0 },
          detected_objects: [],
          anchor_analysis: { possible_anchor_points: [] },
          object_statistics: { total_objects: 2, objects_by_color: {}, objects_by_shape: {}, table_occupancy: 0.3 }
        }
      },
      patterns: {
        input: { detected_patterns: [], pattern_statistics: { total_patterns: 0, pattern_density: 0, pattern_coverage: 0 }, complexity_analysis: { pattern_complexity: 'simple', regularity_index: 0.5, predictability: 0.7 } },
        output: { detected_patterns: [], pattern_statistics: { total_patterns: 0, pattern_density: 0, pattern_coverage: 0 }, complexity_analysis: { pattern_complexity: 'simple', regularity_index: 0.5, predictability: 0.7 } }
      },
      transformations: {
        dimension_change: { same_dimensions: true, width_change: 0, height_change: 0, transformation_type: 'identity' },
        object_changes: [],
        pattern_changes: []
      },
      symmetries: {
        input: { horizontal: false, vertical: false, diagonal_main: false, diagonal_anti: false, symmetry_count: 0, symmetry_axes: [] },
        output: { horizontal: false, vertical: false, diagonal_main: false, diagonal_anti: false, symmetry_count: 0, symmetry_axes: [] }
      },
      complexity: {
        input: { pattern_complexity: 'simple', regularity_index: 0.5, predictability: 0.7 },
        output: { pattern_complexity: 'simple', regularity_index: 0.5, predictability: 0.7 },
        transformation_complexity: 'medium'
      },
      colors: {
        input: { present_colors: [0, 1, 2], dominant_color: 0, background_color: 0, non_background_colors: [1, 2], color_count: 3, color_distribution: {} },
        output: { present_colors: [0, 1, 2], dominant_color: 0, background_color: 0, non_background_colors: [1, 2], color_count: 3, color_distribution: {} }
      },
      spatial_relations: {
        input: { object_density: 0.3, background_ratio: 0.7, filled_ratio: 0.3, center_of_mass: [1.5, 1.5], spatial_distribution: 'uniform' },
        output: { object_density: 0.3, background_ratio: 0.7, filled_ratio: 0.3, center_of_mass: [1.5, 1.5], spatial_distribution: 'uniform' }
      },
      line_uniformity: {
        input: { uniform_rows: [], uniform_columns: [], uniform_main_diagonals: [], uniform_anti_diagonals: [], line_distribution: { rows: 0, columns: 0, main_diagonals: 0, anti_diagonals: 0 }, color_frequency_in_lines: {} },
        output: { uniform_rows: [], uniform_columns: [], uniform_main_diagonals: [], uniform_anti_diagonals: [], line_distribution: { rows: 0, columns: 0, main_diagonals: 0, anti_diagonals: 0 }, color_frequency_in_lines: {} }
      },
      diff_analysis: null,
      enhanced_objects: { input: [], output: [] },
      repeating_patterns: { input: [], output: [] }
    };
  }
}

export const demoRunner = DemoRunner.getInstance();