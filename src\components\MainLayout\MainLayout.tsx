import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  IconButton,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Paper,
  Divider,
  Stack,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Brightness4 as ThemeIcon,
  Settings as SettingsIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Shuffle as RandomIcon,
  PlayArrow as DemoIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { setTheme } from '../../store/slices/settingsSlice';
import {
  nextPuzzle,
  previousPuzzle,
  randomPuzzle,
  setSelectedSubset,
  setPuzzleList,
  setCurrentPuzzleIndex,
  setLoading,
  setError,
  toggleShowValues,
} from '../../store/slices/puzzleSlice';
import { arcDataLoader } from '../../services/arcDataLoader';
import ApiKeyManager from '../ApiKeyManager/ApiKeyManager';
import PuzzleViewer from '../PuzzleViewer/PuzzleViewer';
import AnalysisPanel from '../AnalysisPanel/AnalysisPanel';
import ChatInterface from '../ChatInterface/ChatInterface';
import PromptTemplateEditor from '../PromptTemplateEditor/PromptTemplateEditor';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ height: '100%', overflow: 'hidden' }}>
    {value === index && <Box sx={{ height: '100%', overflow: 'hidden' }}>{children}</Box>}
  </div>
);

const MainLayout: React.FC = () => {
  const dispatch = useDispatch();
  const { theme } = useSelector((state: RootState) => state.settings);
  const { currentPuzzle, puzzleList, selectedSubset, isLoading, error, showValues } = useSelector((state: RootState) => state.puzzle);

  const [showApiKeyManager, setShowApiKeyManager] = useState(false);
  const [rightTabValue, setRightTabValue] = useState(0);
  const [showPuzzleSelector, setShowPuzzleSelector] = useState(false);
  const [puzzleSearchTerm, setPuzzleSearchTerm] = useState('');

  // Charger les puzzles au démarrage et quand le subset change
  useEffect(() => {
    loadPuzzles();
  }, [selectedSubset]);

  const loadPuzzles = async () => {
    dispatch(setLoading(true));
    dispatch(setError(null));

    try {
      const puzzles = await arcDataLoader.loadAllPuzzles(
        selectedSubset,
        (loaded, total) => {
          console.log(`Chargement: ${loaded}/${total} puzzles`);
        }
      );

      dispatch(setPuzzleList(puzzles));

      // Sélectionner le premier puzzle si disponible
      if (puzzles.length > 0) {
        dispatch(setCurrentPuzzleIndex(0));
      }

    } catch (error) {
      console.error('Erreur lors du chargement des puzzles:', error);
      dispatch(setError((error as Error).message));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const cycleTheme = () => {
    const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    dispatch(setTheme(themes[nextIndex]));
  };

  const handleSubsetChange = (newSubset: string) => {
    dispatch(setSelectedSubset(newSubset as 'training' | 'evaluation' | 'both'));
  };

  const handlePuzzleSelect = (puzzleId: string) => {
    if (!puzzleId || !puzzleList) return;
    
    const puzzleIndex = puzzleList.findIndex(puzzle => puzzle.id === puzzleId);
    if (puzzleIndex !== -1) {
      dispatch(setCurrentPuzzleIndex(puzzleIndex));
      setShowPuzzleSelector(false);
      setPuzzleSearchTerm('');
    }
  };

  const filteredPuzzles = puzzleList.filter(puzzle => 
    puzzle.id.toLowerCase().includes(puzzleSearchTerm.toLowerCase())
  );

  const currentIndex = puzzleList.findIndex(p => p.id === currentPuzzle?.id);
  const totalPuzzles = puzzleList.length;

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            ARC AGI Testing
          </Typography>

          {/* Dataset Selection */}
          <FormControl size="small" sx={{ minWidth: 120, mr: 2 }}>
            <InputLabel>Dataset</InputLabel>
            <Select
              value={selectedSubset}
              label="Dataset"
              onChange={(e) => handleSubsetChange(e.target.value)}
              disabled={isLoading}
            >
              <MenuItem value="training">Training</MenuItem>
              <MenuItem value="evaluation">Evaluation</MenuItem>
              <MenuItem value="both">Both</MenuItem>
            </Select>
          </FormControl>

          {/* Loading indicator */}
          {isLoading && (
            <CircularProgress size={20} sx={{ mr: 2 }} />
          )}

          {/* Puzzle Navigation */}
          <Stack direction="row" spacing={1} alignItems="center" sx={{ mr: 2 }}>
            <IconButton
              color="inherit"
              onClick={() => dispatch(previousPuzzle())}
              disabled={!currentPuzzle || currentIndex <= 0}
            >
              <PrevIcon />
            </IconButton>
            
            <Button
              variant="outlined"
              size="small"
              onClick={() => setShowPuzzleSelector(true)}
              disabled={!currentPuzzle || totalPuzzles === 0}
              sx={{ 
                color: 'inherit', 
                borderColor: 'inherit',
                minWidth: '100px',
                mr: 1
              }}
            >
              {currentPuzzle?.id || 'Puzzle'}
            </Button>
            
            <Chip
              label={`${currentIndex + 1} / ${totalPuzzles}`}
              variant="outlined"
              sx={{ color: 'inherit', borderColor: 'inherit' }}
            />
            
            <IconButton
              color="inherit"
              onClick={() => dispatch(nextPuzzle())}
              disabled={!currentPuzzle || currentIndex >= totalPuzzles - 1}
            >
              <NextIcon />
            </IconButton>
            
            <IconButton
              color="inherit"
              onClick={() => dispatch(randomPuzzle())}
              disabled={!currentPuzzle}
            >
              <RandomIcon />
            </IconButton>
          </Stack>

          {/* Valeurs Checkbox */}
          <FormControlLabel
            control={
              <Checkbox
                checked={showValues}
                onChange={() => dispatch(toggleShowValues())}
                size="small"
                sx={{ color: 'inherit' }}
              />
            }
            label="Valeurs"
            sx={{ color: 'inherit', mr: 2 }}
          />

          {/* Demo Button */}
          <Button
            color="inherit"
            startIcon={<DemoIcon />}
            sx={{ mr: 2 }}
          >
            Demo
          </Button>

          {/* Theme Toggle */}
          <IconButton color="inherit" onClick={cycleTheme} title={`Thème: ${theme}`}>
            <ThemeIcon />
          </IconButton>

          {/* Settings */}
          <IconButton 
            color="inherit" 
            onClick={() => setShowApiKeyManager(true)}
            title="Configuration des clés API"
          >
            <SettingsIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ m: 1 }} onClose={() => dispatch(setError(null))}>
          {error}
        </Alert>
      )}

      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Left Panel - Puzzle Viewer */}
        <Box sx={{ width: '50%', display: 'flex', flexDirection: 'column' }}>
          <Paper sx={{ m: 1, flex: 1, overflow: 'auto' }}>
            <PuzzleViewer />
          </Paper>
        </Box>

        <Divider orientation="vertical" flexItem />

        {/* Right Panel - Tabs */}
        <Box sx={{ width: '50%', display: 'flex', flexDirection: 'column' }}>
          <Paper sx={{ m: 1, flex: 1, display: 'flex', flexDirection: 'column' }}>
            {/* Tabs Header */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={rightTabValue}
                onChange={(_, newValue) => setRightTabValue(newValue)}
                variant="fullWidth"
              >
                <Tab label="Analyse" />
                <Tab label="Chat" />
                <Tab label="Prompt" />
              </Tabs>
            </Box>

            {/* Tabs Content */}
            <Box sx={{ flex: 1, overflow: 'hidden' }}>
              <TabPanel value={rightTabValue} index={0}>
                <AnalysisPanel />
              </TabPanel>
              <TabPanel value={rightTabValue} index={1}>
                <ChatInterface />
              </TabPanel>
              <TabPanel value={rightTabValue} index={2}>
                <PromptTemplateEditor />
              </TabPanel>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* API Key Manager Dialog */}
      <ApiKeyManager
        open={showApiKeyManager}
        onClose={() => setShowApiKeyManager(false)}
      />

      {/* Puzzle Selector Dialog */}
      <Dialog open={showPuzzleSelector} onClose={() => setShowPuzzleSelector(false)} maxWidth="md" fullWidth>
        <DialogTitle>Sélectionner un puzzle</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Rechercher par ID"
            fullWidth
            variant="outlined"
            value={puzzleSearchTerm}
            onChange={(e) => setPuzzleSearchTerm(e.target.value)}
            sx={{ mb: 2 }}
          />
          <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
            <Stack spacing={1}>
              {filteredPuzzles.slice(0, 50).map((puzzle) => (
                <Button
                  key={puzzle.id}
                  variant={puzzle.id === currentPuzzle?.id ? "contained" : "outlined"}
                  onClick={() => handlePuzzleSelect(puzzle.id)}
                  sx={{ justifyContent: 'flex-start' }}
                >
                  {puzzle.id} {puzzle.id === currentPuzzle?.id && '(actuel)'}
                </Button>
              ))}
              {filteredPuzzles.length > 50 && (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
                  {filteredPuzzles.length - 50} autres puzzles... Affinez votre recherche.
                </Typography>
              )}
            </Stack>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPuzzleSelector(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MainLayout;
