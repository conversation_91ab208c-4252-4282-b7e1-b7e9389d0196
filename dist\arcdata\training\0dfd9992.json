{"test": [{"input": [[9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 6, 2, 9, 9, 2, 0, 0, 0, 0, 0, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 0, 0, 0, 0, 0, 1, 8, 8, 1, 5, 2, 1], [6, 3, 2, 3, 6, 2, 9, 9, 2, 0, 0, 0, 0, 0, 2, 9, 9, 2, 6, 3, 2], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [5, 2, 1, 2, 0, 0, 0, 0, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [9, 6, 5, 6, 0, 0, 0, 0, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 0, 0, 0, 0, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 0, 0, 9, 2, 6, 3, 2], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 0, 0, 3, 5, 9, 6, 5], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 0, 0, 8, 1, 5, 2, 1], [0, 0, 8, 9, 0, 0, 0, 6, 8, 3, 9, 8, 9, 3, 0, 0, 6, 8, 3, 9, 8], [0, 0, 8, 9, 0, 0, 0, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [5, 2, 1, 2, 0, 0, 0, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [9, 6, 5, 6, 0, 0, 0, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 0, 0, 0, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1]], "output": [[9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8, 9, 3, 8, 6, 6, 8, 3, 9, 8], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1], [9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5, 6, 9, 5, 3, 3, 5, 9, 6, 5], [6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2, 3, 6, 2, 9, 9, 2, 6, 3, 2], [5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1, 2, 5, 1, 8, 8, 1, 5, 2, 1]]}], "train": [{"input": [[3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 0, 0, 0, 0, 0, 5], [2, 5, 4, 5, 2, 1, 2, 5, 0, 0, 2, 1, 2, 5, 4, 0, 0, 0, 0, 0, 4], [3, 6, 5, 6, 3, 2, 3, 0, 0, 0, 0, 2, 3, 6, 5, 0, 0, 0, 0, 0, 5], [6, 3, 2, 3, 6, 5, 6, 0, 0, 0, 0, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 0, 0, 0, 0, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 0, 0, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 0, 0, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 0, 0, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 0, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 5, 0, 0, 0, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 6, 0, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1]], "output": [[3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1]]}, {"input": [[2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [2, 7, 7, 2, 0, 0, 0, 0, 0, 7, 2, 6, 5, 6, 2, 7, 0, 0, 6, 5, 6], [6, 4, 4, 6, 0, 0, 0, 0, 0, 4, 6, 3, 2, 3, 6, 4, 0, 0, 3, 2, 3], [5, 3, 3, 5, 0, 0, 0, 0, 0, 3, 5, 2, 1, 2, 5, 3, 0, 0, 2, 1, 2], [6, 4, 4, 6, 0, 0, 0, 0, 0, 4, 6, 3, 2, 3, 6, 4, 0, 0, 0, 0, 3], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 0, 0, 0, 6], [0, 0, 0, 0, 0, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 0, 0, 0, 4], [0, 0, 0, 0, 0, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 0, 0, 0, 4], [0, 0, 0, 0, 0, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 0, 0, 3, 2, 3], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 0, 0, 6, 5, 6], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 0, 0, 4, 3, 4], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3]], "output": [[2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4, 7, 5, 5, 7, 4, 3, 4], [2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6, 2, 7, 7, 2, 6, 5, 6], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3], [5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2, 5, 3, 3, 5, 2, 1, 2], [6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3, 6, 4, 4, 6, 3, 2, 3]]}, {"input": [[3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [0, 0, 0, 0, 0, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [0, 0, 0, 0, 0, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 0, 0, 0, 2, 3, 6, 3], [2, 1, 2, 5, 0, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, 0, 0, 1, 2, 5, 2], [3, 2, 3, 6, 0, 0, 0, 0, 3, 0, 0, 0, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 0, 0, 0, 0, 6, 0, 0, 0, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 0, 0, 0, 0, 3, 0, 0, 0, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 0, 0, 0, 0, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 0, 0, 0, 0, 0, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 0, 0, 0, 0, 0, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 0, 0, 0, 0, 0, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 0, 0, 0, 0, 0, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 0, 0, 0, 0, 0, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3]], "output": [[3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2, 1, 2, 5, 2], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3], [6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6, 5, 6, 1, 6], [3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3, 2, 3, 6, 3]]}]}